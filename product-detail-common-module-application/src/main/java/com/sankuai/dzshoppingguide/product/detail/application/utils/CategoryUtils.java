package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.MassageThirdCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/6 10:18
 */
public class CategoryUtils {

    /**
     * 获取三级分类信息
     * @param productCategory
     * @return
     */
    public static int getThirdCategoryId(ProductCategory productCategory) {
        return Optional.ofNullable(productCategory)
                .map(ProductCategory::getProductThirdCategoryId)
                .orElse(0);
    }

    /**
     * 根据足疗行业三级分类信息获取对应的枚举
     * @param thirdCategoryId
     * @return
     */
    public static MassageThirdCategoryEnum massageThirdCategoryEnum(int thirdCategoryId) {
        return MassageThirdCategoryEnum.getByCategoryId(thirdCategoryId);
    }
}
