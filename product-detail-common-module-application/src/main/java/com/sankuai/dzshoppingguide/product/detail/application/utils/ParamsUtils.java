package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.vc.sdk.dp.client.ClientType;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/8 16:30
 */
public class ParamsUtils {

    public static int getClientType(final ClientTypeEnum clientType,
                                    final MobileOSTypeEnum mobileOSType) {
        boolean isIos = mobileOSType == MobileOSTypeEnum.IOS;
        switch (clientType) {
            case DP_APP: // 点评APP
                return isIos ? ClientType.DP_IPHONE_NATIVE : ClientType.DP_ANDROID_NATIVE;
            case DP_PC: // 点评PC
            case DP_M: // 点评M站
                return ClientType.M_WEB;
            case DP_XCX: // 点评小程序
                return ClientType.DP_WEIXIN_API;
            case DP_BAIDU_MAP_XCX:
                return isIos ? ClientType.DP_IPHONE_WEB : ClientType.DP_ANDROID_WEB;
            case DP_WX: // 点评微信
            case MT_WX: // 美团微信
                return ClientType.WEIXIN;
            case MT_WAI_MAI_APP:
            case MT_MAP:
            case MT_MAO_YAN_APP:
            case MT_APP: // 美团APP
                return isIos ? ClientType.MT_IPHONE_NATIVE : ClientType.MT_ANDROID_NATIVE;
            case MT_PC: // 美团PC
            case MT_I: // 美团I站
                return ClientType.I_WEB;
            case MT_XCX: // 美团小程序
                return ClientType.MT_WEIXIN_API;
            case MT_ZJ_XCX:
            case MT_WWJKZ_XCX:
            case MT_KUAI_SHOU_XCX:
            case MT_MAO_YAN_XCX:
                return isIos ? ClientType.MT_IPHONE_WEB : ClientType.MT_ANDROID_WEB;
            case KAI_DIAN_BAO:
                return ClientType.DP_ANDROID_NATIVE;
        }
        // TODO 兜底先传点评安卓native的枚举信息
        return ClientType.DP_ANDROID_NATIVE;
    }
}
