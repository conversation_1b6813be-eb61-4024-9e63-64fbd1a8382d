package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center;

import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/2/9 22:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryCenterAggregateReturnValue extends FetcherReturnValueDTO {

    private DealGroupDTO dealGroupDTO;

}
