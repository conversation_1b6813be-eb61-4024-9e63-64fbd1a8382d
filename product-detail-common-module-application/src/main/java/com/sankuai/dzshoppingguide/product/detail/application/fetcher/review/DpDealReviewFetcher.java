package com.sankuai.dzshoppingguide.product.detail.application.fetcher.review;

import com.dianping.cip.growth.mana.api.dto.response.UserManaDTO;
import com.dianping.cip.growth.mana.api.service.CipGrowthManaService;
import com.dianping.core.type.PageModel;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.review.professional.ReviewDataV2;
import com.dianping.reviewremote.remote.ReviewServiceV2;
import com.dianping.tuangou.dztg.bjwrapper.api.UserWrapperService;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtUserDto;
import com.dianping.tuangou.dztg.bjwrapper.api.enums.UserFieldsTypeEnum;
import com.dianping.ugc.dto.ExtParamV2;
import com.dianping.ugc.enums.TokenType;
import com.dianping.ugc.proxyService.remote.dp.DpReviewUserInfoService;
import com.dianping.ugc.proxyService.remote.dto.AnonymousUserInfo;
import com.dianping.ugc.proxyService.remote.dto.UserRequestPara;
import com.dianping.ugc.review.remote.dto.FilterParamV2;
import com.dianping.ugc.review.remote.dto.MTQueryResult;
import com.dianping.ugc.review.remote.dto.MTReviewData;
import com.dianping.ugc.review.remote.enums.ReviewSortType;
import com.dianping.ugc.review.remote.mt.MTReviewQueryServiceV2;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.userremote.base.service.UserService;
import com.dianping.vipremote.service.VIPUserService;
import com.dianping.vipremote.vo.UserInfoForAppVO;
import com.dp.arts.client.SearchService;
import com.dp.arts.client.request.Request;
import com.dp.arts.client.request.SortItem;
import com.dp.arts.client.request.StatItem;
import com.dp.arts.client.request.TermQuery;
import com.dp.arts.client.response.Record;
import com.dp.arts.client.response.Response;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.enums.ReviewFilterType;
import com.sankuai.dzshoppingguide.product.detail.application.enums.ThreadPoolNameEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ShopReviewHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ShopUrlUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.thread.ThreadPoolUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.idmapper.MapperCacheWrapper;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.ProductReviewDetail;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.ProductReviewTagFilter;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.UGCUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * @Author: litengfei04
 * @Date: 2025/2/9
 */
@Fetcher(
        previousLayerDependencies = {
                CommonModuleStarter.class,
                ShopIdMapperFetcher.class,
        },
        timeout = 500
)
@Slf4j
public class DpDealReviewFetcher extends NormalFetcherContext<ProductReviewReturnValue> {


    @Resource
    private SearchService shopReviewSearchServiceFuture;

    @Resource
    private UserWrapperService userWrapperService;

    @MdpPigeonClient(url = "ReviewService.ReviewService", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private ReviewServiceV2 reviewServiceFuture;

    @MdpPigeonClient(url = "http://service.dianping.com/cip/growth/mana/cipGrowthManaService_2.0.0", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private CipGrowthManaService cipGrowthServiceFuture;

    @MdpPigeonClient(url = "UGCProxyService.DpReviewUserInfoService", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private DpReviewUserInfoService dpReviewUserInfoServiceFuture;

    @MdpPigeonClient(url = "UGCReviewService.MTReviewQueryServiceV2", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private MTReviewQueryServiceV2 mtReviewQueryServiceFutureV2;

    @MdpPigeonClient(url = "http://service.dianping.com/vipService/vipUserService_1.0.0", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private VIPUserService vipUserServiceFuture;

    @MdpPigeonClient(url = "http://service.dianping.com/userBaseService/userService_2.0.0", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private UserService userBaseFuture;

    @Resource
    private MapperCacheWrapper mapperCacheWrapper;

    private static final int SHOP_PAGE_REVIEW_LENGTH = 150;

    private static final String DP_REVIEW_TAG_URL = "dianping://review?referid=%s&refertype=0&tagtype=%d&selecttagname=%s_%d";

    private static final String DP_REVIEW_LIST_URL = "dianping://review?referid=%s&refertype=0";

    @Override
    protected CompletableFuture<ProductReviewReturnValue> doFetch() {
        // 点评侧直接返回
        if (this.request.getClientTypeEnum().isMtClientType()) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(
                () -> getDpReviewDetailList(buildShopReviewCtx(), 2),
                ThreadPoolUtils.getExecutor(ThreadPoolNameEnum.REVIEW_EXECUTOR)
        );
    }

    private ShopReviewCtx buildShopReviewCtx() {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx();
        shopReviewCtx.setDpId(getDpDealGroupId());
        shopReviewCtx.setMtId(getMtDealGroupId());
        shopReviewCtx.setDpLongShopId(getDpShopId());
        shopReviewCtx.setMtLongShopId(getMtShopId());
        shopReviewCtx.setUnionId(request.getShepherdGatewayParam().getUnionid());
        shopReviewCtx.setMtUserId(request.getMtUserId());
        shopReviewCtx.setDpUserId(request.getDpUserId());
        shopReviewCtx.setToken("");
        shopReviewCtx.setClientTypeEnum(request.getClientTypeEnum());
        return shopReviewCtx;
    }

    private ProductReviewReturnValue getDpReviewDetailList(ShopReviewCtx shopReviewCtx, int displayReviewCount) {
        ProductReviewReturnValue productReviewReturnValue = new ProductReviewReturnValue();
        Future shopReviewFuture = getShopReviewFuture(shopReviewCtx, displayReviewCount);
        Future allShopReviewTagFuture = getDPAllShopReviewTagFuture(shopReviewCtx);

        PageModel pageModel = getFutureResult(shopReviewFuture);
        productReviewReturnValue.setTotalCount(pageModel == null ? 0 : pageModel.getRecordCount());

        List<ProductReviewDetail> reviewDetailDOList = buildDpReviewDOList(shopReviewCtx, pageModel, false);
        if (ShopReviewHelper.disPlayShopReviewTag()) {
            List<ProductReviewTagFilter> reviewTagDOList = createReviewTag(allShopReviewTagFuture, shopReviewCtx);
            productReviewReturnValue.setTagList(reviewTagDOList);
        }
        int mtReviewCount = 0;
        int dpReviewCount = productReviewReturnValue.getTotalCount();
        if (dpReviewCount < displayReviewCount && ShopReviewHelper.dpShopReviewNeedSupply()) {
            Future mtShopReviewFuture = getMtShopReviewFutureV2(shopReviewCtx.getDpLongShopId(), 0, displayReviewCount - dpReviewCount, ReviewFilterType.RANK_ALGO);
            MTQueryResult mtQueryResult = getFutureResult(mtShopReviewFuture);
            if (mtQueryResult != null && CollectionUtils.isNotEmpty(mtQueryResult.getMtReviewDataList())) {
                if (CollectionUtils.isEmpty(reviewDetailDOList)) {
                    reviewDetailDOList = Lists.newArrayList();
                }
                List<MTReviewData> mtReviewDataList = mtQueryResult.getMtReviewDataList();
                for (MTReviewData mtReviewData : mtReviewDataList) {
                    MtUserDto userModel = getUserModel(mtReviewData.getUserId());
                    ProductReviewDetail reviewDetailDO = ShopReviewHelper.mtReviewDataToReviewDetailDO(mtReviewData, userModel, null,  shopReviewCtx);
                    reviewDetailDOList.add(reviewDetailDO);
                }
                mtReviewCount = mtQueryResult.getQueryResultCount();
            }
        }
        productReviewReturnValue.setTotalCount(dpReviewCount + mtReviewCount);
        productReviewReturnValue.setReviewList(reviewDetailDOList);
        productReviewReturnValue.setMoreUrl(getReviewListUrl());
        return productReviewReturnValue;
    }


    private String getReviewListUrl() {
        return String.format(DP_REVIEW_LIST_URL, request.getPoiId());
    }

    public MtUserDto getUserModel(long userId) {
        if (userId <= 0) {
            return null;
        }
        try {
            return userWrapperService.getUserModel(userId, UserFieldsTypeEnum.SAMPLE_INFO.getUserFieldsType());
        } catch (Exception e) {
            log.error("userWrapperService.getUserModelMap error", e);
        }
        return null;
    }


    public List<ProductReviewDetail> buildDpReviewDOList(ShopReviewCtx shopReviewCtx, PageModel pageModel, boolean supply) {
        if (pageModel == null || CollectionUtils.isEmpty(pageModel.getRecords())) {
            return Collections.emptyList();
        }
        Set<Long> userIdLSet = Sets.newHashSet();
        List<Long> reviewIdList = Lists.newArrayList();
        List<String> reviewIdStrList = Lists.newArrayList();
        List<Long> anonymousReviewIds = Lists.newArrayList();
        Map<Long, Long> userIdToReviewId = Maps.newHashMap();
        List<ReviewDataV2> reviewDataV2List = (List<ReviewDataV2>) pageModel.getRecords();
        for (ReviewDataV2 reviewData : reviewDataV2List) {
            if (reviewData == null) {
                continue;
            }
            if (reviewData.getReviewBody() != null && reviewData.getReviewBody().length() > SHOP_PAGE_REVIEW_LENGTH) {
                reviewData.setReviewBody(reviewData.getReviewBody().substring(0, SHOP_PAGE_REVIEW_LENGTH - 1));
            }
            if (reviewData.getAnonymous()) {
                anonymousReviewIds.add(reviewData.getReviewIdLong());
                userIdToReviewId.put(reviewData.getUserId(), reviewData.getReviewIdLong());
            }
            userIdLSet.add(reviewData.getUserId());
            reviewIdList.add(reviewData.getReviewIdLong());
            reviewIdStrList.add(String.valueOf(reviewData.getReviewIdLong()));
        }
        if (CollectionUtils.isEmpty(userIdLSet)) {
            return Collections.emptyList();
        }
        List<ProductReviewDetail> reviewDetailDOList = Lists.newArrayList();
        Future anonymousUserInfoFuture = getAnonymousUserInfoFuture(anonymousReviewIds);
        Future vipFuture = getUserVipInfoFuture(userIdLSet);
        Future userFuture = getUserInfos(Lists.newArrayList(userIdLSet));
        Future userGrowthFuture = batchQueryUserGrowth(Lists.newArrayList(userIdLSet));

        Map<Long, UserDTO> userDTOMap = getFutureResult(userFuture);
        Map<Long, UserInfoForAppVO> vipUserMap = getFutureResult(vipFuture);
        Map<Long, UserManaDTO> userGrowthDTOMap = getFutureResult(userGrowthFuture);
        Map<Long, AnonymousUserInfo> anonymousUserMap = getFutureResult(anonymousUserInfoFuture);

        Map<Long, UGCUserInfo> reviewUserModelMap = ShopReviewHelper.buildReviewUserModelList(Lists.newArrayList(userIdLSet), userDTOMap, userGrowthDTOMap, vipUserMap);
        transferAnonymousUserInfo(reviewUserModelMap, userIdToReviewId, anonymousUserMap);

        for (ReviewDataV2 reviewData : reviewDataV2List) {
            ProductReviewDetail reviewDetailDO = ShopReviewHelper.reviewDataToReviewDetailDO(reviewData, reviewUserModelMap, supply);
            reviewDetailDOList.add(reviewDetailDO);
        }
        return reviewDetailDOList;
    }

    private void transferAnonymousUserInfo(Map<Long, UGCUserInfo> reviewUserModelMap, Map<Long, Long> userIdToReviewId, Map<Long, AnonymousUserInfo> anonymousUserMap) {
        if (MapUtils.isEmpty(reviewUserModelMap) || MapUtils.isEmpty(userIdToReviewId)
                || MapUtils.isEmpty(anonymousUserMap)) {
            return;
        }
        for (Map.Entry<Long, UGCUserInfo> reviewUserModelEntry : reviewUserModelMap.entrySet()) {
            if (userIdToReviewId.containsKey(reviewUserModelEntry.getKey())) {
                long reviewId = userIdToReviewId.get(reviewUserModelEntry.getKey());
                AnonymousUserInfo anonymousUserInfo = anonymousUserMap.get(reviewId);
                if (anonymousUserInfo != null) {
                    reviewUserModelEntry.getValue().setDetailUrl(null);
                    reviewUserModelEntry.getValue().setPicUrl(anonymousUserInfo.getAvatar());
                    reviewUserModelEntry.getValue().setNickName(anonymousUserInfo.getNickName());
//                    if (CollectionUtils.isNotEmpty(anonymousUserInfo.getPendantList())) {
//                        for (Pendant pendant : anonymousUserInfo.getPendantList()) {
//                            if (pendant.getType() == PendantTypeEnum.LEVEL.getPendantType()) {
//                                reviewUserModelEntry.getValue().setUserLevel(pendant.getImgUrl());
//                            }
//                        }
//                    }
                }
            }
        }
    }

    public Future getMtShopReviewFutureV2(long dpShopId, int start, int limit, int filterType) {
        if (dpShopId <= 0) {
            return null;
        }
        try {
            FilterParamV2 filterParam = createFilterParamV2(filterType);
            mtReviewQueryServiceFutureV2.getReviewByShop(dpShopId, filterParam, start, limit, null);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getMtShopReviewFuture failed:dpShopId=" + dpShopId, e);
        }
        return null;
    }

    private FilterParamV2 createFilterParamV2(int filterType) {
        FilterParamV2 filterParam = new FilterParamV2();
        // 排序
        if (ReviewFilterType.RANK_LASTTIME == filterType) {
            filterParam.setSortType(ReviewSortType.MODTIME_DESC.value);
        } else {
            filterParam.setSortType(ReviewSortType.WEIGHT_DESC.value);
        }
        // 带图
        if (ReviewFilterType.RANK_PICTURE == filterType) {
            filterParam.setHasPic(true);
        }
        // 低分
        if (ReviewFilterType.RANK_BAD == filterType) {
            List<Integer> starRange = Lists.newArrayList(10, 20);
            filterParam.setStarRange(starRange);
        }
        return filterParam;
    }

    public Future getAnonymousUserInfoFuture(List<Long> anonymousReviewIds) {
        try {
            UserRequestPara userRequestPara = new UserRequestPara();
            userRequestPara.setVipType(0);
            userRequestPara.setLevelType(1);
            dpReviewUserInfoServiceFuture.findAnonymousUserInfoV3(anonymousReviewIds, userRequestPara);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getAnonymousUserInfoFuture error", e);
        }
        return null;
    }

    //获取VIP用户信息
    public Future getUserVipInfoFuture(Set<Long> userIdLSet) {
        try {
            vipUserServiceFuture.findVipUserInfo(Lists.newArrayList(userIdLSet), 0);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getUserVipInfoFuture error", e);
        }
        return null;
    }

    public Future getUserInfos(List<Long> userIdList) {
        try {
            userBaseFuture.getUserMap(userIdList);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getUserMap error", e);
        }
        return null;
    }

    public Future batchQueryUserGrowth(List<Long> userIdList) {
        try {
            cipGrowthServiceFuture.batchLoadUserMana(userIdList);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getUserMap error", e);
        }
        return null;
    }

    public Future getShopReviewFuture(ShopReviewCtx shopReviewCtx, int pageSize) {
        try {
            reviewServiceFuture.paginateShopReviewsV3(shopReviewCtx.getDpLongShopId(), 1, pageSize, buildExtParam(shopReviewCtx.getToken()));
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getGoodShopReviews error", e);
        }
        return null;
    }

    private static ExtParamV2 buildExtParam(String token) {
        if (StringUtils.isEmpty(token)) {
            token = "";
        }
        ExtParamV2 extParam = new ExtParamV2();
        extParam.setTokenType(TokenType.NewToken.getValue());
        extParam.setTokenStr(token);
        return extParam;
    }

    public Future getDPAllShopReviewTagFuture(ShopReviewCtx shopReviewCtx) {
        try {
            Request request = new Request(Request.Platform.WWW, "reviewsummary");

            request.addQuery(new TermQuery("shopid", String.valueOf(shopReviewCtx.getDpLongShopId())));
            request.addQuery(new TermQuery("userdefaultreview", "1"));
            request.addStatItem(new StatItem("reviewtagsentiment"));
            request.addInfo("userid", String.valueOf(shopReviewCtx.getDpUserId()));
            request.addInfo("dpid", String.valueOf(shopReviewCtx.getDpId()));
            request.addSortItem(new SortItem("abstract", Request.SortOrder.DESC));

            shopReviewSearchServiceFuture.search(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getGoodShopReviews error", e);
        }
        return null;
    }

    private long getMtShopId() {
        return request.getClientTypeEnum().isMtClientType() ? request.getPoiId() : mapperCacheWrapper.fetchMtShopId(request.getPoiId());
    }

    private long getDpShopId() {
        return request.getClientTypeEnum().isMtClientType() ? mapperCacheWrapper.fetchDpShopId(request.getPoiId()) : request.getPoiId();
    }

    private long getMtDealGroupId() {
        return request.getClientTypeEnum().isMtClientType() ? request.getProductId() : mapperCacheWrapper.getMtDealGroupId(request.getProductId());
    }

    private long getDpDealGroupId() {
        return request.getClientTypeEnum().isMtClientType() ? mapperCacheWrapper.getDpDealGroupId(request.getProductId()) : request.getProductId();
    }

    public Future getMTAllShopReviewTagFuture(ShopReviewCtx shopReviewCtx) {
        try {
            Request request = new Request(Request.Platform.WWW, "reviewsummary");
            request.addQuery(new TermQuery("referid", String.valueOf(shopReviewCtx.getMtId())));
            request.addQuery(new TermQuery("source", "2"));
            request.addQuery(new TermQuery("power", "2"));
            request.addQuery(new TermQuery("refertype", "2"));
            request.addStatItem(new StatItem("reviewtagsentiment"));
            request.addInfo("userid", String.valueOf(shopReviewCtx.getMtUserId()));//已确认判断平台后再使用
            request.addInfo("mtid", shopReviewCtx.getUnionId());
            request.addSortItem(new SortItem("abstract", Request.SortOrder.DESC));

            shopReviewSearchServiceFuture.search(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getGoodShopReviews error", e);
        }
        return null;
    }

    public <T> T getFutureResult(Future serviceFuture) {
        return getFutureResult(serviceFuture, Strings.EMPTY, Strings.EMPTY);
    }


    public <T> T getFutureResult(Future serviceFuture, String className, String info) {
        if (serviceFuture == null) {
            return null;
        }
        try {
            return (T) serviceFuture.get(1000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("getFutureResult error, className:{}, info:{}", className, info, e);
        }
        return null;
    }

    /**
     * 从评论内容中获取动态标签
     * 包括非正面评价标签
     */
    private List<ProductReviewTagFilter> createReviewTag(Future allShopReviewTagFuture, ShopReviewCtx shopReviewCtx) {
        List<ProductReviewTagFilter> reviewTagDOList = Lists.newArrayList();
        createBaseReviewTag(allShopReviewTagFuture, reviewTagDOList, shopReviewCtx);
        return reviewTagDOList;
    }

    /**
     * 从评论内容中获取动态标签
     */
    private void createBaseReviewTag(Future mtShopReviewTagFuture, List<ProductReviewTagFilter> reviewTagDOList, ShopReviewCtx shopReviewCtx) {
        Response response = getFutureResult(mtShopReviewTagFuture);
        if (response == null) {
            return;
        }
        createDynamicAbstract(response, reviewTagDOList, shopReviewCtx);
    }

    private void createDynamicAbstract(Response response, List<ProductReviewTagFilter> reviewAbstracts, ShopReviewCtx shopReviewCtx) {

        if (!response.getStatus().equals(Response.OK) || CollectionUtils.isEmpty(response.getRecordList())) {
            return;
        }

        List<Record> recordList = response.getRecordList();
        List<ProductReviewTagFilter> reviewAbstractList = Lists.newArrayList();
        int count = 1;
        for (Record record : recordList) {
            Integer reviewCount = Integer.parseInt(record.get("hit"));
            String key = record.get("tag");
            String[] abstractWithAffective = key.split("_");
            if (abstractWithAffective.length < 2) {
                continue;
            }
            ProductReviewTagFilter reviewAbstract = new ProductReviewTagFilter();
            reviewAbstract.setTitle(abstractWithAffective[0].trim());
            reviewAbstract.setReviewCount(reviewCount);
            reviewAbstract.setType(1);
            reviewAbstract.setAffection(Integer.parseInt(abstractWithAffective[1])); // 不做值的校验
            reviewAbstract.setUrl(buildReviewTagUrl(shopReviewCtx, reviewAbstract));
            reviewAbstractList.add(reviewAbstract);
            count++;
        }
        if (CollectionUtils.isNotEmpty(reviewAbstractList)) {
            // 按评价标签的正向含义到负向含义排序 情感：喜欢：1，不喜欢：-1，中立：0
            reviewAbstractList.sort(Comparator.comparing(ProductReviewTagFilter::getAffection).reversed());
            reviewAbstracts.addAll(reviewAbstractList);
        }
    }

    private String buildReviewTagUrl(ShopReviewCtx shopReviewCtx, ProductReviewTagFilter reviewAbstract) {
        return String.format(DP_REVIEW_TAG_URL, shopReviewCtx.getDpLongShopId(), 700, ShopUrlUtils.urlEncode(reviewAbstract.getTitle()), reviewAbstract.getAffection());
    }

}