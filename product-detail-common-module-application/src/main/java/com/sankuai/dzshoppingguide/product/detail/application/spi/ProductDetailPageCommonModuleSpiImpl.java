package com.sankuai.dzshoppingguide.product.detail.application.spi;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.api.module.arrange.framework.application.ModuleArrangeFrameworkRunner;
import com.sankuai.dz.api.module.arrange.framework.application.request.ModuleArrangeRequest;
import com.sankuai.dz.api.module.arrange.framework.application.response.FrameworkRunnerResult;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.ProductDetailPageResponse;
import com.sankuai.dz.product.detail.gateway.spi.service.ProductDetailPageService;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashSet;

/**
 * @Author: guangyujie
 * @Date: 2025/1/24 15:47
 */
@MdpPigeonServer(
        serviceInterface = ProductDetailPageService.class,
        url = "com.sankuai.dzshoppingguide.ProductDetailPageCommonModuleSpiService",
        useSharedPool = false,
        poolName = "ProductDetailPageCommonModuleSpiService"//使用专属线程池
)
@Slf4j
public class ProductDetailPageCommonModuleSpiImpl implements ProductDetailPageService {

    private static final String LOG_PRINT_CONDITION_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.log.print.condition.config";

    @Override
    public ProductDetailPageResponse query(ProductDetailPageRequest request) {
        try {
            request.checkParam();
            if (CollectionUtils.isEmpty(request.getModuleKeys())) {
                throw new IllegalArgumentException("入参ModuleKey为空");
            }
            //默认带上打点模块
            request.getModuleKeys().add(ModuleKeyConstants.COMMON_DATA);
            final ModuleArrangeRequest moduleArrangeRequest = new ModuleArrangeRequest(
                    request, new HashSet<>(request.getModuleKeys())
            );
            FrameworkRunnerResult result = ModuleArrangeFrameworkRunner.run(moduleArrangeRequest);

            try {
                // 线下以及RC环境打印日志,方便调试定位问题
                if (Lion.getBoolean(Environment.getAppName(), LOG_PRINT_CONDITION_CONFIG, false)) {
                    log.info(XMDLogFormat.build().putTag("interfaceName", "ProductDetailPageCommonModuleSpiImpl")
                            .message(String.format("ProductDetailPageCommonModuleSpiImpl.query: request:%s",
                                    JsonCodec.encode(request))));
                }
            } catch (Exception e) {
                log.error("ProductDetailPageCommonModuleSpiImpl log error", e);
            }

            return result.getProductDetailPageResponse();
        } catch (Exception e) {
            log.error("ProductDetailPageCommonModuleSpi,request:{}", JSON.toJSONString(request), e);
            return ProductDetailPageResponse.fail(e.getMessage());
        }
    }

}
