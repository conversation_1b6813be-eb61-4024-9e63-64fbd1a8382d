package com.sankuai.dzshoppingguide.product.detail.application.builder.review;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.review.DpDealReviewFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.review.MtDealReviewFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.review.ProductReviewReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.ProductReviewVO;
import org.apache.commons.collections4.CollectionUtils;

/**
 * @Author: litengfei04
 * @Date: 2025/2/5 17:45
 */
@Builder(
        moduleKey = ModuleKeyConstants.DEAL_REVIEW,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class,
                MtDealReviewFetcher.class,
                DpDealReviewFetcher.class,
        }
)
public class DealReviewModuleBuilder extends BaseBuilder<ProductReviewVO> {

    @Override
    public ProductReviewVO doBuild() {
        ProductReviewReturnValue mtReviewValue = getDependencyResult(MtDealReviewFetcher.class);
        ProductReviewReturnValue dpReviewValue = getDependencyResult(DpDealReviewFetcher.class);

        if (request.getClientTypeEnum().isMtClientType()) {
            return mtReviewValue == null || mtReviewValue.getTotalCount() == 0 || CollectionUtils.isEmpty(mtReviewValue.getReviewList()) ? null : toProductReviewVO(mtReviewValue);
        } else {
            return dpReviewValue == null || dpReviewValue.getTotalCount() == 0 || CollectionUtils.isEmpty(dpReviewValue.getReviewList()) ? null : toProductReviewVO(dpReviewValue);
        }
    }

    private ProductReviewVO toProductReviewVO(ProductReviewReturnValue reviewReturnValue) {
        ProductReviewVO reviewVO = new ProductReviewVO();
        reviewVO.setReviewList(reviewReturnValue.getReviewList());
        reviewVO.setTitle(reviewReturnValue.getTitle());
        reviewVO.setTagList(reviewReturnValue.getTagList());
        reviewVO.setTotalCount(reviewReturnValue.getTotalCount());
        reviewVO.setMoreUrl(reviewReturnValue.getMoreUrl());
        return reviewVO;
    }

}
