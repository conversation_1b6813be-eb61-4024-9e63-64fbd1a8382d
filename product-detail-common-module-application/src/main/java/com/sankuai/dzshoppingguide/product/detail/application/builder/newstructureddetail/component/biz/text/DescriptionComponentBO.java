package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text;

import com.sankuai.athena.digital.arch.annotations.DigitalArchField;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentMetadata;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.ComponentVO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.DetailComponentKeyConstant;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.DetailType13ViewComponent;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: caisiyuan03
 * @Date: 2025/6/3 20:44
 * @Description: TODO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@ComponentMetadata(
        componentKey = DetailComponentKeyConstant.Description,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        desc = "内容描述组件",
        componentGroup = ComponentGroupEnum.Business,
        componentType = ComponentTypeEnum.Text_Biz
)
public class DescriptionComponentBO extends BizComponentBO {

    @DigitalArchField(desc = "补充说明")
    private String description;

    @Override
    protected ComponentVO doBuildVO(int recursionDepth) {
        DetailType13ViewComponent viewComponent = DetailType13ViewComponent.builder().content(description).build();
        return new BizComponentVO(viewComponent);
    }
}