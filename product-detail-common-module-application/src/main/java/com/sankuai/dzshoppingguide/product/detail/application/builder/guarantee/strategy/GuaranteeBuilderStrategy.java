package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy;

import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.ProductGuaranteeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 商品详情页保障条构造策略
 */
public interface GuaranteeBuilderStrategy {
    /**
     * 获取策略枚举
     * @return 策略枚举
     */
    GuaranteeStrategyEnum getStrategyEnum();

    ProductGuaranteeDTO build(GuaranteeParam param);
}
