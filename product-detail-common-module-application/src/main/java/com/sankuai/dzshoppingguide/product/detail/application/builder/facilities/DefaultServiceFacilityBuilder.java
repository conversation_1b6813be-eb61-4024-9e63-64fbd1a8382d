package com.sankuai.dzshoppingguide.product.detail.application.builder.facilities;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.facilities.vo.DetailServiceFacilitiesVO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/29 17:32
 */
@Slf4j
@Builder(
        moduleKey = ModuleKeyConstants.DEAL_DETAIL_FACILITIES,
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {CommonModuleStarter.class}
)
public class DefaultServiceFacilityBuilder extends BaseVariableBuilder<DetailServiceFacilitiesVO> {
    @Override
    public DetailServiceFacilitiesVO doBuild() {
        return null;
    }
}
