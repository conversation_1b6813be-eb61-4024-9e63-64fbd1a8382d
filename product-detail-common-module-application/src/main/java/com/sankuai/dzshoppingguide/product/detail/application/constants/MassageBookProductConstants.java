package com.sankuai.dzshoppingguide.product.detail.application.constants;


public class MassageBookProductConstants {

    public interface Params {
        /**
         * 平台，1-点评，2美团
         */
        String PLATFORM = "platform";

        /**
         * 设备类型，DP_APP(100, "dpapp"), DP_M(101, "m"), DP_XCX(102, "dpxcx"),
         * MT_APP(200, "mtapp"),MT_I(201, "i")， MT_XCX(202, "mtxcx")
         */
        String UA_CODE ="uaCode";

        /**
         * 城市id，点评传点评，美团传美团
         */
        String CITY_ID = "cityId";

        /**
         * 城市ID,点评侧
         */
        String DP_CITY_ID = "dpCityId";

        /**
         * 城市ID,美团侧
         */
        String MT_CITY_ID = "mtCityId";

        /**
         * 设备ID，点评dpid，美团uuid
         */
        String DEVICE_ID = "deviceId";

        /**
         * 统一设备ID
         */
        String UNION_ID = "unionId";

        /**
         * 纬度
         */
        String LAT = "lat";

        /**
         * 经度
         */
        String LNG = "lng";

        /**
         * 用户ID，区分平台，点评传点评，美团传美团
         */
        String USER_ID = "userId";

        /**
         * 点评用户ID
         */
        String DP_USER_ID = "dpUserId";

        /**
         * 美团用户ID
         */
        String MT_USER_ID = "mtUserId";

        /**
         * app版本
         */
        String APP_VERSION = "appVersion";

        /**
         * 门店UUID
         */
        String SHOP_UUID = "shopUuid";

        /**
         * 标品类型
         */
        String PRODUCT_TYPE = "spuType";

        /**
         * 门店ID，区分平台
         */
        String SHOP_IDL = "shopIdL";

        /**
         * 门店ID，区分平台
         */
        String SHOP_ID = "shopId";

        /**
         * 美团门店ID
         */
        String MT_SHOP_IDL = "mtShopIdL";

        /**
         * 美团门店ID,int
         */
        String MT_SHOP_ID = "mtShopId";

        /**
         * 点评门店ID,long
         */
        String DP_SHOP_IDL = "dpShopIdL";

        /**
         * 点评门店ID,int
         */
        String DP_SHOP_ID = "dpShopId";

        /**
         * 门店城市ID
         */
        String SHOP_CITY_ID = "shopCityId";

        /**
         * 门店点评门店ID
         */
        String SHOP_DP_CITY_ID = "shopDpCityId";

        /**
         * 门店美团门店ID
         */
        String SHOP_MT_CITY_ID = "shopMtCityId";

        /**
         * 标品基础信息查询策略
         */
        String QUERY_SPU_STRATEGIES = "querySpuStrategies";

        /**
         * 标品价格信息查询策略
         */
        String QUERY_PRICE_SCENE = "queryPriceScene";

        /**
         * 场景到主题字段映射关系
         */
        String SCENE_TO_THEME_PARAMS = "sceneThemeParamsMap";

        /**
         * 类目id
         */
        String CATEGORY_ID = "categoryId";

        /**
         * 查询价格优惠标识
         */
        String QUERY_PRICE_PROMO_SCENE = "scene";

        /**
         * 查询其他优惠场景信息时需要传递，如未来优惠时。(足疗预订会员场景下优惠传递400201)
         */
        String QUERY_FUTURE_PRICE_PROMO_SCENE = "promoScene";

        /**
         * 查询报价服务的日期列表
         */
        String SELECT_DATES = "selectDates";

        /**
         * 查询价格优惠标识列表
         * @see com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum
         */
        String QUERY_PRICE_SCENE_LIST = "promoSceneList";

        /**
         * 商品对应的门店id，透传到主题层shopTheme使用，value是Map<Integer, Integer>
         */
        String PRODUCTID_TO_SHOPID = "productId2ShopIdMap";
        /**
         * 商品对应的门店id，透传到主题层shopTheme使用，value是Map<Integer, Long>，兼容poi升级
         */
        String PRODUCTID_TO_SHOPIDASLONG = "productId2ShopIdAsLongMap";

        /**
         * 商品对应的门店id，透传到主题层shopTheme使用，value是Map<Integer, Long>
         */
        String PRODUCTID_TO_SELECT_DATE = "productId2DateMap";

        /**
         * 价格力业务类型ID，如：医美预付：1011
         */
        String PRICE_ABILITY_BIZ_TYPE_ID = "priceAbilityBizTypeId";

        /**
         * 标签查询key
         */
        String TAG_QUERY_KEY = "tagQueryKey";

        /**
         * 是否获取自促详情， true:获取，false:不获取
         */
        String GOODS_ACTIVITY_DETAIL = "goodsActivityDetail";

        /**
         * 标签业务ID
         */
        String TAG_BIZ_ID = "tagBizId";

        /**
         * 收藏来源的类型
         */
        String MT_COLLECTION_SOURCE_TYPE = "mtCollectionSourceType";

        /**
         * 活动来源
         */
        String ACTIVITY_SOURCE = "activitySource";

        /**
         * 商品类目
         */
        String PRODUCT_CATEGORY = "productCategory";

        /**
         * 排行榜场景
         */
        String RANK_SCENE = "rankScene";

        /**
         * 排行榜来源
         */
        String RANK_SOURCE = "rankSource";

        /**
         * 商品对应的拼场id，用于拼场数据填充时，只需要查询这些poolId。value是Map<Integer, List<Long>>
         */
        String PRODUCT_TO_POOL_IDS = "product2PoolIds";

        /**
         * 查询拼场信息，是否需要场次价格
         */
        String POOL_NEED_PRICE_INFO = "poolNeedPriceInfo";

        /**
         * 查询拼场信息，是否需要场次用户信息
         */
        String POOL_NEED_USER_INFO = "poolNeedUserInfo";

        /**
         * 查询价格，需要考虑未来多少天的特殊日期参数
         */
        String CAL_PRICE_DAYS = "calPriceDays";

        /**
         * 设备号 API层是deviceId，服务内流转dpId
         * 足疗主题内部统一用dpId接收美团和点评的设备号
         */
        String DP_ID = "dpId";

        /**
         * 商品版本，快照页需要
         */
        String PRODUCT_VERSION = "productVersion";

        /**
         * 传递sku参数
         */
        String PRODUCT_ITEM_ID = "productItemId";

        /**
         * 是否快照商品
         */
        String SNAPSHOT_PRODUCT = "snapshotProduct";

        /**
         * pageSource,页面来源
         */
        String PAGE_SOURCE = "pageSource";

        /**
         * 【二级商品组参数, 可选】当前商品组销量标识, 从泛商品渠道查询销量的时候需要使用
         */
        String STATISTIC_SALE_BIZ_TYPE = "statisticSaleBizType";

        /**
         * 客户端类型：ios | android | 空字符串
         */
        String CLIENT_TYPE = "clientType";

        /**
         * 请求价格服务参数,表示单列还是双列，同时获取优惠标签
         */
        String PRICE_DESC_TYPE = "priceDescType";

        String PRODUCTID_TO_SKUID = "productId2SkuIdMap";

        /**
         * 商品管理的技师id
         */
        String TECH_ID = "techId";

        /**
         * 门店标识信息
         */
        String SHOP_IDENTIFIER = "shopIdentifier";

        /**
         * 泛指查询天数
         */
        String DAYS = "days";

        /**
         * 泛指查询开始时间
         */
        String START_TIME = "startTime";

        /**
         * 泛指查询结束时间
         */
        String END_TIME = "endTime";

        String SPU_TYPE = "spuType";

        /**
         * 提单页拼参唯一code
         */
        String URL_EXTRA_CODE = "urlExtraCode";

        /**
         * 传给交易的页面来源 0 货架 1 商详 2 详情页
         */
        String SUBMIT_PAGE_SOURCE = "submitPageSource";

        /**
         * 是否调用详情页新样式
         */
        String queryNewTuanDetail = "queryNewTuanDetail";

        /**
         * 神会员点位信息（膨胀点位信息，报价需要）
         */
        String pricePosition = "pricePosition";

        /**
         * 神会员Page信息(所属页面信息，报价需要)
         */
        String pricePageSource = "pricePageSource";

        /**
         * 门店定位城市id
         */
        String userLocalCityId = "locationCityId";

        /**
         * 美团侧门店定位城市id
         */
        String mtUserLocalCityId = "mtUserLocalCityId";

        /**
         * 点评侧门店对应城市id
         */
        String dpUserLocalCityId = "dpUserLocalCityId";

        /**
         * 用户ID, 不同平台不同值
         */
        String mtVirtualUserId = "virtualMTUserId";


    }

}
