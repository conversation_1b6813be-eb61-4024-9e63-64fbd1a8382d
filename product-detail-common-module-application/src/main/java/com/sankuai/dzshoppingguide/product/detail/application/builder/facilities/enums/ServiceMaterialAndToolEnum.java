package com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 材料工具
 *
 * @author: created by hang.yu on 2023/9/13 11:16
 */
@Getter
@AllArgsConstructor
public enum ServiceMaterialAndToolEnum {
    /**
     * 按摩工具
     */
    HEAD_MASSAGE_TOOL("HeadMassageTool", "按摩工具", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),
    /**
     * 洗头/洗发材料
     */
    HAIR_WASHING_MATERIAL("ShampooHairWashingMaterial", "洗头/洗发材料", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),
    /**
     * 头发/头皮护理工具
     */
    HAIR_SCALP_CARE_TOOLS("HairScalpCareTools", "头发/头皮护理工具", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),
    /**
     * 热敷工具
     */
    HEAD_THERAPEUTIC_HOT_COMPRESS_TOOL("HeadTherapeuticHotCompressTool", "热敷工具", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),
    /**
     * 消毒及卫生用品
     */
    DISINFECTING_AND_HYGIENE_PRODUCTS("DisinfectingAndHygieneProducts", "消毒及卫生用品", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),
    /**
     * 热敷工具
     */
    HOTPACK_TOOL("hotpackTool", "热敷工具", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 按摩工具
     */
    MASSAGE_TOOL("massageTool", "按摩工具", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 泡脚桶
     */
    FOOT_BATH_BUCKET("footbathBucket", "泡脚桶", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 泡脚包
     */
    FOOT_BATH_MATERIAL("footbathMaterial", "泡脚包", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 免费精油
     */
    FREE_ESSENTIAL_OIL("freeEssentialOil", "免费精油", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 特色采耳工具
     */
    EAR_PICKING_TOOL("earpickingTool", "特色采耳工具", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 刮痧工具
     */
    SCRAPING_TOOL("scrapingTool", "刮痧工具", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 刮痧材料
     */
    SCRAPING_MATERIAL("scrapingMaterial", "刮痧材料", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 拔罐罐体
     */
    CUPPING_TOOL("cuppingTool", "拔罐罐体", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 艾灸材料
     */
    MOXIBUSTION_MATERIAL("moxibustionMaterial", "艾灸材料", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 艾灸工具/仪器
     */
    MOXIBUSTION_TOOL("moxibustionTool", "艾灸工具/仪器", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 衣物布草
     */
    DISPOSABLE_MATERIAL("disposableMaterial", "衣物布草", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 其他工具
     */
    UNCLASSIFIED_TOOLS("unclassifiedTools", "其他工具", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 女性用品
     */
    FEMALE_SUPPLIES("FeminineProducts", "女性用品", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    /**
     * 消毒及卫生用品
     */
    DISINFECTION_SUPPLIES("DisinfectingAndHygieneProducts", "消毒及卫生用品", "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png"),

    ;

    /**
     * 工具code
     */
    private final String toolCode;

    /**
     * 工具名称
     */
    private final String toolName;

    /**
     * 工具图标
     */
    private final String icon;

}
