package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealreserve.DealOnlineReserveFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealreserve.DealOnlineReserveResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.*;
import com.sankuai.dzshoppingguide.product.detail.domain.cpv.unavailable.UnavailableDateConfigService;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Builder(
        builderType = BuilderTypeEnum.ABSTRACT_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO, startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductAttrFetcher.class, ProductCategoryFetcher.class, DealOnlineReserveFetcher.class, ProductBaseInfoFetcher.class, SkuAttrFetcher.class,SkuDefaultSelectFetcher.class}
)
/**
 * 须知条
 */
public abstract class AbstractReminderInfoBuilder extends BaseVariableBuilder<ProductDetailReminderVO> {

    // 工作日/周末挡
    private static final String WEEKEND_TIME = "weekendTime";

    @Autowired
    private UnavailableDateConfigService dateConfig;

    @Override
    public ProductDetailReminderVO doBuild() {
        return afterBuild(preBuild());
    }

    /**
     * 执行各行业定制化的须知条信息
     * @return
     */
    public abstract ProductDetailReminderVO preBuild();

    /**
     * 适用于全行业的通用后置处理
     * @param productDetailReminderVO
     * @return
     */
    public ProductDetailReminderVO afterBuild(ProductDetailReminderVO productDetailReminderVO) {
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        DisableDateDTO disableDateDTO = Optional.ofNullable(productBaseInfo).map(ProductBaseInfo::getRule)
                .map(DealGroupRuleDTO::getUseRule).map(DealGroupUseRuleDTO::getDisableDate).orElse(null);

        // 命中周几不可用
        List<String> weekDay = Optional.ofNullable(productAttr).map(item -> item.getSkuAttrValue("TimeRange3")).orElse(Lists.newArrayList());
        List<Integer> weekDayInt = AvailableTimeHelper.getWeekDayInt(weekDay);
        int todayWeekDay = AvailableTimeHelper.getTodayWeekDay();
        if (CollectionUtils.isNotEmpty(weekDayInt) && !weekDayInt.contains(todayWeekDay)) {
            return UnavailableReminderInfoUtils.processHit(productDetailReminderVO, ReminderInfoUtils.fromOldDetail(request));
        }

        // 命中每周以及节假日不可用日期
        List<Integer> disableDays = Optional.ofNullable(disableDateDTO).map(DisableDateDTO::getDisableDays).orElse(Lists.newArrayList());
        disableDays = handleSpecialDisableDays(productAttr, disableDays);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(disableDays) && UnavailableReminderInfoUtils.hitWeekAndHolidayUnavailable(disableDays,dateConfig.getConfig())) {
            return UnavailableReminderInfoUtils.processHit(productDetailReminderVO, ReminderInfoUtils.fromOldDetail(request));
        }

        // 命中自定义不可用日期
        List<DateRangeDTO> disableDateRangeDTOS = Optional.ofNullable(disableDateDTO).map(DisableDateDTO::getDisableDateRangeDTOS).orElse(Lists.newArrayList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(disableDateRangeDTOS) && UnavailableReminderInfoUtils.hitCustomUnavailable(disableDateRangeDTOS)) {
            return UnavailableReminderInfoUtils.processHit(productDetailReminderVO, ReminderInfoUtils.fromOldDetail(request));
        }

        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        long skuId = Optional.ofNullable(skuDefaultSelect).map(SkuDefaultSelect::getSelectedSkuId).orElse(0L);
        // 命中工作日-周末档不可用日期
        SkuAttr skuAttr = getDependencyResult(SkuAttrFetcher.class);
        String weekendTime = Optional.ofNullable(skuAttr).map(attr -> attr.getSkuAttrFirstValue(skuId, WEEKEND_TIME)).orElse(StringUtils.EMPTY);
        if (com.dianping.zebra.util.StringUtils.isNotBlank(weekendTime) && UnavailableReminderInfoUtils.hitWeekendUnavailable(weekendTime)) {
            return UnavailableReminderInfoUtils.processHit(productDetailReminderVO, ReminderInfoUtils.fromOldDetail(request));
        }

        // 如果设置了可用时间需要进一步判断
        AvailableDateDTO availableDateDTO = Optional.ofNullable(productBaseInfo).map(ProductBaseInfo::getRule)
                .map(DealGroupRuleDTO::getUseRule).map(DealGroupUseRuleDTO::getAvailableDate).orElse(null);
        if (availableDateDTO != null && !UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO)) {
            return UnavailableReminderInfoUtils.processHit(productDetailReminderVO, ReminderInfoUtils.fromOldDetail(request));
        }
        return productDetailReminderVO;
    }

    /**
     * 足疗按摩、茶馆、棋牌室、游戏厅、台球团详及上单改造需求中,不可用日期中不能存在一周中的数据,但是供应链洗数据没这么快并且可能洗不干净,这里做兼容
     * @param productAttr
     * @param disableDays
     * @return
     */
    private static List<Integer> handleSpecialDisableDays(ProductAttr productAttr, List<Integer> disableDays) {
        String availableTimePeriod3 = Optional.ofNullable(productAttr).map(item -> item.getSkuAttrFirstValue("AvailableTimePeriod3")).orElse("");
        if (StringUtils.isNotBlank(availableTimePeriod3)) {
            disableDays = disableDays.stream()
                    .filter(day -> day < 1 || day > 7)
                    .collect(Collectors.toList());
        }
        return disableDays;
    }

    public ProductDetailReminderVO getBaseReminderInfo() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        DealOnlineReserveResult dealOnlineReserveResult = getDependencyResult(DealOnlineReserveFetcher.class);
        if (productAttr == null || CollectionUtils.isEmpty(productAttr.getSkuAttrList())) {
            return null;
        }
        List<AttrDTO> attrList = productAttr.getSkuAttrList();
        ProductDetailReminderVO bookingDetailsReminderInfoVO = new ProductDetailReminderVO();

        GuaranteeInstructionsBarLayerVO reminderInfoLayerVO = new GuaranteeInstructionsBarLayerVO();
        reminderInfoLayerVO.setType(2);
        reminderInfoLayerVO.setModulekey(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS);
        bookingDetailsReminderInfoVO.setLayer(reminderInfoLayerVO);
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        List<String> reservationInfo = getReservationInfo(attrList, request, productCategory, dealOnlineReserveResult);
        if (CollectionUtils.isNotEmpty(reservationInfo)) {
            List<String> reservations = reservationInfo.subList(0, 1);
            List<GuaranteeInstructionsContentVO> reminderInfoVOS = reservations.stream().filter(StringUtils::isNotBlank).map(item -> {
                Optional<GuaranteeInstructionsContentVO> reminderInfoVO = ReminderInfoUtils.buildReminderInfo(item, ReminderInfoUtils.fromOldDetail(request));
                return reminderInfoVO.orElse(null);
            }).filter(Objects::nonNull).collect(Collectors.toList());
            contents.addAll(reminderInfoVOS);
        }
        bookingDetailsReminderInfoVO.setContents(contents);
        return bookingDetailsReminderInfoVO;
    }

    private List<String> getReservationInfo(List<AttrDTO> attrList, ProductDetailPageRequest request, ProductCategory categoryDTO, DealOnlineReserveResult dealOnlineReserveResult) {
        List<String> reservationInfo = new ArrayList<>();
        String reservation = null;
        //从上单信息（团单属性）中判断是否需预约、是否支持上门、是否支持到店
        boolean needReservation = DealAttrHelper.needReservation(attrList);
        boolean supportHome = DealAttrHelper.isSupportHomeService(attrList);
        boolean supportShop = DealAttrHelper.isSupportShopService(attrList);
        // 判断是否为预订单，仅支持可预约团单
        boolean needPreOrder = isPreOrderDeal(request,categoryDTO);
        int categoryId = Optional.ofNullable(categoryDTO).map(ProductCategory::getProductSecondCategoryId).orElse(0);
        if ( LionConfigUtils.reserveAfterPurchase(categoryId)) {
            if (reserveOnline(dealOnlineReserveResult)) {
                reservation = needPreOrder ? "在线预订" : "在线预约";
            } else if (supportHome) {
                reservation = needPreOrder ? "预订上门" : "预约上门";
            } else if (supportShop && needReservation) {
                reservation = needPreOrder ? "预订到店" : "预约到店";
            } else if (needReservation) {
                reservation = needPreOrder ? "需预订" : "需预约";
            }
        } else if (needReservation) {
            reservation = needPreOrder ? "需预订" : "需预约";
        }
        if (reservation != null) {
            reservationInfo.add(reservation);
        }

        // 指定类目并且预约信息为不为"是"显示"免预约", 若指定类目预约信息为空时不显示"免预约"
        if ((!needReservation && !LionConfigUtils.forceReserve(categoryId)) &&
                (!isReservationEmpty(attrList, categoryId))) {
            reservationInfo.add("免预约");
        }
        return reservationInfo;
    }

    private boolean reserveOnline(DealOnlineReserveResult dealOnlineReserveResult) {
        if (dealOnlineReserveResult == null) {
            return false;
        }
        return dealOnlineReserveResult.isCanReserve();
    }

    private boolean isPreOrderDeal(ProductDetailPageRequest request,ProductCategory categoryDTO) {
        if ( Objects.isNull(request) || Objects.isNull(request.getShepherdGatewayParam())) {
            return false;
        }
        // 当前仅作用于美团APP、点评APP
        // 标识和二级类目决定是否为强预订团单
        return request.getClientTypeEnum().isInApp() &&  com.sankuai.dz.product.detail.RequestSourceEnum.PRE_ORDER_DEAL.getSource().equals(request.getPageSource()) && hitPreOrderDealInfo(categoryDTO);
    }

    private boolean hitPreOrderDealInfo(ProductCategory categoryDTO) {
        String categoryId = Optional.ofNullable(categoryDTO).map(ProductCategory::getProductSecondCategoryId).map(String::valueOf).orElse(StringUtils.EMPTY);
        List<String> preOrderCategoryIds = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.PRE_ORDER_CATEGORY_IDS, String.class, Collections.emptyList());
        if ( org.apache.commons.collections4.CollectionUtils.isEmpty(preOrderCategoryIds)) {
            return false;
        }
        return preOrderCategoryIds.contains(categoryId);
    }


    // 指定类目的预约信息没有值, 则返回true
    private boolean isReservationEmpty(List<AttrDTO> attrs, int categoryId) {
        // 指定的二级类目
        List<Long> filterCategory = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.RESERVATION_EMPTY_NOT_DISPLAY_TYPE, Long.class, new ArrayList<>());
        // 团单二级类目 不是 指定Lion配置的类目则返回false
        if (categoryId <= 0 || !filterCategory.contains((long)categoryId)) {
            return false;
        }
        // 预约信息为空
        return CollectionUtils.isEmpty(DealAttrHelper.getAttributeValues(attrs, DealAttrKeys.RESERVATION)) &&
                CollectionUtils.isEmpty(DealAttrHelper.getAttributeValues(attrs, DealAttrKeys.RESERVATION_2)) &&
                CollectionUtils.isEmpty(DealAttrHelper.getAttributeValues(attrs, DealAttrKeys.RESERVATION_3));
    }
}
