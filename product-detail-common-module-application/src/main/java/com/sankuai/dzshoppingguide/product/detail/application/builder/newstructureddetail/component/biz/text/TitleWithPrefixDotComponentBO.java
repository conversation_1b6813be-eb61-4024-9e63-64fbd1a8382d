package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text;

import com.sankuai.athena.digital.arch.annotations.DigitalArchField;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentMetadata;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.ComponentVO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.DetailComponentKeyConstant;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.DetailType11ViewComponent;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-05-30
 * @desc
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@ComponentMetadata(
        componentKey = DetailComponentKeyConstant.Title_With_PrefixDot,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        desc = "带前缀点的标题组件",
        componentGroup = ComponentGroupEnum.Business,
        componentType = ComponentTypeEnum.Text_Biz
)
public class TitleWithPrefixDotComponentBO extends BizComponentBO {

    @DigitalArchField(desc = "标题")
    private String title;

    @Override
    protected ComponentVO doBuildVO(int recursionDepth) {
        BaseViewComponent viewComponent = DetailType11ViewComponent.builder()
                .title(title)
                .build();
        return new BizComponentVO(viewComponent);
    }

}
