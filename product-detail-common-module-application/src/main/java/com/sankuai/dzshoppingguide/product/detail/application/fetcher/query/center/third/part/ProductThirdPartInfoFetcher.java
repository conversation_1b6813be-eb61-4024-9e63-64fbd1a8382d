package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.third.part;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.utils.SkuIdUtils;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.third.part.dto.SkuThirdPartyDTO;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupThirdPartyBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealThirdPartyBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupThirdPartyDTO;

import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/6 11:28
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class
)
public class ProductThirdPartInfoFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        ProductThirdPartInfo> {

    @Override
    public void fulfillRequest(final QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder
                .dealGroupThirdPart(DealGroupThirdPartyBuilder.builder().all())
                .dealThirdParty(DealThirdPartyBuilder.builder().all())
        ;
    }

    @Override
    protected FetcherResponse<ProductThirdPartInfo> mapResult(final FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        Optional<DealGroupDTO> dealGroupDTOOptional = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO);
        if (!dealGroupDTOOptional.isPresent()) {
            return FetcherResponse.succeed(null);//聚合查询失败已经报错了，这里无需再报错
        }
        DealGroupDTO dealGroupDTO = dealGroupDTOOptional.get();
        DealGroupThirdPartyDTO thirdPartyInfo = dealGroupDTO.getThirdPartyInfo();
        Map<Long, SkuThirdPartyDTO> skuThirdPartyDTOMap = Optional.ofNullable(dealGroupDTO.getDeals())
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(
                        sku -> SkuIdUtils.getSkuId(this.request.getProductTypeEnum(), sku),
                        sku -> new SkuThirdPartyDTO(sku.getBasic())
                ));
        return FetcherResponse.succeed(new ProductThirdPartInfo(thirdPartyInfo, skuThirdPartyDTOMap));
    }
}
