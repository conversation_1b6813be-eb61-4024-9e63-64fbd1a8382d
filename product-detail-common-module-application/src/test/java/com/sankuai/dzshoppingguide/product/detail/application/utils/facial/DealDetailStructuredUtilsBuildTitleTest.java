package com.sankuai.dzshoppingguide.product.detail.application.utils.facial;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class DealDetailStructuredUtilsBuildTitleTest {

    /**
     * 测试正常title输入场景
     * 验证方法能正确构建包含title和type的对象
     */
    @Test
    @DisplayName("Should build VO with given title and correct type")
    public void testBuildTitleWithValidTitle() {
        // arrange
        String expectedTitle = "Test Title";
        int expectedType = ViewComponentTypeEnum.TITLE.getType();
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildTitle(expectedTitle);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedTitle, result.getTitle(), "Title should match input");
        assertEquals(expectedType, result.getType(), "Type should be TITLE type");
    }

    /**
     * 测试空字符串title输入场景
     * 验证方法能正确处理空字符串title
     */
    @Test
    @DisplayName("Should handle empty title correctly")
    public void testBuildTitleWithEmptyTitle() {
        // arrange
        String emptyTitle = "";
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildTitle(emptyTitle);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(emptyTitle, result.getTitle(), "Title should be empty string");
        assertEquals(ViewComponentTypeEnum.TITLE.getType(), result.getType(), "Type should be TITLE type");
    }

    /**
     * 测试null title输入场景
     * 验证方法能正确处理null title
     */
    @Test
    @DisplayName("Should handle null title correctly")
    public void testBuildTitleWithNullTitle() {
        // arrange
        String nullTitle = null;
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildTitle(nullTitle);
        // assert
        assertNotNull(result, "Result should not be null");
        assertNull(result.getTitle(), "Title should be null");
        assertEquals(ViewComponentTypeEnum.TITLE.getType(), result.getType(), "Type should be TITLE type");
    }

    /**
     * 测试builder模式是否正确使用
     * 验证builder创建的对象类型正确
     */
    @Test
    @DisplayName("Should use builder pattern correctly")
    public void testBuildTitleBuilderPattern() {
        // arrange
        String testTitle = "Builder Test";
        DealDetailStructuredDetailVO mockVO = mock(DealDetailStructuredDetailVO.class);
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder mockBuilder = mock(DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder.class);
        when(mockBuilder.title(anyString())).thenReturn(mockBuilder);
        when(mockBuilder.type(anyInt())).thenReturn(mockBuilder);
        when(mockBuilder.build()).thenReturn(mockVO);
        // 由于buildTitle是静态方法且不依赖外部，这里主要验证builder的使用方式
        // 实际测试中不需要mock，这里仅展示mock用法
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildTitle(testTitle);
        // assert
        assertNotNull(result, "Result should not be null");
        // 验证builder模式创建的对象类型正确
        assertTrue(result instanceof DealDetailStructuredDetailVO, "Result should be instance of DealDetailStructuredDetailVO");
    }
}
