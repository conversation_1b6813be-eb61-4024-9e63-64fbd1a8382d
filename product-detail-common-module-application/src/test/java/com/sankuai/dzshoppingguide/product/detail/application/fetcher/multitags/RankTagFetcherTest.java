package com.sankuai.dzshoppingguide.product.detail.application.fetcher.multitags;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingLabelDTO;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;

class RankTagFetcherTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ShopIdMapper shopIdMapper;

    @Spy
    @InjectMocks
    private TestableRankTagFetcher rankTagFetcher;

    // Create a testable subclass that exposes protected method
    static class TestableRankTagFetcher extends RankTagFetcher {

        public ShopIdMapper getShopIdMapperForTest() {
            return getDependencyResult(ShopIdMapperFetcher.class);
        }
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test when product type is not DEAL
     */
    @Test
    public void testDoFetchWhenProductTypeNotDeal() throws Throwable {
        // arrange
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.RESERVE);
        // act
        CompletableFuture<RankTagResult> result = rankTagFetcher.doFetch();
        // assert
        assertNull(result.get());
    }
}
