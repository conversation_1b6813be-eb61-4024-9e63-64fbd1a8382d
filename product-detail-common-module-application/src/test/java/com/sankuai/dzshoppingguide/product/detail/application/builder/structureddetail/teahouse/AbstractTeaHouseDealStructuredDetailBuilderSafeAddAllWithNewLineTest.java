package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import static org.junit.jupiter.api.Assertions.*;

import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AbstractTeaHouseDealStructuredDetailBuilderSafeAddAllWithNewLineTest {

    @Spy
    private TestTeaHouseBuilder builder;

    @Mock
    private DealDetailStructuredDetailVO mockDetailVO;

    private static class TestTeaHouseBuilder extends AbstractTeaHouseDealStructuredDetailBuilder {

        @Override
        public DealDetailStructuredDetailVO buildPackageDetailsTitle(ProductAttr productAttr) {
            return null;
        }

        @Override
        public List<DealDetailStructuredDetailVO> buildPackageDetails(ProductAttr productAttr) {
            return null;
        }

        @Override
        public DealDetailStructuredDetailVO buildStructuredDetailVO(String title, String content, String subcontent, int type) {
            return super.buildStructuredDetailVO(title, content, subcontent, type);
        }
    }

    /**
     * Test when both result and addition lists are valid, with newLine=true
     * Verifies:
     * 1. Addition list items are added to result
     * 2. New line is added at the end
     * 3. Final list contains correct number of items
     */
    @Test
    public void testSafeAddAllWithNewLine_ValidListsWithNewLine() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        List<DealDetailStructuredDetailVO> addition = new ArrayList<>();
        DealDetailStructuredDetailVO item = DealDetailStructuredDetailVO.builder().content("test content").type(1).build();
        addition.add(item);
        // act
        builder.safeAddAllWithNewLine(result, addition, true);
        // assert
        assertEquals(2, result.size());
        assertEquals(item, result.get(0));
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_NEW_LINE.getType(), result.get(1).getType());
    }

    /**
     * Test when addition list is empty but newLine is true
     * Verifies:
     * 1. No items are added to result list
     * 2. No new line is added when result is empty
     */
    @Test
    public void testSafeAddAllWithNewLine_EmptyAdditionWithNewLine() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        List<DealDetailStructuredDetailVO> addition = new ArrayList<>();
        // act
        builder.safeAddAllWithNewLine(result, addition, true);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when result already contains items and newLine is true
     * Verifies:
     * 1. Existing items in result remain unchanged
     * 2. New line is added at the end
     */
    @Test
    public void testSafeAddAllWithNewLine_ExistingResultWithNewLine() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        DealDetailStructuredDetailVO existingItem = DealDetailStructuredDetailVO.builder().content("existing").type(1).build();
        result.add(existingItem);
        List<DealDetailStructuredDetailVO> addition = new ArrayList<>();
        DealDetailStructuredDetailVO newItem = DealDetailStructuredDetailVO.builder().content("new").type(1).build();
        addition.add(newItem);
        // act
        builder.safeAddAllWithNewLine(result, addition, true);
        // assert
        assertEquals(3, result.size());
        assertEquals(existingItem, result.get(0));
        assertEquals(newItem, result.get(1));
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_NEW_LINE.getType(), result.get(2).getType());
    }

    /**
     * Test when result is null
     * Verifies:
     * 1. Method handles null result gracefully
     * 2. No exceptions are thrown
     */
    @Test
    public void testSafeAddAllWithNewLine_NullResult() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> addition = new ArrayList<>();
        addition.add(DealDetailStructuredDetailVO.builder().content("test").build());
        // act & assert
        assertDoesNotThrow(() -> builder.safeAddAllWithNewLine(null, addition, true));
    }

    /**
     * Test when addition is null
     * Verifies:
     * 1. Method handles null addition gracefully
     * 2. Result list remains unchanged
     * 3. No new line is added when addition is null
     */
    @Test
    public void testSafeAddAllWithNewLine_NullAddition() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        DealDetailStructuredDetailVO existingItem = DealDetailStructuredDetailVO.builder().content("existing").type(1).build();
        result.add(existingItem);
        // act
        // Changed newLine to false
        builder.safeAddAllWithNewLine(result, null, false);
        // assert
        assertEquals(1, result.size());
        assertEquals(existingItem, result.get(0));
    }
}
