package com.sankuai.dzshoppingguide.product.detail.application.builder.review;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.DpReserveShopReview;
import java.lang.reflect.Field;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

class DpReserveReviewModuleBuilderTest {

    private DpReserveReviewModuleBuilder builder;

    private ProductDetailPageRequest mockRequest;

    @BeforeEach
    void setUp() throws Exception {
        builder = new DpReserveReviewModuleBuilder();
        mockRequest = Mockito.mock(ProductDetailPageRequest.class);
        // 使用反射设置protected字段
        Class<?> currentClass = builder.getClass();
        Field requestField = null;
        while (currentClass != null && requestField == null) {
            try {
                requestField = currentClass.getDeclaredField("request");
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        if (requestField != null) {
            requestField.setAccessible(true);
            requestField.set(builder, mockRequest);
        } else {
            throw new RuntimeException("Could not find 'request' field in class hierarchy");
        }
    }

    /**
     * 测试美团客户端类型场景，应返回null
     */
    @Test
    public void testDoBuildWhenMtClientType() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        DpReserveShopReview result = builder.doBuild();
        // assert
        assertNull(result);
    }

    /**
     * 测试poiId为0的场景，应返回null
     */
    @Test
    public void testDoBuildWhenPoiIdIsZero() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(mockRequest.getPoiId()).thenReturn(0L);
        // act
        DpReserveShopReview result = builder.doBuild();
        // assert
        assertNull(result);
    }

    /**
     * 测试poiId为负数的场景，应返回null
     */
    @Test
    public void testDoBuildWhenPoiIdIsNegative() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(mockRequest.getPoiId()).thenReturn(-1L);
        // act
        DpReserveShopReview result = builder.doBuild();
        // assert
        assertNull(result);
    }

    /**
     * 测试正常场景，应返回填充好的DpReserveShopReview对象
     */
    @Test
    public void testDoBuildWhenNormalCase() throws Throwable {
        // arrange
        long poiId = 12345L;
        when(mockRequest.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(mockRequest.getPoiId()).thenReturn(poiId);
        // act
        DpReserveShopReview result = builder.doBuild();
        // assert
        assertNotNull(result);
        assertEquals("用户评价", result.getModuleName());
        assertEquals("店铺点评", result.getContent());
        assertEquals("查看全部点评", result.getMoreText());
        assertEquals(String.format("dianping://review?referid=%d", poiId), result.getMoreUrl());
    }

    /**
     * 测试客户端类型为null的场景，预期会抛出NullPointerException
     */
    @Test
    public void testDoBuildWhenClientTypeIsNull() throws Throwable {
        // arrange
        when(mockRequest.getClientTypeEnum()).thenReturn(null);
        when(mockRequest.getPoiId()).thenReturn(12345L);
        // act & assert
        assertThrows(NullPointerException.class, () -> builder.doBuild());
    }
}
