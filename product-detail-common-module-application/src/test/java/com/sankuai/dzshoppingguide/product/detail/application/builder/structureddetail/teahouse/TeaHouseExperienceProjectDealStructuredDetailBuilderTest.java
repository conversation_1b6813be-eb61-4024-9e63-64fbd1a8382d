package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;

@ExtendWith(MockitoExtension.class)
public class TeaHouseExperienceProjectDealStructuredDetailBuilderTest {

    @InjectMocks
    private TeaHouseExperienceProjectDealStructuredDetailBuilder builder;

    @Mock
    private ProductAttr productAttr;

    /**
     * Test case for null ProductAttr input
     */
    @Test
    public void testBuildPackageDetails_NullProductAttr() throws Throwable {
        // arrange
        ProductAttr nullAttr = null;
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(nullAttr);
        // assert
        assertNull(result);
    }

    /**
     * Test case for project type only
     */
    @Test
    public void testBuildPackageDetails_ProjectTypeOnly() throws Throwable {
        // arrange
        // Mock all possible method calls
        when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("ProjectType")).thenReturn("标准项目");
        when(productAttr.getSafeString("DurationUnit", "")).thenReturn("");
        when(productAttr.getSkuAttrValue("freeFood")).thenReturn(null);
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("项目类型", result.get(0).getTitle());
        assertEquals("标准项目", result.get(0).getContent());
    }

    /**
     * Test case for duration with unit
     */
    @Test
    public void testBuildPackageDetails_DurationWithUnit() throws Throwable {
        // arrange
        // Mock all possible method calls
        when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn("60");
        when(productAttr.getSafeString("DurationUnit", "")).thenReturn("分钟");
        when(productAttr.getSkuAttrValue("freeFood")).thenReturn(null);
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("体验时长", result.get(0).getTitle());
        assertEquals("60分钟", result.get(0).getContent());
    }

    /**
     * Test case for applicable users
     */
    @Test
    public void testBuildPackageDetails_ApplicableUsers() throws Throwable {
        // arrange
        // Mock all possible method calls
        when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("fixed_people_amount_gate")).thenReturn("2");
        when(productAttr.getSafeString("DurationUnit", "")).thenReturn("");
        when(productAttr.getSkuAttrValue("freeFood")).thenReturn(null);
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("使用人数", result.get(0).getTitle());
        assertEquals("2人", result.get(0).getContent());
    }

    /**
     * Test case for free food list
     */
    @Test
    public void testBuildPackageDetails_FreeFoodList() throws Throwable {
        // arrange
        // Mock all possible method calls
        when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn(null);
        when(productAttr.getSafeString("DurationUnit", "")).thenReturn("");
        List<String> freeFood = Arrays.asList("茶点", "水果", "小吃");
        when(productAttr.getSkuAttrValue("freeFood")).thenReturn(freeFood);
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("免费餐食", result.get(0).getTitle());
        assertEquals("茶点、水果、小吃", result.get(0).getContent());
    }

    /**
     * Test case for additional services
     */
    @Test
    public void testBuildPackageDetails_AdditionalServices() throws Throwable {
        // arrange
        // Mock all possible method calls
        when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("px_additional_service")).thenReturn("免费停车");
        when(productAttr.getSafeString("DurationUnit", "")).thenReturn("");
        when(productAttr.getSkuAttrValue("freeFood")).thenReturn(null);
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("附加服务", result.get(0).getTitle());
        assertEquals("免费停车", result.get(0).getContent());
    }

    /**
     * Test case for all fields present
     */
    @Test
    public void testBuildPackageDetails_AllFieldsPresent() throws Throwable {
        // arrange
        // Mock all possible method calls
        when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("ProjectType")).thenReturn("高级项目");
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn("90");
        when(productAttr.getSafeString("DurationUnit", "")).thenReturn("分钟");
        when(productAttr.getSkuAttrFirstValue("fixed_people_amount_gate")).thenReturn("4");
        when(productAttr.getSkuAttrValue("freeFood")).thenReturn(Arrays.asList("茶水", "点心"));
        when(productAttr.getSkuAttrFirstValue("px_additional_service")).thenReturn("免费WiFi");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertNotNull(result);
        assertEquals(5, result.size());
        // Verify each field in order
        assertEquals("项目类型", result.get(0).getTitle());
        assertEquals("高级项目", result.get(0).getContent());
        assertEquals("体验时长", result.get(1).getTitle());
        assertEquals("90分钟", result.get(1).getContent());
        assertEquals("使用人数", result.get(2).getTitle());
        assertEquals("4人", result.get(2).getContent());
        assertEquals("免费餐食", result.get(3).getTitle());
        assertEquals("茶水、点心", result.get(3).getContent());
        assertEquals("附加服务", result.get(4).getTitle());
        assertEquals("免费WiFi", result.get(4).getContent());
    }

    @Test
    public void testBuildPackageDetailsTitleReturnsCorrectVO() throws Throwable {
        // arrange
        TeaHouseExperienceProjectDealStructuredDetailBuilder builder = new TeaHouseExperienceProjectDealStructuredDetailBuilder();
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(mockProductAttr);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals("套餐详情", result.getTitle(), "标题应设置为'套餐详情'");
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "类型应设置为DETAIL_TYPE_11对应的值");
    }

    @Test
    public void testBuildPackageDetailsTitleIndependentOfInput() throws Throwable {
        // arrange
        TeaHouseExperienceProjectDealStructuredDetailBuilder builder = new TeaHouseExperienceProjectDealStructuredDetailBuilder();
        ProductAttr mockProductAttr1 = mock(ProductAttr.class);
        ProductAttr mockProductAttr2 = mock(ProductAttr.class);
        // act
        DealDetailStructuredDetailVO result1 = builder.buildPackageDetailsTitle(mockProductAttr1);
        DealDetailStructuredDetailVO result2 = builder.buildPackageDetailsTitle(mockProductAttr2);
        DealDetailStructuredDetailVO result3 = builder.buildPackageDetailsTitle(null);
        // assert
        assertEquals(result1.getTitle(), result2.getTitle(), "不同mock参数应返回相同标题");
        assertEquals(result1.getType(), result2.getType(), "不同mock参数应返回相同类型");
        assertEquals(result1.getTitle(), result3.getTitle(), "null参数应返回相同标题");
        assertEquals(result1.getType(), result3.getType(), "null参数应返回相同类型");
    }

    @Test
    public void testBuildPackageDetailsTitleDoesNotUseInput() throws Throwable {
        // arrange
        TeaHouseExperienceProjectDealStructuredDetailBuilder builder = new TeaHouseExperienceProjectDealStructuredDetailBuilder();
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        // act
        builder.buildPackageDetailsTitle(mockProductAttr);
        // assert
        verifyZeroInteractions(mockProductAttr);
    }

    @Test
    public void testBuildPackageDetailsTitleReturnsNewInstanceEachTime() throws Throwable {
        // arrange
        TeaHouseExperienceProjectDealStructuredDetailBuilder builder = new TeaHouseExperienceProjectDealStructuredDetailBuilder();
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        // act
        DealDetailStructuredDetailVO result1 = builder.buildPackageDetailsTitle(mockProductAttr);
        DealDetailStructuredDetailVO result2 = builder.buildPackageDetailsTitle(mockProductAttr);
        // assert
        assertNotSame(result1, result2, "每次调用应返回新的实例");
    }
}
