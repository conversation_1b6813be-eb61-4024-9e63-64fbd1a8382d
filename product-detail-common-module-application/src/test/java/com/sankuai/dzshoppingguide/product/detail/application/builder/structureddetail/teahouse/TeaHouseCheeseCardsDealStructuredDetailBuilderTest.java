package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.DisplayName;

@ExtendWith(MockitoExtension.class)
@DisplayName("TeaHouseCheeseCardsDealStructuredDetailBuilder Tests")
class TeaHouseCheeseCardsDealStructuredDetailBuilderTest {

    @InjectMocks
    private TeaHouseCheeseCardsDealStructuredDetailBuilder builder;

    @Mock
    private ProductAttr productAttr;

    /**
     * 测试buildPackageDetailsTitle方法返回的VO对象不为null
     */
    @Test
    public void testBuildPackageDetailsTitleReturnsNonNull() {
        // arrange
        TeaHouseCheeseCardsDealStructuredDetailBuilder builder = new TeaHouseCheeseCardsDealStructuredDetailBuilder();
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(mockProductAttr);
        // assert
        assertNotNull(result, "返回的DealDetailStructuredDetailVO不应为null");
    }

    /**
     * 测试buildPackageDetailsTitle方法返回的VO标题正确设置为"套餐详情"
     */
    @Test
    public void testBuildPackageDetailsTitleHasCorrectTitle() {
        // arrange
        TeaHouseCheeseCardsDealStructuredDetailBuilder builder = new TeaHouseCheeseCardsDealStructuredDetailBuilder();
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(mockProductAttr);
        // assert
        assertEquals("套餐详情", result.getTitle(), "标题应设置为'套餐详情'");
    }

    /**
     * 测试buildPackageDetailsTitle方法返回的VO类型正确设置为DETAIL_TYPE_11
     */
    @Test
    public void testBuildPackageDetailsTitleHasCorrectType() {
        // arrange
        TeaHouseCheeseCardsDealStructuredDetailBuilder builder = new TeaHouseCheeseCardsDealStructuredDetailBuilder();
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(mockProductAttr);
        // assert
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "类型应设置为DETAIL_TYPE_11对应的值");
    }

    /**
     * 测试buildPackageDetailsTitle方法不依赖输入参数，即使传入null也能正常工作
     */
    @Test
    public void testBuildPackageDetailsTitleWorksWithNullInput() {
        // arrange
        TeaHouseCheeseCardsDealStructuredDetailBuilder builder = new TeaHouseCheeseCardsDealStructuredDetailBuilder();
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(null);
        // assert
        assertNotNull(result, "即使输入为null也应返回非null结果");
        assertEquals("套餐详情", result.getTitle(), "标题应保持为'套餐详情'");
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "类型应保持为DETAIL_TYPE_11对应的值");
    }

    @Test
    @DisplayName("Should build package details title with valid ProductAttr")
    void testBuildPackageDetailsTitleWithValidInput() {
        // arrange - setup done by Mockito annotations
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(productAttr);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals("套餐详情", result.getTitle(), "Title should be '套餐详情'");
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "Type should match DETAIL_TYPE_11");
    }

    @Test
    @DisplayName("Should build package details title with null ProductAttr")
    void testBuildPackageDetailsTitleWithNullInput() {
        // arrange - using null instead of mocked ProductAttr
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(null);
        // assert
        assertNotNull(result, "Result should not be null even with null input");
        assertEquals("套餐详情", result.getTitle(), "Title should be '套餐详情'");
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "Type should match DETAIL_TYPE_11");
    }

    @Test
    @DisplayName("Should return consistent results across multiple calls")
    void testBuildPackageDetailsTitleConsistency() {
        // arrange
        DealDetailStructuredDetailVO firstResult = builder.buildPackageDetailsTitle(productAttr);
        // act
        DealDetailStructuredDetailVO secondResult = builder.buildPackageDetailsTitle(productAttr);
        // assert
        assertNotNull(firstResult, "First result should not be null");
        assertNotNull(secondResult, "Second result should not be null");
        assertEquals(firstResult.getTitle(), secondResult.getTitle(), "Titles should be consistent across calls");
        assertEquals(firstResult.getType(), secondResult.getType(), "Types should be consistent across calls");
    }

    @Test
    @DisplayName("Should build complete DealDetailStructuredDetailVO structure")
    void testBuildPackageDetailsTitleStructure() {
        // arrange - setup done by Mockito annotations
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(productAttr);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals("套餐详情", result.getTitle(), "Title should be set correctly");
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "Type should be set correctly");
        // Verify no unexpected fields are set
        assertEquals(null, result.getContent(), "Content should not be set");
        assertEquals(null, result.getSubContent(), "SubContent should not be set");
        assertEquals(null, result.getDetail(), "Detail should not be set");
    }
}
