# 产品详情页眼镜口腔行业功能需求技术实现方案

## 1. 需求分析总结

基于`requirement_new.md`的需求分析，本次需要实现以下核心功能：

### 1.1 眼镜行业功能需求
- **质保信息单位属性功能**：支持镜片/镜框/产品质保的数值和单位分离展示
- **取镜时间类型扩展功能**：支持4种取镜时间类型的灵活展示
- **验光操作人员多选功能**：支持单选/多选模式，包含门店提示
- **镜片技术字段多选功能**：支持技术字段的多选展示
- **检查时长范围值功能**：支持时长的范围展示和总时长计算

### 1.2 口腔行业功能需求
- **补牙材料科普信息功能**：3M树脂材料的科普信息入口
- **服务时长范围功能**：支持服务时长的范围展示
- **套餐包含属性功能**：特定类目的套餐包含内容展示

## 2. 架构分析与设计方案

### 2.1 现有架构分析

基于`index.md`的架构分析，当前系统采用DDD架构，核心组件包括：

- **FacialDealStructuredDetailBuilder**：眼镜、眼科、口腔行业的统一构建器
- **AbstractDealDetailBuilder**：抽象构建器，支持模块化扩展
- **DealDetailBuildContext**：构建上下文，包含所有必要数据
- **ProductAttr**：产品属性数据模型
- **EyesAttrUtils/DealDetailStructuredUtils**：属性处理工具类

### 2.2 技术实现路径

#### 2.2.1 属性处理增强
在现有的属性处理基础上，扩展多选字段和范围值处理能力：

```java
// 多选字段处理模式
public static String handleMultiSelectAttr(ProductAttr productAttr, String attrName, String delimiter, String suffix) {
    List<String> attrValues = productAttr.getSkuAttrValues(attrName);
    if (CollectionUtils.isEmpty(attrValues)) {
        return null;
    }
    if (attrValues.size() == 1) {
        return attrValues.get(0);
    }
    return String.join(delimiter, attrValues) + suffix;
}

// 时长范围处理模式
public static String handleDurationRange(String minValue, String maxValue, String unit) {
    if (StringUtils.isBlank(minValue)) {
        return null;
    }
    if (StringUtils.isBlank(maxValue) || minValue.equals(maxValue)) {
        return minValue + unit;
    }
    return minValue + "-" + maxValue + unit;
}
```

#### 2.2.2 质保信息处理逻辑
新增质保信息构建器，支持数值和单位的组合展示：

```java
public static Optional<DealDetailStructuredDetailVO> buildWarrantyInfo(ProductAttr productAttr) {
    // 镜片/镜框质保组合展示
    String lensAssure = productAttr.getSkuAttrFirstValue("lens_assure");
    String frameWarranty = productAttr.getSkuAttrFirstValue("frame_warranty");
    String lensUnit = productAttr.getSkuAttrFirstValue("LensQualityAssuranceUnit");
    String frameUnit = productAttr.getSkuAttrFirstValue("FrameQualityUnit");
    
    if (StringUtils.isNotBlank(lensAssure) && StringUtils.isNotBlank(frameWarranty)) {
        String lensUnitDisplay = StringUtils.isBlank(lensUnit) ? "年" : lensUnit;
        String frameUnitDisplay = StringUtils.isBlank(frameUnit) ? "年" : frameUnit;
        String content = String.format("镜片%s%s质保，镜框%s%s质保", 
            lensAssure, lensUnitDisplay, frameWarranty, frameUnitDisplay);
        return DealDetailStructuredUtils.buildTitleAndContent("质保信息", content);
    }
    
    // 产品质保独立展示
    String productWarranty = productAttr.getSkuAttrFirstValue("product_warranty");
    String productUnit = productAttr.getSkuAttrFirstValue("ProductQualityAssuranceUnit");
    if (StringUtils.isNotBlank(productWarranty)) {
        String unitDisplay = StringUtils.isBlank(productUnit) ? "年" : productUnit;
        String content = productWarranty + unitDisplay + "质保";
        return DealDetailStructuredUtils.buildTitleAndContent("质保信息", content);
    }
    
    return Optional.empty();
}
```

## 3. 具体实现方案

### 3.1 新增工具类和方法

#### 3.1.1 扩展EyesAttrUtils类
在`EyesAttrUtils`中新增以下方法：

1. **质保信息处理**：`buildWarrantyInfo(ProductAttr productAttr)`
2. **取镜时间处理**：`buildAcquireTime(ProductAttr productAttr)`
3. **验光操作人员处理**：`buildOptometrist(ProductAttr productAttr, Long shopCount)`
4. **镜片技术多选处理**：`buildLensTechnology(ProductAttr productAttr)`
5. **检查时长范围处理**：`buildExaminationDuration(ProductAttr productAttr)`

#### 3.1.2 新增DentalAttrUtils类
创建口腔行业专用的属性处理工具类：

1. **材料科普信息处理**：`buildMaterialInfo(ProductAttr productAttr)`
2. **服务时长范围处理**：`buildServiceDuration(ProductAttr productAttr)`
3. **套餐包含属性处理**：`buildPackageIncludes(ProductAttr productAttr)`

### 3.2 构建器模块扩展

#### 3.2.1 眼镜行业构建器增强
在现有的`KeyInformationBuilder`和`ExcelInfoBuilder`中集成新功能：

```java
// KeyInformationBuilder中新增质保信息展示
private List<DealDetailStructuredDetailVO> buildWarrantySection(DealDetailBuildContext context) {
    List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
    
    // 质保信息
    EyesAttrUtils.buildWarrantyInfo(context.getProductAttr())
        .ifPresent(result::add);
    
    // 取镜时间
    EyesAttrUtils.buildAcquireTime(context.getProductAttr())
        .ifPresent(result::add);
        
    return result;
}
```

#### 3.2.2 口腔行业构建器新增
创建`DentalKeyInfoStrategy`类，处理口腔行业特有的展示逻辑：

```java
@Component
public class DentalKeyInfoStrategy extends AbstractSubModuleBuildStrategy {
    
    @Override
    public boolean isHit(DealDetailBuildContext context) {
        return context.getProductCategory().getProductSecondCategoryId() == 
            SecondCategoryEnum.ORAL_DENTAL.getSecondCategoryId();
    }
    
    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        
        // 材料科普信息
        DentalAttrUtils.buildMaterialInfo(context.getProductAttr())
            .ifPresent(result::add);
            
        // 套餐包含属性
        DentalAttrUtils.buildPackageIncludes(context.getProductAttr())
            .ifPresent(result::add);
            
        return result;
    }
}
```

### 3.3 配置管理扩展

#### 3.3.1 Lion配置新增
在`LionConstants`中新增配置项：

```java
// 材料对比图片配置
String MATERIAL_COMPARISON_PIC = "com.sankuai.dzshoppingguide.detail.commonmodule.material.comparison.pic";

// 眼镜质保信息适用类目
String GLASSES_WARRANTY_CATEGORY_IDS = "com.sankuai.dzshoppingguide.detail.commonmodule.glasses.warranty.category.ids";

// 口腔套餐包含配置
String DENTAL_PACKAGE_INCLUDES_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.dental.package.includes.config";
```

#### 3.3.2 LionConfigUtils扩展
新增配置读取方法：

```java
public static Map<String, String> getMaterialComparisonPicConfig() {
    return Lion.getMap(LionConstants.APP_KEY, LionConstants.MATERIAL_COMPARISON_PIC, 
        String.class, Collections.emptyMap());
}

public static List<Integer> getGlassesWarrantyCategoryIds() {
    return Lion.getList(LionConstants.APP_KEY, LionConstants.GLASSES_WARRANTY_CATEGORY_IDS, 
        Integer.class, Collections.emptyList());
}
```

## 4. 数据流程设计

### 4.1 数据获取流程
```
ProductAttrFetcher → ProductAttr → 属性解析 → 业务逻辑处理 → VO构建 → 前端展示
```

### 4.2 多选字段处理流程
```
属性值数组 → 判断数量 → 单选直接展示/多选拼接 → 添加后缀提示 → 最终展示文案
```

### 4.3 时长范围处理流程
```
最小值/最大值 → 范围判断 → 单值/范围展示 → 总时长计算 → 四舍五入处理
```

## 5. 风险评估与解决方案

### 5.1 技术风险

#### 5.1.1 属性字段兼容性风险
**风险描述**：新增属性字段可能与现有字段冲突
**解决方案**：
- 使用新的属性字段名称，避免与现有字段冲突
- 在属性处理时进行空值检查和兼容性处理
- 提供降级方案，新字段为空时使用原有逻辑

#### 5.1.2 性能影响风险
**风险描述**：新增属性处理逻辑可能影响接口性能
**解决方案**：
- 属性处理逻辑优化，避免重复计算
- 使用缓存机制缓存计算结果
- 异步处理非关键属性信息

### 5.2 业务风险

#### 5.2.1 展示逻辑复杂性风险
**风险描述**：多种展示模式可能导致逻辑复杂，维护困难
**解决方案**：
- 使用策略模式分离不同的展示逻辑
- 提供统一的配置管理机制
- 完善单元测试覆盖各种场景

#### 5.2.2 数据一致性风险
**风险描述**：属性数据可能存在不一致或缺失
**解决方案**：
- 在属性处理时进行数据校验
- 提供默认值和降级展示方案
- 记录异常情况用于监控和优化

## 6. 实施计划

### 6.1 第一阶段：基础设施建设（1-2天）
- 扩展EyesAttrUtils工具类
- 新增DentalAttrUtils工具类
- 完善Lion配置管理

### 6.2 第二阶段：核心功能实现（3-4天）
- 实现质保信息处理逻辑
- 实现取镜时间扩展功能
- 实现验光操作人员多选功能
- 实现镜片技术多选功能

### 6.3 第三阶段：口腔功能实现（2-3天）
- 实现材料科普信息功能
- 实现服务时长范围功能
- 实现套餐包含属性功能

### 6.4 第四阶段：测试与优化（2-3天）
- 单元测试编写
- 集成测试验证
- 性能测试和优化

## 7. 监控与运维

### 7.1 关键指标监控
- 属性处理成功率
- 接口响应时间
- 异常情况统计

### 7.2 日志记录
- 属性解析异常日志
- 业务逻辑处理日志
- 性能监控日志

### 7.3 降级方案
- 新功能异常时回退到原有逻辑
- 配置开关控制功能启用
- 灰度发布验证功能稳定性

## 8. 总结

本方案基于现有的DDD架构，通过扩展属性处理工具类和构建器模块，实现了眼镜和口腔行业的特定功能需求。方案具有以下特点：

1. **最小侵入性**：在现有架构基础上扩展，不破坏原有逻辑
2. **高可扩展性**：使用策略模式和配置化管理，便于后续扩展
3. **向后兼容**：提供降级方案，确保系统稳定性
4. **可维护性**：模块化设计，职责清晰，便于维护

该方案能够满足需求文档中的所有功能要求，同时保持系统的稳定性和可扩展性。
