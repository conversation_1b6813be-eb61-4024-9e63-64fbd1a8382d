# Set the default logging level to INFO
.level = SEVERE

# Specify the handlers that will handle the log messages
handlers = java.util.logging.ConsoleHandler

# Configure the ConsoleHandler
java.util.logging.ConsoleHandler.level = ALL
java.util.logging.ConsoleHandler.formatter = java.util.logging.SimpleFormatter

# Configure the FileHandler
java.util.logging.FileHandler.pattern = log.txt
java.util.logging.FileHandler.limit = 0
java.util.logging.FileHandler.count = 1
java.util.logging.FileHandler.formatter = java.util.logging.SimpleFormatter

# Set the logging level for the FileHandler to ALL
java.util.logging.FileHandler.level = ALL

# Configure the SimpleFormatter to include date, source, logger name, level, and message
# java.util.logging.SimpleFormatter.format = %1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS %4$s %2$s %5$s%6$s%n
java.util.logging.SimpleFormatter.format=%5$s %6$s%n
# %1$tF: Date in ISO 8601 format (e.g., 2023-10-05).
# %1$tT: Time in 24-hour format (e.g., 12:34:56).
# %4$s: Log level (e.g., INFO).
# %3$s: Method name (e.g., main).
# %5$s: Log message.
# %6$s: Thrown exception (if any).
# %n: Newline.

org.bsc.langgraph4j.level = FINEST
