package com.dianping.tpfun.mapi.shop.widget;

import com.alibaba.fastjson.JSON;
import com.dianping.tpfun.mapi.AbstractTestObject;
import com.dianping.tpfun.mapi.dto.shop.KTVShopInfoWidgetDO;
import com.dianping.tpfun.mapi.dto.shop.list.ShopListDo;
import com.dianping.tpfun.mapi.shop.widget.shopinfo.GetCommonShopInfoAgent;
import com.dianping.tpfun.mapi.zhbook.biz.BaseShopBiz;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created with IntelliJ IDEA.
 * User: jy
 * Date: 2017/12/1
 * Time: 下午9:27
 * To change this template use File | Settings | File Templates.
 */
public class GetCommonShopInfoAgentTest extends AbstractTestObject {

    @Autowired
    private GetCommonShopInfoAgent getCommonShopInfoAgent;

    @Autowired
    private BaseShopBiz baseShopBiz;

    @Test
    public void testGetShopListDo() {
        ShopListDo shopListDo = baseShopBiz.getShopListDo(Lists.newArrayList(73591142L, 5588410L), 0, 10, new Double(11), new Double(21));
        System.out.println(JSON.toJSONString(shopListDo));
    }

    @Test
    public void test(){
        KTVShopInfoWidgetDO ktvShopInfoWidgetDO= getCommonShopInfoAgent.getCommonShopInfo(21289332);
        String a="fsafsfsad\n" +
                "\n" +
                "fdsfsd";
        System.out.println(StringUtils.replace(a,"\n","XXXXX"));
        if(ktvShopInfoWidgetDO==null){
            System.out.println("null");
        }
        System.out.println(JSON.toJSON(ktvShopInfoWidgetDO));
    }
}
