package com.dianping.tpfun.mapi.ktv.refund.widget;

import com.dianping.tpfun.mapi.AbstractTestObject;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Map;


/**
 * Created by wangzi07 on 2018/2/28.
 */
public class KtvRefundDetailWidgetTest extends AbstractTestObject {

    @Resource
    private KtvRefundDetailWidget detailWidget;

    @Test
    public void testGetWidget() throws Exception {
        Map<String, Object> map = detailWidget.getWidget("151973310274229400440128", 1);
        Assert.assertNotNull(map);
    }
}