package com.dianping.tpfun.mapi.framework;

import com.dianping.standardapi4node.Request;
import com.dianping.tpfun.mapi.AbstractTestObject;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: jy
 * Date: 2018/1/11
 * Time: 上午11:47
 * To change this template use File | Settings | File Templates.
 */
public class NodeServiceTest extends AbstractTestObject {

    @Autowired
    private TpfunMapiNodeRPCRouter tpfunMapiNodeRPCRouter;

    @Test
    public void testInvoke2() {

        Request request = new Request();
        request.setUri("dzbook/pintuandetail.json2");
        Map<String, String> paraMap = Maps.newHashMap();
        paraMap.put("pintuanid", "1036");
        request.setParams(paraMap);
        String s = tpfunMapiNodeRPCRouter.invoke2(request);
        System.out.println(s);


    }
}
