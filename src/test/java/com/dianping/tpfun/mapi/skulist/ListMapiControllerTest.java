package com.dianping.tpfun.mapi.skulist;

import com.alibaba.fastjson.JSON;
import com.dianping.api.AbstractTestObject;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.Platform;
import com.dianping.mobile.base.datatypes.enums.Product;
import com.dianping.mobile.framework.datatypes.MobileContext;
import com.dianping.tpfun.mapi.shopcart.widget.ShopCartWidget;
import com.dianping.tpfun.mapi.skulist.action.ListMapiController;
import com.dianping.tpfun.mapi.skulist.biz.ListBiz;
import com.dianping.tpfun.mapi.skulist.data.entities.ListContext;
import com.dianping.tpfun.product.api.common.KTVChannel;
import com.dianping.tpfun.product.api.common.KTVClientContext;
import com.dianping.tpfun.product.api.common.KTVClientType;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: yinshunming
 * @email: <EMAIL>
 * @date: 2022/7/15 2:18 下午
 * @description:
 */
public class ListMapiControllerTest extends AbstractTestObject {

    @Autowired
    private ListBiz listBiz;

    @Autowired
    @Qualifier("general_search_sku_skulist.bin")
    private ListMapiController listMapiController;

    @Test
    public void testQuery() {
        ListContext context = new ListContext();
        listBiz.query(context);
    }

    @Test
    public void testGetSkuList() {
        MobileContext mobileContext = new MobileContext();
        Map<String, String> paramtersMap = Maps.newHashMap();
        paramtersMap.put("shopidStr", String.valueOf(5304705));
        paramtersMap.put("channel","dp");
        paramtersMap.put("clientType", "m");
        paramtersMap.put("platform", "iphone");
        paramtersMap.put("items", "556");
        paramtersMap.put("bizname", "decomaterial");
        paramtersMap.put("lng", "117.252767");
        paramtersMap.put("lat", "39.139974");
        paramtersMap.put("start", "0");
        mobileContext.setParamtersMap(paramtersMap);

        ClientType clientContext = new ClientType(Platform.Android, Product.MTMAIN_APP);

        mobileContext.setClient(clientContext);

        listMapiController.setMobileContext(mobileContext);
        Object ob = listMapiController.getSkuList();
        System.out.println(JSON.toJSONString(ob));
    }
}
