package com.dianping.tpfun.mapi.bizmanager.widget;

import com.dianping.tpfun.mapi.AbstractTestObject;
import com.dianping.tpfun.mapi.bizmanager.dto.KTVBizManagerComplaintAuditResultMsg;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.Assert.assertNotNull;

public class KTVBusinessManagerComplaintMsgWidgetTest extends AbstractTestObject {

    @Resource
    private KTVBizManagerComplaintAuditResultMsgWidget ktvBizManagerComplaintAuditResultMsgWidget;


    @Test
    public void bindShopWidget() throws Exception {
        KTVBizManagerComplaintAuditResultMsg ktvBizManagerComplaintAuditResultMsg = ktvBizManagerComplaintAuditResultMsgWidget.getWidget(37750678, 29);
        assertNotNull(ktvBizManagerComplaintAuditResultMsg);
    }

    @Test
    public void unbindShopWidget() throws Exception {

    }

}