package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.json.facade.JsonFacade;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.tab.RelateDealsFactory;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RelateDealsTest extends GenericTest {

    @Mock
    private RelateDealsFactory relateDealsFactory;
    @InjectMocks
    private DealQueryFacade dealQueryFacade;

/*    @Test
    public void testBeautyHair() {

        RelatedDealsReq request = new RelatedDealsReq();
        request.setDealGroupId(403287260);
        request.setShopId(23005380);//点评门店
        //request.setShopId(152393402);//美团门店
        request.setShopUuid("");
        request.setCityId(10);

        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(ClientTypeEnum.dp_mainApp_ios.getType());//点评
        envCtx.setVersion("10.39.0");
        //envCtx.setClientType(ClientTypeEnum.mt_mainApp_ios.getType());//美团

        RelatedDeals relatedDeals = dealQueryFacade.queryRelatedDeals(request, envCtx);
        System.out.println(JsonFacade.serialize(relatedDeals));
    }*/

    @Test
    @Ignore
    public void testPet() {

        RelatedDealsReq request = new RelatedDealsReq();
        request.setDealGroupId(406288047);
        //request.setShopId(1739542);//点评门店
        request.setShopId(604169746);//美团门店
        request.setShopUuid("");
        request.setCityId(10);

        EnvCtx envCtx = new EnvCtx();
        //envCtx.setClientType(ClientTypeEnum.dp_mainApp_ios.getType());//点评
        //envCtx.setVersion("10.39.0");
        envCtx.setClientType(ClientTypeEnum.mt_mainApp_ios.getType());//美团
        envCtx.setVersion("11.4.400");

        RelatedDeals relatedDeals = null;
        try {
            relatedDeals = dealQueryFacade.queryRelatedDeals(request, envCtx);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(JsonFacade.serialize(relatedDeals));
    }

    @Test
    @Ignore
    public void testPhoto() {

        RelatedDealsReq request = new RelatedDealsReq();
        request.setDealGroupId(404606866);
        request.setShopId(428456689);//点评门店
        //request.setShopId(152393402);//美团门店
        request.setShopUuid("");
        request.setCityId(10);

        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(ClientTypeEnum.dp_mainApp_ios.getType());//点评
        envCtx.setVersion("10.39.0");
        //envCtx.setClientType(ClientTypeEnum.mt_mainApp_ios.getType());//美团

        RelatedDeals relatedDeals = dealQueryFacade.queryRelatedDeals(request, envCtx);
        System.out.println(JsonFacade.serialize(relatedDeals));

    }

}