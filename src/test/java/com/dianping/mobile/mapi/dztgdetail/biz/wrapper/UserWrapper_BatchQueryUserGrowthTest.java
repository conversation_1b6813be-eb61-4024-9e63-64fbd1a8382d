package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.cip.growth.mana.api.service.CipGrowthManaService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import static org.mockito.Mockito.*;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class UserWrapper_BatchQueryUserGrowthTest {

    @InjectMocks
    private UserWrapper userWrapper;

    @Mock
    private CipGrowthManaService cipGrowthServiceFuture;

    @Mock
    private Future<?> mockFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Tests the batchQueryUserGrowth method under exceptional conditions.
     */
    @Test
    public void testBatchQueryUserGrowthException() throws Throwable {
        // arrange
        List<Long> userIdList = Arrays.asList(1L, 2L, 3L);
        doThrow(new RuntimeException()).when(cipGrowthServiceFuture).batchLoadUserMana(userIdList);
        // act
        Future<?> result = userWrapper.batchQueryUserGrowth(userIdList);
        // assert
        assertNull("The result should be null on exception", result);
        verify(cipGrowthServiceFuture).batchLoadUserMana(userIdList);
    }
}
