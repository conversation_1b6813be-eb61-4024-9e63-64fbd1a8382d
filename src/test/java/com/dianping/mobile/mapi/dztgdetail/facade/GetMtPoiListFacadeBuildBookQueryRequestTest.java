package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.book.req.BookQueryRequest;
import com.dianping.deal.book.req.ShopBookDto;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.common.enums.PageType;
import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.Platform;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.ComButton;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GetMtPoiListFacadeBuildBookQueryRequestTest {

    @Mock
    private ClientType client;

    private GetMtPoiListFacade getMtPoiListFacade = new GetMtPoiListFacade();

    @Mock
    private IMobileContext iMobileContext;

    private BookQueryRequest invokePrivateMethod(String methodName, List<Long> mtShopIds) throws Exception {
        Method method = getMtPoiListFacade.getClass().getDeclaredMethod(methodName, List.class, IMobileContext.class);
        method.setAccessible(true);
        return (BookQueryRequest) method.invoke(getMtPoiListFacade, mtShopIds, iMobileContext);
    }

    private ComButton invokeGetRecallBtns(ShopBookDto shopBookDto) throws Exception {
        Method method = GetMtPoiListFacade.class.getDeclaredMethod("getRecallBtns", ShopBookDto.class);
        method.setAccessible(true);
        return (ComButton) method.invoke(getMtPoiListFacade, shopBookDto);
    }

    @Test
    public void testBuildBookQueryRequestWithEmptyMtShopIds() throws Throwable {
        // arrange
        when(iMobileContext.getClient()).thenReturn(client);
        when(client.getPlatform()).thenReturn(Platform.iPhone);
        // act
        BookQueryRequest result = invokePrivateMethod("buildBookQueryRequest", Collections.emptyList());
        // assert
        assertNotNull(result);
        assertTrue(result.getShopIdsLong().isEmpty());
        assertTrue(result.isIdIsMt());
        assertEquals(PageType.BOOK_SHOP_LIST.getType(), result.getPageType());
        assertEquals(ClientTypeEnum.mt_mainApp_ios.getType(), result.getClientType());
    }

    @Test
    public void testBuildBookQueryRequestWithNonEmptyMtShopIds() throws Throwable {
        // arrange
        when(iMobileContext.getClient()).thenReturn(client);
        when(client.getPlatform()).thenReturn(Platform.Android);
        // act
        BookQueryRequest result = invokePrivateMethod("buildBookQueryRequest", Arrays.asList(1L, 2L, 3L));
        // assert
        assertNotNull(result);
        assertEquals(Arrays.asList(1L, 2L, 3L), result.getShopIdsLong());
        assertTrue(result.isIdIsMt());
        assertEquals(PageType.BOOK_SHOP_LIST.getType(), result.getPageType());
        assertEquals(ClientTypeEnum.mt_mainApp_android.getType(), result.getClientType());
    }

    @Test
    public void testBuildBookQueryRequestWithClientAsIphone() throws Throwable {
        // arrange
        when(iMobileContext.getClient()).thenReturn(client);
        when(client.getPlatform()).thenReturn(Platform.iPhone);
        // act
        BookQueryRequest result = invokePrivateMethod("buildBookQueryRequest", Arrays.asList(1L, 2L, 3L));
        // assert
        assertNotNull(result);
        assertEquals(Arrays.asList(1L, 2L, 3L), result.getShopIdsLong());
        assertTrue(result.isIdIsMt());
        assertEquals(PageType.BOOK_SHOP_LIST.getType(), result.getPageType());
        assertEquals(ClientTypeEnum.mt_mainApp_ios.getType(), result.getClientType());
    }

    @Test
    public void testBuildBookQueryRequestWithClientAsAndroid() throws Throwable {
        // arrange
        when(iMobileContext.getClient()).thenReturn(client);
        when(client.getPlatform()).thenReturn(Platform.Android);
        // act
        BookQueryRequest result = invokePrivateMethod("buildBookQueryRequest", Arrays.asList(1L, 2L, 3L));
        // assert
        assertNotNull(result);
        assertEquals(Arrays.asList(1L, 2L, 3L), result.getShopIdsLong());
        assertTrue(result.isIdIsMt());
        assertEquals(PageType.BOOK_SHOP_LIST.getType(), result.getPageType());
        assertEquals(ClientTypeEnum.mt_mainApp_android.getType(), result.getClientType());
    }

    @Test
    public void testBuildBookQueryRequestWithClientAsOther() throws Throwable {
        // arrange
        when(iMobileContext.getClient()).thenReturn(client);
        when(client.getPlatform()).thenReturn(Platform.Unknow);
        // act
        BookQueryRequest result = invokePrivateMethod("buildBookQueryRequest", Arrays.asList(1L, 2L, 3L));
        // assert
        assertNotNull(result);
        assertEquals(Arrays.asList(1L, 2L, 3L), result.getShopIdsLong());
        assertTrue(result.isIdIsMt());
        assertEquals(PageType.BOOK_SHOP_LIST.getType(), result.getPageType());
        assertEquals(ClientTypeEnum.mt_wap.getType(), result.getClientType());
    }

    /**
     * 测试 getRecallBtns 方法，当 ShopBookDto 对象为 null 时
     */
    @Test
    public void testGetRecallBtnsWhenShopBookDtoIsNull() throws Throwable {
        // arrange
        ShopBookDto shopBookDto = null;
        // act
        ComButton result = invokeGetRecallBtns(shopBookDto);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 getRecallBtns 方法，当 ShopBookDto 对象不为 null，但 hasBook 属性为 false 时
     */
    @Test
    public void testGetRecallBtnsWhenHasBookIsFalse() throws Throwable {
        // arrange
        ShopBookDto shopBookDto = new ShopBookDto();
        shopBookDto.setHasBook(false);
        // act
        ComButton result = invokeGetRecallBtns(shopBookDto);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 getRecallBtns 方法，当 ShopBookDto 对象不为 null，且 hasBook 属性为 true 时
     */
    @Test
    public void testGetRecallBtnsWhenHasBookIsTrue() throws Throwable {
        // arrange
        ShopBookDto shopBookDto = new ShopBookDto();
        shopBookDto.setHasBook(true);
        shopBookDto.setBookActionName("Book");
        shopBookDto.setBookUrl("http://example.com");
        // act
        ComButton result = invokeGetRecallBtns(shopBookDto);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals("Book", result.getTitle());
        Assert.assertEquals("http://example.com", result.getClickUrl());
        Assert.assertEquals(ComButton.ActionEnum.REDIRECT.type, result.getAction());
    }
}
