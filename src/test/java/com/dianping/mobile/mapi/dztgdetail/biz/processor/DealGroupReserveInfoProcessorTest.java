package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DealGroupReserveInfoProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.MtDztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.entity.ShowReservationGrayConfig;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.thrift.TException;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/30
 */
@RunWith(MockitoJUnitRunner.class)
public class DealGroupReserveInfoProcessorTest {
    @InjectMocks
    private DealGroupReserveInfoProcessor dealGroupReserveInfoProcessor;
    @Mock
    private DouHuBiz douHuBiz;
    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }


    @Test
    public void testHitExpShowReservation() {
        ShowReservationGrayConfig grayConfig = JSON.parseObject("{\"categoryIds\":[1,2,3],\"category2ShopIds\":{\"1\":[1,2],\"2\":[3,4]},\"category2ExpId\":{\"mt1\":\"exp001\",\"dp1\":\"exp002\"}}\n", ShowReservationGrayConfig.class);
        grayConfig.setWhiteListMode(0);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("123");
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1);
        dealCtx.setChannelDTO(channelDTO);
        dealCtx.setMtLongShopId(100L);
        dealCtx.setDpLongShopId(1);

        List<AbConfig> abConfigs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("exp001");
        abConfig.setExpResult("b");
        abConfigs.add(abConfig);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(abConfigs);

        when(douHuBiz.getAbByUnionIdAndExpId(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(moduleAbConfig);
        Assert.assertTrue(dealGroupReserveInfoProcessor.hitShowReservation(grayConfig, dealCtx));
    }

    @Test
    public void testHitShopIdShowReservation() {
        ShowReservationGrayConfig grayConfig = JSON.parseObject("{\"categoryIds\":[1,2,3],\"category2ShopIds\":{\"1\":[1,2],\"2\":[3,4]},\"category2ExpId\":{\"mt1\":\"exp001\",\"dp1\":\"exp002\"}}\n", ShowReservationGrayConfig.class);
        grayConfig.setWhiteListMode(0);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("123");
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1);
        dealCtx.setChannelDTO(channelDTO);
        dealCtx.setMtLongShopId(1L);
        dealCtx.setDpLongShopId(100L);

        Assert.assertFalse(dealGroupReserveInfoProcessor.hitShowReservation(grayConfig, dealCtx));
    }

    @Test
    public void testCompute() throws TException {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("123");
        DealCtx dealCtx = new DealCtx(envCtx);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(304);
        dealCtx.setChannelDTO(channelDTO);
        dealCtx.setMtLongShopId(1L);
        dealCtx.setDpLongShopId(100L);

        dealCtx.getFutureCtx().setAdditionalDealGroupDtoFuture(CompletableFuture.completedFuture(new DealGroupDTO()));

        // 配置当Lion.getList被调用时的行为
        lionMockedStatic.when(() -> Lion.getList(eq("com.sankuai.dzu.tpbase.dztgdetailweb"), eq("com.sankuai.dzu.tpbase" +
                        ".dztgdetailweb.supportAdditionalCategory"), eq(Integer.class), any()))
                .thenReturn(Lists.newArrayList(304));


        lionMockedStatic.when(() -> Lion.getList(eq("com.sankuai.dzu.tpbase.dztgdetailweb"), eq("com.sankuai.dzu.tpbase" +
                        ".dztgdetailweb.category.additionalJumpUrl"), eq(DealGroupReserveInfoProcessor.AdditionalCategoryJumpUrl.class), any()))
                .thenReturn(mockAdditionalCategoryJumpUrl());

        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(new DealGroupDTO());
        // 在这里调用你的方法，该方法内部会调用Lion.getList
        dealGroupReserveInfoProcessor.process(dealCtx);

        // process方法无返回值
        Assert.assertEquals(dealCtx.getAdditionalInfo().getAdditionalJumpUrl(), "bb");
    }

    private List<DealGroupReserveInfoProcessor.AdditionalCategoryJumpUrl> mockAdditionalCategoryJumpUrl() {
        DealGroupReserveInfoProcessor.AdditionalCategoryJumpUrl url = new DealGroupReserveInfoProcessor.AdditionalCategoryJumpUrl();
        url.setCategoryId(304);
        url.setShelfSceneCode("aa");
        Map<Integer, String> jumpUrlMap = new HashMap<>();
        jumpUrlMap.put(1, "bb");
        url.setJumpUrlMap(jumpUrlMap);
        return Lists.newArrayList(url);
    }

    @Test
    public void buildReserveRedirectUrlIsMtUrlTest() throws Exception {
        Method method = DealGroupReserveInfoProcessor.class.getDeclaredMethod("buildReserveRedirectUrl", DealCtx.class);
        method.setAccessible(true);
        DealGroupBaseDTO dealGroupBase = new DealGroupBaseDTO();
        dealGroupBase.setDealGroupId(1);
        dealGroupBase.setDealGroupType(1);
        dealGroupBase.setProductTitle("123");
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.setDealGroupBase(dealGroupBase);
        lionMockedStatic.when(() -> Lion.getString(anyString(),anyString())).thenReturn("imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-le-deal-reserve&mrn_component=reservepopup&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&isTransparent=true&hideLoading=true&pageFrom=poiShelf&dealId=${dealGroupId}&shopId=${shopId}&dealTitle=${dealTitle}");
        boolean result ;

        try {
            method.invoke(dealGroupReserveInfoProcessor, ctx);
            envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
            method.invoke(dealGroupReserveInfoProcessor, ctx);
            ctx.setDealGroupBase(null);
            method.invoke(dealGroupReserveInfoProcessor, ctx);
            DealBaseReq dealBaseReq = new DealBaseReq();
            dealBaseReq.setDealgroupid(1);
            DztgShareModule dztgShareModule = new DztgShareModule();
            MtDztgShareModule mtDztgShareModule = new MtDztgShareModule();
            mtDztgShareModule.setBrandName("123");
            dztgShareModule.setMt(mtDztgShareModule);
            ctx.setDztgShareModule(dztgShareModule);
            ctx.setDealGroupBase(null);
            ctx.setDealBaseReq(dealBaseReq);
            envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
            method.invoke(dealGroupReserveInfoProcessor, ctx);
            result = true;
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }
}
