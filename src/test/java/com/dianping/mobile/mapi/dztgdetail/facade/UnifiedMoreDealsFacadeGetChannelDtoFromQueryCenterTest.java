package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.publishcategory.dto.ChannelDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedMoreDealsReq;
import com.dianping.mobile.mapi.dztgdetail.entity.DealRecModuleOfflineList;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedMoreDealsFacadeGetChannelDtoFromQueryCenterTest {

    @InjectMocks
    private UnifiedMoreDealsFacade unifiedMoreDealsFacade;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private com.dianping.deal.publishcategory.dto.DealGroupChannelDTO invokePrivateMethod(DealGroupDTO dealGroupDTO) throws Exception {
        Method method = UnifiedMoreDealsFacade.class.getDeclaredMethod("getChannelDtoFromQueryCenter", DealGroupDTO.class);
        method.setAccessible(true);
        return (com.dianping.deal.publishcategory.dto.DealGroupChannelDTO) method.invoke(unifiedMoreDealsFacade, dealGroupDTO);
    }

    private boolean invokeDealRecModuleOffline(UnifiedMoreDealsReq request, EnvCtx envCtx) throws Exception {
        Method method = UnifiedMoreDealsFacade.class.getDeclaredMethod("dealRecModuleOffline", UnifiedMoreDealsReq.class, EnvCtx.class);
        method.setAccessible(true);
        return (boolean) method.invoke(unifiedMoreDealsFacade, request, envCtx);
    }

    /**
     * Test case for null input dealGroupDTO
     */
    @Test
    public void testGetChannelDtoFromQueryCenter_NullInput() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        com.dianping.deal.publishcategory.dto.DealGroupChannelDTO result = invokePrivateMethod(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test case for dealGroupDTO with null channel
     */
    @Test
    public void testGetChannelDtoFromQueryCenter_NullChannel() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getChannel()).thenReturn(null);
        // act
        com.dianping.deal.publishcategory.dto.DealGroupChannelDTO result = invokePrivateMethod(dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test case for valid input with category information
     */
    @Test
    public void testGetChannelDtoFromQueryCenter_ValidInputWithCategory() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupChannelDTO channelDTO = mock(DealGroupChannelDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupDTO.getChannel()).thenReturn(channelDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(dealGroupDTO.getDpDealGroupIdInt()).thenReturn(123);
        when(categoryDTO.getCategoryId()).thenReturn(456L);
        when(channelDTO.getChannelId()).thenReturn(1);
        when(channelDTO.getChannelEn()).thenReturn("testEn");
        when(channelDTO.getChannelCn()).thenReturn("testCn");
        when(channelDTO.getChannelGroupId()).thenReturn(2);
        when(channelDTO.getChannelGroupEn()).thenReturn("groupEn");
        when(channelDTO.getChannelGroupCn()).thenReturn("groupCn");
        // act
        com.dianping.deal.publishcategory.dto.DealGroupChannelDTO result = invokePrivateMethod(dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(123, result.getDealGroupId());
        assertEquals(456, result.getCategoryId());
        ChannelDTO resultChannel = result.getChannelDTO();
        assertNotNull(resultChannel);
        assertEquals(1, resultChannel.getChannelId());
        assertEquals("testEn", resultChannel.getChannelEn());
        assertEquals("testCn", resultChannel.getChannelCn());
        assertEquals(2, resultChannel.getChannelGroupId());
        assertEquals("groupEn", resultChannel.getChannelGroupEn());
        assertEquals("groupCn", resultChannel.getChannelGroupCn());
    }

    /**
     * Test case for valid input without category information
     */
    @Test
    public void testGetChannelDtoFromQueryCenter_ValidInputWithoutCategory() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupChannelDTO channelDTO = mock(DealGroupChannelDTO.class);
        when(dealGroupDTO.getChannel()).thenReturn(channelDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        when(dealGroupDTO.getDpDealGroupIdInt()).thenReturn(123);
        when(channelDTO.getChannelId()).thenReturn(1);
        when(channelDTO.getChannelEn()).thenReturn("testEn");
        when(channelDTO.getChannelCn()).thenReturn("testCn");
        when(channelDTO.getChannelGroupId()).thenReturn(2);
        when(channelDTO.getChannelGroupEn()).thenReturn("groupEn");
        when(channelDTO.getChannelGroupCn()).thenReturn("groupCn");
        // act
        com.dianping.deal.publishcategory.dto.DealGroupChannelDTO result = invokePrivateMethod(dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(123, result.getDealGroupId());
        // Default value for int
        assertEquals(0, result.getCategoryId());
        ChannelDTO resultChannel = result.getChannelDTO();
        assertNotNull(resultChannel);
        assertEquals(1, resultChannel.getChannelId());
        assertEquals("testEn", resultChannel.getChannelEn());
        assertEquals("testCn", resultChannel.getChannelCn());
        assertEquals(2, resultChannel.getChannelGroupId());
        assertEquals("groupEn", resultChannel.getChannelGroupEn());
        assertEquals("groupCn", resultChannel.getChannelGroupCn());
    }

    /**
     * Test case for valid input with null category ID
     */
    @Test
    public void testGetChannelDtoFromQueryCenter_ValidInputWithNullCategoryId() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupChannelDTO channelDTO = mock(DealGroupChannelDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupDTO.getChannel()).thenReturn(channelDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(dealGroupDTO.getDpDealGroupIdInt()).thenReturn(123);
        when(categoryDTO.getCategoryId()).thenReturn(null);
        when(channelDTO.getChannelId()).thenReturn(1);
        when(channelDTO.getChannelEn()).thenReturn("testEn");
        when(channelDTO.getChannelCn()).thenReturn("testCn");
        when(channelDTO.getChannelGroupId()).thenReturn(2);
        when(channelDTO.getChannelGroupEn()).thenReturn("groupEn");
        when(channelDTO.getChannelGroupCn()).thenReturn("groupCn");
        // act
        com.dianping.deal.publishcategory.dto.DealGroupChannelDTO result = invokePrivateMethod(dealGroupDTO);
        // assert
        assertNotNull(result);
        assertEquals(123, result.getDealGroupId());
        // Default value for int when categoryId is null
        assertEquals(0, result.getCategoryId());
        ChannelDTO resultChannel = result.getChannelDTO();
        assertNotNull(resultChannel);
        assertEquals(1, resultChannel.getChannelId());
        assertEquals("testEn", resultChannel.getChannelEn());
        assertEquals("testCn", resultChannel.getChannelCn());
        assertEquals(2, resultChannel.getChannelGroupId());
        assertEquals("groupEn", resultChannel.getChannelGroupEn());
        assertEquals("groupCn", resultChannel.getChannelGroupCn());
    }

    /**
     * 测试 request 为 null 的情况
     */
    @Test
    public void testDealRecModuleOfflineRequestNull() throws Throwable {
        // arrange
        UnifiedMoreDealsReq request = null;
        EnvCtx envCtx = new EnvCtx();
        // act
        boolean result = invokeDealRecModuleOffline(request, envCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 request.getDealGroupId() 为 null 的情况
     */
    @Test
    public void testDealRecModuleOfflineDealGroupIdNull() throws Throwable {
        // arrange
        UnifiedMoreDealsReq request = new UnifiedMoreDealsReq();
        request.setDealGroupId(null);
        EnvCtx envCtx = new EnvCtx();
        // act
        boolean result = invokeDealRecModuleOffline(request, envCtx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 request.getDealGroupId() 小于等于 0 的情况
     */
    @Test
    public void testDealRecModuleOfflineDealGroupIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        UnifiedMoreDealsReq request = new UnifiedMoreDealsReq();
        request.setDealGroupId(0);
        EnvCtx envCtx = new EnvCtx();
        // act
        boolean result = invokeDealRecModuleOffline(request, envCtx);
        // assert
        assertFalse(result);
    }
}
