package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzCardPromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MiniProgramSceneEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for BestShopProcessor.isEnable() method
 */
@RunWith(MockitoJUnitRunner.class)
public class BestShopProcessorTest {

    @InjectMocks
    private BestShopProcessor bestShopProcessor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DzCardPromoWrapper wrapper;

    private BestShopReq invokePrivateMethod(BestShopProcessor processor, String methodName, DealCtx ctx) throws Exception {
        Method method = BestShopProcessor.class.getDeclaredMethod(methodName, DealCtx.class);
        method.setAccessible(true);
        return (BestShopReq) method.invoke(bestShopProcessor, ctx);
    }

    /**
     * Helper method to invoke the private getDisplayShopIds method using reflection.
     */
    private List<Long> invokePrivateGetDisplayShopIds(DealGroupDTO dealGroupDTO) throws Exception {
        Method method = BestShopProcessor.class.getDeclaredMethod("getDisplayShopIds", DealGroupDTO.class);
        method.setAccessible(true);
        return (List<Long>) method.invoke(bestShopProcessor, dealGroupDTO);
    }

    /**
     * Test when dpId is less than 500000
     * Expected: should return true
     */
    @Test
    public void testIsEnable_WhenDpIdLessThan500000_ShouldReturnTrue() {
        // arrange
        when(dealCtx.getDpId()).thenReturn(499999);
        // act
        boolean result = bestShopProcessor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test when dpId equals 500000
     * Expected: should return false
     */
    @Test
    public void testIsEnable_WhenDpIdEquals500000_ShouldReturnFalse() {
        // arrange
        when(dealCtx.getDpId()).thenReturn(500000);
        // act
        boolean result = bestShopProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when dpId is between 500000 and 510000
     * Expected: should return false
     */
    @Test
    public void testIsEnable_WhenDpIdBetween500000And510000_ShouldReturnFalse() {
        // arrange
        when(dealCtx.getDpId()).thenReturn(505000);
        // act
        boolean result = bestShopProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when dpId equals 510000
     * Expected: should return false
     */
    @Test
    public void testIsEnable_WhenDpIdEquals510000_ShouldReturnFalse() {
        // arrange
        when(dealCtx.getDpId()).thenReturn(510000);
        // act
        boolean result = bestShopProcessor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test when dpId is greater than 510000
     * Expected: should return true
     */
    @Test
    public void testIsEnable_WhenDpIdGreaterThan510000_ShouldReturnTrue() {
        // arrange
        when(dealCtx.getDpId()).thenReturn(510001);
        // act
        boolean result = bestShopProcessor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test when ctx.isMt() returns true
     */
    @Test
    public void testBuildBestShopReqWithoutShopId_WhenMtIsTrue() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtId()).thenReturn(123);
        when(ctx.getMtCityId()).thenReturn(1);
        when(ctx.getUserlat()).thenReturn(31.2);
        when(ctx.getUserlng()).thenReturn(121.5);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getClientType()).thenReturn(1);
        // act
        BestShopReq result = invokePrivateMethod(bestShopProcessor, "buildBestShopReqWithoutShopId", ctx);
        // assert
        assertEquals(123, result.getDealGroupId());
        assertEquals(1, result.getCityId());
        assertEquals(31.2, result.getLat(), 0.001);
        assertEquals(121.5, result.getLng(), 0.001);
        assertEquals(GpsType.GCJ02.getType(), result.getGpsType());
        assertEquals(1, result.getClientType());
    }

    /**
     * Test when ctx.isMt() returns false
     */
    @Test
    public void testBuildBestShopReqWithoutShopId_WhenMtIsFalse() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpId()).thenReturn(456);
        when(ctx.getDpCityId()).thenReturn(2);
        when(ctx.getUserlat()).thenReturn(39.9);
        when(ctx.getUserlng()).thenReturn(116.4);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getClientType()).thenReturn(2);
        // act
        BestShopReq result = invokePrivateMethod(bestShopProcessor, "buildBestShopReqWithoutShopId", ctx);
        // assert
        assertEquals(456, result.getDealGroupId());
        assertEquals(2, result.getCityId());
        assertEquals(39.9, result.getLat(), 0.001);
        assertEquals(116.4, result.getLng(), 0.001);
        assertEquals(GpsType.GCJ02.getType(), result.getGpsType());
        assertEquals(2, result.getClientType());
    }

    /**
     * Test with zero/default values
     */
    @Test
    public void testBuildBestShopReqWithoutShopId_WithZeroValues() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtId()).thenReturn(0);
        when(ctx.getMtCityId()).thenReturn(0);
        when(ctx.getUserlat()).thenReturn(0.0);
        when(ctx.getUserlng()).thenReturn(0.0);
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.getClientType()).thenReturn(0);
        // act
        BestShopReq result = invokePrivateMethod(bestShopProcessor, "buildBestShopReqWithoutShopId", ctx);
        // assert
        assertEquals(0, result.getDealGroupId());
        assertEquals(0, result.getCityId());
        assertEquals(0.0, result.getLat(), 0.001);
        assertEquals(0.0, result.getLng(), 0.001);
        assertEquals(GpsType.GCJ02.getType(), result.getGpsType());
        assertEquals(0, result.getClientType());
    }

    /**
     * Test with null EnvCtx
     */
    @Test
    public void testBuildBestShopReqWithoutShopId_WithNullEnvCtx() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        EnvCtx envCtx = mock(EnvCtx.class);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtId()).thenReturn(789);
        when(ctx.getMtCityId()).thenReturn(3);
        when(ctx.getUserlat()).thenReturn(22.5);
        when(ctx.getUserlng()).thenReturn(114.0);
        // Ensure EnvCtx is not null
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        // Set a default client type
        when(envCtx.getClientType()).thenReturn(0);
        // act
        BestShopReq result = invokePrivateMethod(bestShopProcessor, "buildBestShopReqWithoutShopId", ctx);
        // assert
        assertEquals(789, result.getDealGroupId());
        assertEquals(3, result.getCityId());
        assertEquals(22.5, result.getLat(), 0.001);
        assertEquals(114.0, result.getLng(), 0.001);
        assertEquals(GpsType.GCJ02.getType(), result.getGpsType());
        assertEquals(0, result.getClientType());
    }

    /**
     * Test when context is external but not enabled for joy card scene
     */
    @Test
    public void testPrepareJoyCardInfo_WhenExternalButNotEnabled() throws Throwable {
        // arrange
        EnvCtx envCtx = mock(EnvCtx.class);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFutureCtx(new FutureCtx());
        ctx.setExternal(true);
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.JOY_CARD.getPromoScene())).thenReturn(false);
        // act
        Method method = BestShopProcessor.class.getDeclaredMethod("prepareJoyCardInfo", DealCtx.class);
        method.setAccessible(true);
        method.invoke(bestShopProcessor, ctx);
        // assert
        verify(wrapper, never()).prepare(ctx);
        verify(wrapper, never()).prepareUserState(ctx);
        assertNull(ctx.getFutureCtx().getDzCardFuture());
        assertNull(ctx.getFutureCtx().getUserStateFuture());
    }

    /**
     * Test when context is external and enabled returns null
     */
    @Test
    public void testPrepareJoyCardInfo_WhenExternalAndEnabledNull() throws Throwable {
        // arrange
        EnvCtx envCtx = mock(EnvCtx.class);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFutureCtx(new FutureCtx());
        ctx.setExternal(true);
        // Changed from null to false
        when(envCtx.isExternalAndEnabled(MiniProgramSceneEnum.JOY_CARD.getPromoScene())).thenReturn(false);
        // act
        Method method = BestShopProcessor.class.getDeclaredMethod("prepareJoyCardInfo", DealCtx.class);
        method.setAccessible(true);
        method.invoke(bestShopProcessor, ctx);
        // assert
        verify(wrapper, never()).prepare(ctx);
        verify(wrapper, never()).prepareUserState(ctx);
        assertNull(ctx.getFutureCtx().getDzCardFuture());
        assertNull(ctx.getFutureCtx().getUserStateFuture());
    }

    /**
     * Test when dealGroupDTO is null
     */
    @Test
    public void testGetDisplayShopIds_WhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        List<Long> result = invokePrivateGetDisplayShopIds(dealGroupDTO);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when displayShopInfo is null
     */
    @Test
    public void testGetDisplayShopIds_WhenDisplayShopInfoIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(null);
        // act
        List<Long> result = invokePrivateGetDisplayShopIds(dealGroupDTO);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when dpDisplayShopIds is null
     */
    @Test
    public void testGetDisplayShopIds_WhenDpDisplayShopIdsIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopInfo = mock(DealGroupDisplayShopDTO.class);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        when(displayShopInfo.getDpDisplayShopIds()).thenReturn(null);
        // act
        List<Long> result = invokePrivateGetDisplayShopIds(dealGroupDTO);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when there are valid display shop IDs
     */
    @Test
    public void testGetDisplayShopIds_WithValidShopIds() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopInfo = mock(DealGroupDisplayShopDTO.class);
        List<Long> expectedShopIds = Arrays.asList(1L, 2L, 3L);
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        when(displayShopInfo.getDpDisplayShopIds()).thenReturn(expectedShopIds);
        // act
        List<Long> result = invokePrivateGetDisplayShopIds(dealGroupDTO);
        // assert
        assertEquals(expectedShopIds, result);
    }

    /**
     * Test when display shop IDs list is empty
     */
    @Test
    public void testGetDisplayShopIds_WithEmptyShopIds() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopInfo = mock(DealGroupDisplayShopDTO.class);
        List<Long> emptyShopIds = Collections.emptyList();
        when(dealGroupDTO.getDisplayShopInfo()).thenReturn(displayShopInfo);
        when(displayShopInfo.getDpDisplayShopIds()).thenReturn(emptyShopIds);
        // act
        List<Long> result = invokePrivateGetDisplayShopIds(dealGroupDTO);
        // assert
        assertTrue(result.isEmpty());
    }
}
