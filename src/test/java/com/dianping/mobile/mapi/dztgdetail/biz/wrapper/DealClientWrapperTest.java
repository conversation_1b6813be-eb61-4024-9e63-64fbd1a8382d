package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.meituan.service.mobile.prometheus.client.service.DealClientService;
import com.meituan.service.mobile.prometheus.model.DealModel;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

public class DealClientWrapperTest {

    @InjectMocks
    private DealClientWrapper dealClientWrapper;

    @Mock
    private DealClientService dealClientService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试listDealByIds方法正常情况
     */
    @Test
    public void testListDealByIdsNormal() throws Throwable {
        // arrange
        List<Integer> dids = Arrays.asList(1, 2, 3);
        List<String> fields = Arrays.asList("field1", "field2");
        List<DealModel> expected = Arrays.asList(new DealModel(), new DealModel());
        when(dealClientService.listDealByIds(dids, fields)).thenReturn(expected);
        // act
        List<DealModel> actual = dealClientWrapper.listDealByIds(dids, fields);
        // assert
        assertEquals(expected, actual);
        verify(dealClientService, times(1)).listDealByIds(dids, fields);
    }

    /**
     * 测试listDealByIds方法异常情况
     */
    @Test
    public void testListDealByIdsException() throws Throwable {
        // arrange
        List<Integer> dids = Arrays.asList(1, 2, 3);
        List<String> fields = Arrays.asList("field1", "field2");
        when(dealClientService.listDealByIds(dids, fields)).thenThrow(new RuntimeException());
        // act
        List<DealModel> actual = dealClientWrapper.listDealByIds(dids, fields);
        // assert
        assertNull(actual);
        verify(dealClientService, times(1)).listDealByIds(dids, fields);
    }
}
