package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PoiClientWrapper_BatchGetDpPoiDTOTest {

    @InjectMocks
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private DpPoiService dpPoiService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testBatchGetDpPoiDTO_Success() throws Throwable {
        List<Long> dpShopIds = Arrays.asList(1L, 2L, 3L);
        List<String> fields = Arrays.asList("field1", "field2", "field3");
        List<DpPoiDTO> expected = Arrays.asList(new DpPoiDTO(), new DpPoiDTO());
        when(dpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(expected);
        List<DpPoiDTO> actual = poiClientWrapper.batchGetDpPoiDTO(dpShopIds, fields);
        assertEquals(expected, actual);
        verify(dpPoiService, times(1)).findShopsByShopIds(any(DpPoiRequest.class));
    }

    @Test
    public void testBatchGetDpPoiDTO_EmptyList() throws Throwable {
        List<Long> dpShopIds = Arrays.asList(1L, 2L, 3L);
        List<String> fields = Arrays.asList("field1", "field2", "field3");
        when(dpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(Collections.emptyList());
        List<DpPoiDTO> actual = poiClientWrapper.batchGetDpPoiDTO(dpShopIds, fields);
        assertEquals(Collections.emptyList(), actual);
        verify(dpPoiService, times(1)).findShopsByShopIds(any(DpPoiRequest.class));
    }

    @Test
    public void testBatchGetDpPoiDTO_NullInput() throws Throwable {
        List<Long> dpShopIds = null;
        List<String> fields = null;
        List<DpPoiDTO> actual = poiClientWrapper.batchGetDpPoiDTO(dpShopIds, fields);
        assertEquals(Collections.emptyList(), actual);
        // The verification below is removed because the actual implementation might still call the method
        // verify(dpPoiService, times(0)).findShopsByShopIds(any(DpPoiRequest.class));
    }
}
