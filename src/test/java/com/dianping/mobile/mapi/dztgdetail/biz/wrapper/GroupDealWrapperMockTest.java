package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.UniversalInsuranceProcess;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.UnifiedCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO;
import com.dianping.mobile.mapi.dztgdetail.facade.DealTinyInfoFacade;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.meituan.common.json.JSONUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;

@RunWith(MockitoJUnitRunner.class)
public class GroupDealWrapperMockTest {

    @InjectMocks
    private UniversalInsuranceProcess universalInsuranceProcess;

    @Mock
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Mock
    private DealProductService dealProductService;

    @InjectMocks
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Mock
    private SkuWrapper skuWrapper;

   @Test
    public void test2(){
       UnifiedCtx unifiedCtx = new UnifiedCtx(new EnvCtx());
       unifiedCtx.setMtShopId(23005380);
       unifiedCtx.setDpShopId(23005380);
       unifiedCtx.getMtShopId();
       unifiedCtx.getDpShopId();
       Assert.assertNotNull(unifiedCtx);
   }

   @Test
    public void test3() throws Exception{
       try {
           EnvCtx envCtx = new EnvCtx();
           envCtx.setMtUserId(1);
           envCtx.setDpUserId(1);
           envCtx.setAppDeviceId("");
           DealCtx ctx = new DealCtx(envCtx);
           ctx.isMt();
           ctx.setMtId(403287260);
           ctx.setDpId(403287260);
           ctx.setMtLongShopId(152393402);
           ctx.setDpLongShopId(23005380);
           ctx.setRealMtCityId(10);
           ctx.setDpCityId(10);
           universalInsuranceProcess.prepare(ctx);
           Object o = ctx.getFutureCtx().getDealThemeFuture();
           Assert.assertNull(o);
       } catch (Exception e) {

       }
   }

    @Test
    public void test4(){
        EnvCtx envCtx = new EnvCtx();
        envCtx.setMtUserId(859830518L);
        envCtx.setDpUserId(859830518L);
        envCtx.setAppDeviceId("");
        GetDealTinyInfoRequest getDealTinyInfoRequest = new GetDealTinyInfoRequest();
        getDealTinyInfoRequest.setDealGroupId("403287260");
        getDealTinyInfoRequest.setShopId("23005380");
        getDealTinyInfoRequest.setPageSource("caixi");
        getDealTinyInfoRequest.setCityId(10);
        getDealTinyInfoRequest.setLat(-78.44295);
        getDealTinyInfoRequest.setLng(47.60355);
        DealTinyInfoVO dealTinyInfo = dealTinyInfoFacade.getDealTinyInfo(getDealTinyInfoRequest, envCtx);
        System.out.println(JSONUtil.toJSONString(dealTinyInfo));
        Assert.assertNull(dealTinyInfo);
    }

    @Test
    public void test1(){
        String uuid = ShopUuidUtils.getUuidByIdLong(23005380L);
        ShopUuidUtils.getShopIdByUuid("k65YkZ7itAC0KkwI");
        Assert.assertNotNull(uuid);
    }


    @Test
    public void test21(){

       GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
       request.setBusinessType("purchase_limit");
       request.setDealGroupId("1");
       request.setShopId("2");
       EnvCtx envCtx = new EnvCtx();
       DealGroupWrapper dealGroupWrapper = new DealGroupWrapper();
       PoiClientWrapper poiClientWrapper = new PoiClientWrapper();
       DouHuService douHuService = new DouHuService();
        SkuWrapper skuWrapper = new SkuWrapper();
        DzDealThemeWrapper dzDealThemeWrapper = new DzDealThemeWrapper();
       Class clazz = dealTinyInfoFacade.getClass();
       try {
            Field field = clazz.getDeclaredField("dealGroupWrapper");
            field.setAccessible(true);
            field.set(dealTinyInfoFacade, dealGroupWrapper);

            field = clazz.getDeclaredField("poiClientWrapper");
            field.setAccessible(true);
            field.set(dealTinyInfoFacade, poiClientWrapper);

           field = clazz.getDeclaredField("douHuService");
           field.setAccessible(true);
           field.set(dealTinyInfoFacade, douHuService);

           field = clazz.getDeclaredField("skuWrapper");
           field.setAccessible(true);
           field.set(dealTinyInfoFacade, skuWrapper);

           field = clazz.getDeclaredField("dzDealThemeWrapper");
           field.setAccessible(true);
           field.set(dealTinyInfoFacade, dzDealThemeWrapper);
           request.setCityId(16);
           if (envCtx.isMt()){
               request.setCityId(59);
           }
           dealTinyInfoFacade.isShowLowPriceItemEntrance(request, envCtx);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {

       }

        Assert.assertNotNull(request);

   }
}
