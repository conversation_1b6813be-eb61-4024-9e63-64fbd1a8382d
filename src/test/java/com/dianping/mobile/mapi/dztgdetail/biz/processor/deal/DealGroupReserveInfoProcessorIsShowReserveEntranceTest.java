package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ShowReservationGrayConfig;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.process.gateway.thrift.common.user.trade.request.DealGroupInfoDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class })
public class // @Test
// public void testPoiWhiteListJudge() throws InvocationTargetException, IllegalAccessException {
// DpPoiDTO dpPoiDTO = JacksonUtils.deserialize(dpPoiDtoJson, DpPoiDTO.class);
// when(Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
// "com.sankuai.dzu.tpbase.dztgdetailweb.wash.backlevel2CategoryId", Integer.class,
// new ArrayList<>())).thenAnswer((Answer<?>) invocation -> Lists.newArrayList(817));
// when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
// Method method = PowerMockito.method(DealGroupReserveInfoProcessor.class, "poiWhiteListJudge");
// boolean result = (boolean) method.invoke(dealGroupReserveInfoProcessor,ctx);
// assert result;
DealGroupReserveInfoProcessorIsShowReserveEntranceTest {

    @InjectMocks
    private DealGroupReserveInfoProcessor dealGroupReserveInfoProcessor;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private DealCtx ctx;

    //
    // when(ctx.getModuleAbConfigs()).thenReturn(Lists.newArrayList(moduleAbConfig));
    //
    // Method method = PowerMockito.method(DealGroupReserveInfoProcessor.class, "getWashReserveExpResult");
    // boolean result = (boolean) method.invoke(dealGroupReserveInfoProcessor, ctx);
    // assert !result;
    // }
    private static final String dpPoiDtoJson = "{\"@class\":\"com.sankuai.sinai.data.api.dto.DpPoiDTO\",\"shopId\":607001018,\"shopGroupId\":null,\"branchName\":null,\"altName\":null,\"shopName\":null,\"mainRegionName\":null,\"mainRegionId\":null,\"mainCategoryName\":null,\"mainCategoryId\":null,\"crossRoad\":null,\"address\":null,\"lng\":null,\"lat\":null,\"cityId\":null,\"shopType\":null,\"districtId\":null,\"power\":null,\"clientType\":null,\"status\":null,\"useType\":null,\"srcName\":null,\"shopDesc\":null,\"freshBusinessHours\":null,\"businessHours\":null,\"publicTransit\":null,\"mapType\":null,\"coordType\":null,\"addUserId\":null,\"addUser\":null,\"lastUserId\":null,\"lastUser\":null,\"addTime\":null,\"updateTime\":null,\"shopRegionList\":null,\"shopCategoryList\":null,\"shopBackCategoryList\":null,\"backMainCategoryPath\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO\",\"categoryId\":3,\"categoryName\":\"休闲娱乐\",\"parentId\":0,\"hot\":0,\"categoryLevel\":1,\"leaf\":false,\"main\":null},{\"@class\":\"com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO\",\"categoryId\":817,\"categoryName\":\"按摩/足疗\",\"parentId\":3,\"hot\":0,\"categoryLevel\":2,\"leaf\":true,\"main\":true}]],\"mainCategoryPath\":null,\"uuid\":null,\"normPhones\":null,\"shopPower\":null,\"avgPrice\":null,\"score\":null,\"score1\":null,\"score2\":null,\"score3\":null,\"score4\":null,\"refinedScore1\":\"\",\"refinedScore2\":\"\",\"refinedScore3\":\"\",\"fiveScore\":0,\"sub5\":0,\"sub6\":0,\"fiveSub1\":0,\"fiveSub2\":0,\"fiveSub3\":0,\"fiveSub4\":0,\"fiveSub5\":0,\"fiveSub6\":0,\"defaultPic\":null,\"defaultPicKey\":null,\"hits\":null,\"weeklyHits\":null,\"prevWeeklyHits\":null,\"todayHits\":null,\"monthlyHits\":null,\"poiBizAttrValues\":null,\"sensitiveLevel\":null,\"newPoiSensLevel\":null,\"mid\":null,\"createSubSourceStr\":null,\"hospitalInfo\":null,\"isRevealPic\":null,\"hsStatus\":null,\"poiBrandId\":null,\"appSides\":null,\"legalStatus\":null,\"crtMntSrc\":null,\"dataMntSrc\":null,\"geomConfidence\":null,\"statusConfidence\":null,\"kindConfidence\":null,\"phoneConfidence\":null,\"subScores\":null,\"mtPoiId\":null,\"onDoorInfo\":null,\"shopGuideSteps\":null,\"suspectPhones\":null,\"suspectBizHour\":null}";

    @Mock
    private ShowReservationGrayConfig grayConfig;

    @Mock
    private EnvCtx envCtx;

    private DealGroupReserveInfoProcessor processor;

    @Before
    public void setUp() throws Exception {
        processor = new DealGroupReserveInfoProcessor();
        // Use reflection to set the private field douHuBiz
        Field douHuBizField = DealGroupReserveInfoProcessor.class.getDeclaredField("douHuBiz");
        douHuBizField.setAccessible(true);
        douHuBizField.set(processor, douHuBiz);
    }

    /**
     * Helper method to invoke the private method using reflection.
     */
    private boolean invokeIsShowReserveEntrance(DealGroupReserveInfoProcessor processor, Boolean shopSupportReserve, String expResult) throws Exception {
        Method method = DealGroupReserveInfoProcessor.class.getDeclaredMethod("isShowReserveEntrance", Boolean.class, String.class);
        method.setAccessible(true);
        return (boolean) method.invoke(processor, shopSupportReserve, expResult);
    }

    /**
     * Test Scenario 1.1: Both shopSupportReserve and expResult are not null,
     * shopSupportReserve is true and expResult is "b".
     */
    @Test
    public void testIsShowReserveEntrance_BothNotNull_ShopSupportTrue_ExpResultB() throws Throwable {
        // arrange
        Boolean shopSupportReserve = true;
        String expResult = "b";
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertTrue(result);
    }

    /**
     * Test Scenario 1.2: Both shopSupportReserve and expResult are not null,
     * shopSupportReserve is true and expResult is not "b".
     */
    @Test
    public void testIsShowReserveEntrance_BothNotNull_ShopSupportTrue_ExpResultNotB() throws Throwable {
        // arrange
        Boolean shopSupportReserve = true;
        String expResult = "c";
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test Scenario 1.3: Both shopSupportReserve and expResult are not null,
     * shopSupportReserve is false and expResult is "b".
     */
    @Test
    public void testIsShowReserveEntrance_BothNotNull_ShopSupportFalse_ExpResultB() throws Throwable {
        // arrange
        Boolean shopSupportReserve = false;
        String expResult = "b";
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test Scenario 1.4: Both shopSupportReserve and expResult are not null,
     * shopSupportReserve is false and expResult is not "b".
     */
    @Test
    public void testIsShowReserveEntrance_BothNotNull_ShopSupportFalse_ExpResultNotB() throws Throwable {
        // arrange
        Boolean shopSupportReserve = false;
        String expResult = "c";
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test Scenario 2.1: shopSupportReserve is not null, expResult is null,
     * shopSupportReserve is true.
     */
    @Test
    public void testIsShowReserveEntrance_ShopSupportNotNull_ExpResultNull_ShopSupportTrue() throws Throwable {
        // arrange
        Boolean shopSupportReserve = true;
        String expResult = null;
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertTrue(result);
    }

    /**
     * Test Scenario 2.2: shopSupportReserve is not null, expResult is null,
     * shopSupportReserve is false.
     */
    @Test
    public void testIsShowReserveEntrance_ShopSupportNotNull_ExpResultNull_ShopSupportFalse() throws Throwable {
        // arrange
        Boolean shopSupportReserve = false;
        String expResult = null;
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test Scenario 3: Both shopSupportReserve and expResult are null.
     */
    @Test
    public void testIsShowReserveEntrance_BothNull() throws Throwable {
        // arrange
        Boolean shopSupportReserve = null;
        String expResult = null;
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    // @Test
    // public void testGetWashReserveExpResult() throws InvocationTargetException, IllegalAccessException {
    // ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
    // moduleAbConfig.setKey("MtWashReserveExp");
    // AbConfig abConfig = new AbConfig();
    // abConfig.setExpId("expId");
    // abConfig.setExpResult("c");
    // moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
    /**
     * Test case: MT shop ID not in whitelist should return false
     * Scenario: When isMt=true and shop ID is not in the whitelist for the category
     */
    @Test
    public void testHitShowReservation_MtShopNotInWhitelist() throws Throwable {
        // arrange
        int publishCategoryId = 123;
        long mtShopId = 456L;
        List<Long> whitelistShopIds = Arrays.asList(789L, 101112L);
        Map<String, List<Long>> category2ShopIds = new HashMap<>();
        category2ShopIds.put(String.valueOf(publishCategoryId), whitelistShopIds);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(publishCategoryId);
        when(ctx.getMtLongShopId()).thenReturn(mtShopId);
        when(grayConfig.getWhiteListMode()).thenReturn(0);
        when(grayConfig.getCategory2ShopIds()).thenReturn(category2ShopIds);
        // act
        boolean result = processor.hitShowReservation(grayConfig, ctx);
        // assert
        assertFalse(result);
    }

    // @Test
    // public void testGetWashReserveExpResult() throws InvocationTargetException, IllegalAccessException {
    // ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
    // moduleAbConfig.setKey("MtWashReserveExp");
    // AbConfig abConfig = new AbConfig();
    /**
     * Test case: DP shop ID not in whitelist should return false
     * Scenario: When isMt=false and DP shop ID is not in the whitelist for the category
     */
    @Test
    public void testHitShowReservation_DpShopNotInWhitelist() throws Throwable {
        // arrange
        int publishCategoryId = 123;
        long dpShopId = 456L;
        List<Long> whitelistShopIds = Arrays.asList(789L, 101112L);
        Map<String, List<Long>> category2ShopIds = new HashMap<>();
        category2ShopIds.put(String.valueOf(publishCategoryId), whitelistShopIds);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getCategoryId()).thenReturn(publishCategoryId);
        when(ctx.getDpLongShopId()).thenReturn(dpShopId);
        when(grayConfig.getWhiteListMode()).thenReturn(0);
        when(grayConfig.getCategory2ShopIds()).thenReturn(category2ShopIds);
        // act
        boolean result = processor.hitShowReservation(grayConfig, ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Category not in whitelist map should return false
     * Scenario: When category is not present in category2ShopIds map
     */
    @Test
    public void testHitShowReservation_CategoryNotInWhitelist() throws Throwable {
        // arrange
        int publishCategoryId = 123;
        Map<String, List<Long>> category2ShopIds = new HashMap<>();
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(publishCategoryId);
        when(grayConfig.getWhiteListMode()).thenReturn(0);
        when(grayConfig.getCategory2ShopIds()).thenReturn(category2ShopIds);
        // act
        boolean result = processor.hitShowReservation(grayConfig, ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Empty category2ShopIds map should return false
     * Scenario: When category2ShopIds map is empty
     */
    @Test
    public void testHitShowReservation_EmptyWhitelistMap() throws Throwable {
        // arrange
        int publishCategoryId = 123;
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(publishCategoryId);
        when(grayConfig.getWhiteListMode()).thenReturn(0);
        when(grayConfig.getCategory2ShopIds()).thenReturn(null);
        // act
        boolean result = processor.hitShowReservation(grayConfig, ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Null whitelist map should return false
     * Scenario: When category2ShopIds is null
     */
    @Test
    public void testHitShowReservation_NullWhitelistMap() throws Throwable {
        // arrange
        int publishCategoryId = 123;
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getCategoryId()).thenReturn(publishCategoryId);
        when(grayConfig.getWhiteListMode()).thenReturn(0);
        when(grayConfig.getCategory2ShopIds()).thenReturn(null);
        // act
        boolean result = processor.hitShowReservation(grayConfig, ctx);
        // assert
        assertFalse(result);
    }
}
