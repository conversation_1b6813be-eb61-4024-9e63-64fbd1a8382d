package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.DecryptBiz;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DecryptVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DecryptFacadeTest {

    @InjectMocks
    private DecryptFacade decryptFacade;

    @Mock
    private DecryptBiz decryptBiz;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试decrypt方法，当decryptBiz.decrypt正常返回时
     */
    @Test
    public void testDecryptNormal() throws Throwable {
        // arrange
        String encryptedStr = "encryptedStr";
        boolean isMt = true;
        DecryptVO expected = new DecryptVO();
        when(decryptBiz.decrypt(encryptedStr, isMt)).thenReturn(expected);
        // act
        DecryptVO actual = decryptFacade.decrypt(encryptedStr, isMt);
        // assert
        assertEquals(expected, actual);
        verify(decryptBiz, times(1)).decrypt(encryptedStr, isMt);
    }

    /**
     * 测试decrypt方法，当decryptBiz.decrypt抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testDecryptException() throws Throwable {
        // arrange
        String encryptedStr = "encryptedStr";
        boolean isMt = true;
        when(decryptBiz.decrypt(encryptedStr, isMt)).thenThrow(new RuntimeException());
        // act
        decryptFacade.decrypt(encryptedStr, isMt);
        // assert
        // Exception is expected
    }
}
