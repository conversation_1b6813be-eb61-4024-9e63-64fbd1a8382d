package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.stock.DealStockQueryService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class DealStockWrapper_PreStockTest {

    @InjectMocks
    private DealStockWrapper dealStockWrapper;

    @Mock
    private DealStockQueryService dealStockQueryServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock the FutureFactory.getFuture() to return a non-null Future object for the test
        // This is necessary because the method under test returns the result of FutureFactory.getFuture()
        // which we cannot control without mocking static methods (not recommended here).
        // Instead, we ensure the method call to dealStockQueryServiceFuture.mGetProductGroupStocks does not throw an exception,
        // which would lead to a non-null Future being returned by the method under test.
        when(dealStockQueryServiceFuture.mGetProductGroupStocks(anyList())).thenReturn(mock(Map.class));
    }

    @Test
    public void testPreStocksWithEmptyDpDealGroupIds() throws Throwable {
        List<Integer> dpDealGroupIds = null;
        Future result = dealStockWrapper.preStocks(dpDealGroupIds);
        assertNull(result);
    }

    @Test
    public void testPreStocksWithNonEmptyDpDealGroupIdsAndExceptionMGetProductGroupStocks() throws Throwable {
        List<Integer> dpDealGroupIds = Arrays.asList(1, 2, 3);
        doThrow(new RuntimeException()).when(dealStockQueryServiceFuture).mGetProductGroupStocks(dpDealGroupIds);
        Future result = dealStockWrapper.preStocks(dpDealGroupIds);
        assertNull(result);
        verify(dealStockQueryServiceFuture, times(1)).mGetProductGroupStocks(dpDealGroupIds);
    }
}
