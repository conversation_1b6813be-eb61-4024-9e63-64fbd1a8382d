package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ReserveMaintenanceService;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/12/31 10:24
 */
public class DealDouHuUtilTest {

    @Test
    public void testGetExpressOptimizeExpResultNormal() {
        List<ModuleAbConfig> moduleAbConfigs = JacksonUtils.deserialize(moduleAbConfigsJson, List.class);
        DealCtx ctx = Mockito.mock(DealCtx.class);
        when(ctx.getModuleAbConfigs()).thenReturn(moduleAbConfigs);
        boolean expressOptimizeExpResult = DealDouHuUtil.getExpressOptimizeExpResult(ctx);
        assert expressOptimizeExpResult;
    }

    @Test
    public void testGetExpressOptimizeExpResultNull() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        when(ctx.getModuleAbConfigs()).thenReturn(null);
        boolean expressOptimizeExpResult = DealDouHuUtil.getExpressOptimizeExpResult(ctx);
        assert !expressOptimizeExpResult;
    }

    @Test
    public void testGetExpressOptimizeExpResultException() {
        DealCtx ctx = Mockito.mock(DealCtx.class);
        when(ctx.getModuleAbConfigs()).thenThrow(new RuntimeException());
        boolean expressOptimizeExpResult = DealDouHuUtil.getExpressOptimizeExpResult(ctx);
        assert !expressOptimizeExpResult;
    }

    private static final String moduleAbConfigsJson = "[\"java.util.ArrayList\",[{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTSalesGeneralSection\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp001434\",\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"ea441749-d651-4c5d-86d9-51865e2adb26\\\",\\\"ab_id\\\":\\\"exp001434_a\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate2Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"239e7b94-ec68-4541-9bf3-411e9822313e\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtCouponAlleviate2Exp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024082700008\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"2b5235c1-f900-4261-8734-10629ff920b0\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"useNewStyle\":false}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MTPhysicalExerciseReserve\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"exp000882\",\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"672441d9-921d-4285-a43e-a2dc452fe075\\\",\\\"ab_id\\\":\\\"exp000882_b\\\"}\",\"useNewStyle\":true}]]},{\"@class\":\"com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig\",\"key\":\"MtExpressOptimizeExp\",\"configs\":[\"java.util.ArrayList\",[{\"expId\":\"EXP2024111500002\",\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"65818f59-cb25-4fdf-889f-99a353096408\\\",\\\"ab_id\\\":\\\"EXP2024111500002_c\\\"}\",\"useNewStyle\":false}]]}]]";
}