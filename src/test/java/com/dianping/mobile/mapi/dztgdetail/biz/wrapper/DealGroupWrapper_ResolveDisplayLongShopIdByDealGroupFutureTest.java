package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapper_ResolveDisplayLongShopIdByDealGroupFutureTest {

    @Mock
    private Future future;

    private DealGroupWrapper dealGroupWrapper = new DealGroupWrapper();

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testResolveDisplayLongShopIdByDealGroupFutureFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        List<Long> result = dealGroupWrapper.resolveDisplayLongShopIdByDealGroupFuture(future);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 getFutureResult 返回 null 的情况
     */
    @Test
    public void testResolveDisplayLongShopIdByDealGroupFutureGetFutureResultIsNull() throws Throwable {
        // arrange
        when(dealGroupWrapper.getFutureResult(future, "DealGroupWrapper", "failed to resolve DisplayLongShopIdByDealGroup future")).thenReturn(null);
        // act
        List<Long> result = dealGroupWrapper.resolveDisplayLongShopIdByDealGroupFuture(future);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 getFutureResult 返回的结果为空的情况
     */
    @Test
    public void testResolveDisplayLongShopIdByDealGroupFutureGetFutureResultIsEmpty() throws Throwable {
        // arrange
        when(dealGroupWrapper.getFutureResult(future, "DealGroupWrapper", "failed to resolve DisplayLongShopIdByDealGroup future")).thenReturn(Collections.emptyMap());
        // act
        List<Long> result = dealGroupWrapper.resolveDisplayLongShopIdByDealGroupFuture(future);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 getFutureResult 返回的结果大小不为1的情况
     */
    @Test
    public void testResolveDisplayLongShopIdByDealGroupFutureGetFutureResultSizeNotOne() throws Throwable {
        // arrange
        Map<Integer, List<Long>> map = new HashMap<>();
        map.put(1, Collections.singletonList(1L));
        map.put(2, Collections.singletonList(2L));
        when(dealGroupWrapper.getFutureResult(future, "DealGroupWrapper", "failed to resolve DisplayLongShopIdByDealGroup future")).thenReturn(map);
        // act
        List<Long> result = dealGroupWrapper.resolveDisplayLongShopIdByDealGroupFuture(future);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 getFutureResult 返回的结果大小为1的情况
     */
    @Test
    public void testResolveDisplayLongShopIdByDealGroupFutureGetFutureResultSizeIsOne() throws Throwable {
        // arrange
        Map<Integer, List<Long>> map = new HashMap<>();
        map.put(1, Collections.singletonList(1L));
        when(dealGroupWrapper.getFutureResult(future, "DealGroupWrapper", "failed to resolve DisplayLongShopIdByDealGroup future")).thenReturn(map);
        // act
        List<Long> result = dealGroupWrapper.resolveDisplayLongShopIdByDealGroupFuture(future);
        // assert
        assertEquals(Collections.singletonList(1L), result);
    }
}
