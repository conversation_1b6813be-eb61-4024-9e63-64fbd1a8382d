package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.BuyMoreSaveMoreCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.BuyMoreSaveMoreReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.nailstyle.NailStyleItemVO;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryRecommendParam;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RecommendServiceWrapper_PreGetRecommendStyleImageTest {

    @InjectMocks
    private RecommendServiceWrapper recommendServiceWrapper;

    @Mock
    private RecommendService recommendServiceFuture;

    @Mock
    private Future future;

    @Mock
    private Response<RecommendResult<RecommendDTO>> response;

    @Mock
    private RecommendResult<RecommendDTO> recommendResult;

    @Mock
    private RecommendDTO recommendDTO;

    @Mock
    private ImmersiveImageWrapper immersiveImageWrapper;

    @Mock
    private PlayCenterService.Iface playCenterService;

    @Mock
    private BuyMoreSaveMoreCtx ctx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private BuyMoreSaveMoreReq req;

    private RecommendServiceWrapper wrapper = new RecommendServiceWrapper();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        // Use reflection to set the private field
        Field playCenterServiceField = RecommendServiceWrapper.class.getDeclaredField("playCenterService");
        playCenterServiceField.setAccessible(true);
        playCenterServiceField.set(recommendServiceWrapper, playCenterService);
    }

    private QueryRecommendParam createQueryRecommendParam() {
        return QueryRecommendParam.builder().isMt(true).cityId(1).dpId("dpId").uuid("uuid").originUserId("originUserId").platformEnum(PlatformEnum.DP).latitude(0.0).longitude(0.0).exhibitImgIds(null).filterIds("filterIds").sortType("sortType").flowFlag("flowFlag").shopRatingThreshold("shopRatingThreshold").shopId(1L).start(1).limit(1).clientType(1).categoryId(1).dealGroupPrice("dealGroupPrice").isAll("isAll").dealGroupTagIds("dealGroupTagIds").dpDealGroupId(1L).build();
    }

    /**
     * Test preGetRecommendStyleImage method with some properties null
     */
    @Test
    public void testPreGetRecommendStyleImageSomePropertiesNull() throws Throwable {
        // Arrange
        QueryRecommendParam param = QueryRecommendParam.builder().build();
        // Act
        recommendServiceWrapper.preGetRecommendStyleImage(param);
        // Assert
        verify(recommendServiceFuture).recommend(any(RecommendParameters.class), any());
    }

    /**
     * Test preGetRecommendStyleImage method with all properties normal
     */
    @Test
    public void testPreGetRecommendStyleImageAllPropertiesNormal() throws Throwable {
        // Arrange
        QueryRecommendParam param = QueryRecommendParam.builder().isMt(true).cityId(1).dpId("dpId").uuid("uuid").originUserId("originUserId").platformEnum(PlatformEnum.MT).latitude(30.0).longitude(120.0).exhibitImgIds(Arrays.asList("1", "2", "3")).filterIds("filterIds").sortType("sortType").flowFlag("flowFlag").shopRatingThreshold("shopRatingThreshold").shopId(1L).start(1).limit(10).clientType(1).categoryId(1).dealGroupPrice("dealGroupPrice").isAll("isAll").dealGroupTagIds("dealGroupTagIds").dpDealGroupId(1L).build();
        // Act
        recommendServiceWrapper.preGetRecommendStyleImage(param);
        // Assert
        verify(recommendServiceFuture).recommend(any(RecommendParameters.class), any());
    }

    @Test(expected = NullPointerException.class)
    public void testGetRecommendStyleImageForOrderParamIsNull() throws Throwable {
        recommendServiceWrapper.getRecommendStyleImageForOrder(null, 1);
    }

    /**
     * Tests the preGetRecommendStyleImage method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testPreGetRecommendStyleImageException() throws Throwable {
        // Arrange
        QueryRecommendParam param = QueryRecommendParam.builder().build();
        when(recommendServiceFuture.recommend(any(), any())).thenThrow(new Exception());
        // Act
        recommendServiceWrapper.preGetRecommendStyleImage(param);
        // Assert
        // Exception is expected
    }

    /**
     * 测试正常流程，playCenterService.executePlay 成功执行并返回结果
     */
    @Test
    public void testGetRecommendCombineDealV2Success() throws Throwable {
        // arrange
        BuyMoreSaveMoreCtx ctx = new BuyMoreSaveMoreCtx();
        EnvCtx envCtx = new EnvCtx();
        ctx.setEnvCtx(envCtx);
        BuyMoreSaveMoreReq req = new BuyMoreSaveMoreReq();
        // Ensure source is set
        req.setSource("mainChannel");
        ctx.setReq(req);
        PlayExecuteRequest playExecuteRequest = new PlayExecuteRequest();
        PlayExecuteResponse playExecuteResponse = new PlayExecuteResponse();
        playExecuteResponse.setResult("success");
        when(playCenterService.executePlay(any(PlayExecuteRequest.class))).thenReturn(playExecuteResponse);
        // act
        String result = recommendServiceWrapper.getRecommendCombineDealV2(ctx, 1, 10);
        // assert
        assertEquals("success", result);
        verify(playCenterService, times(1)).executePlay(any(PlayExecuteRequest.class));
    }

    /**
     * 测试异常流程，playCenterService.executePlay 抛出 TException
     */
    @Test
    public void testGetRecommendCombineDealV2ThrowsTException() throws Throwable {
        // arrange
        BuyMoreSaveMoreCtx ctx = new BuyMoreSaveMoreCtx();
        EnvCtx envCtx = new EnvCtx();
        ctx.setEnvCtx(envCtx);
        BuyMoreSaveMoreReq req = new BuyMoreSaveMoreReq();
        // Ensure source is set
        req.setSource("mainChannel");
        ctx.setReq(req);
        when(playCenterService.executePlay(any(PlayExecuteRequest.class))).thenThrow(new TException("Service error"));
        // act
        String result = recommendServiceWrapper.getRecommendCombineDealV2(ctx, 1, 10);
        // assert
        assertNull(result);
        verify(playCenterService, times(1)).executePlay(any(PlayExecuteRequest.class));
    }

    /**
     * 测试边界场景，ctx 为 null
     */
    @Test(expected = NullPointerException.class)
    public void testGetRecommendCombineDealV2WithNullCtx() throws Throwable {
        // arrange
        BuyMoreSaveMoreCtx ctx = null;
        // act
        recommendServiceWrapper.getRecommendCombineDealV2(ctx, 1, 10);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试边界场景，playCenterService 为 null
     */
    @Test(expected = NullPointerException.class)
    public void testGetRecommendCombineDealV2WithNullPlayCenterService() throws Throwable {
        // arrange
        BuyMoreSaveMoreCtx ctx = new BuyMoreSaveMoreCtx();
        EnvCtx envCtx = new EnvCtx();
        ctx.setEnvCtx(envCtx);
        BuyMoreSaveMoreReq req = new BuyMoreSaveMoreReq();
        // Ensure source is set
        req.setSource("mainChannel");
        ctx.setReq(req);
        // Use reflection to set the private field to null
        Field playCenterServiceField = RecommendServiceWrapper.class.getDeclaredField("playCenterService");
        playCenterServiceField.setAccessible(true);
        playCenterServiceField.set(recommendServiceWrapper, null);
        // act
        recommendServiceWrapper.getRecommendCombineDealV2(ctx, 1, 10);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试美团平台、iOS客户端、非特团渠道的场景
     */
    @Test
    public void testBuildRequestMtPlatformIosNonCostEffective() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(true);
        // iOS
        when(envCtx.getClientType()).thenReturn(100502);
        when(ctx.getReq()).thenReturn(req);
        when(req.getSource()).thenReturn("homepage");
        when(req.getCityId()).thenReturn(1);
        when(req.getUserLat()).thenReturn(39.9042);
        when(req.getUserLng()).thenReturn(116.4074);

        // act
        Map<String, String> result = wrapper.buildRequest(ctx, 1, 10);
        // assert
        assertEquals("527", result.get("bizId"));
        assertEquals("1", result.get("platformType"));
        assertEquals("7", result.get("productType"));
        assertEquals("0", result.get("osType"));
        assertEquals("mainChannel", result.get("locationCode"));
        assertEquals("1", result.get("cityId"));
        assertEquals("39.9042", result.get("lat"));
        assertEquals("116.4074", result.get("lng"));
        assertEquals("1", result.get("pageNumber"));
        assertEquals("10", result.get("pageSize"));
    }

    /**
     * 测试点评平台、Android客户端、特团渠道的场景
     */
    @Test
    public void testBuildRequestDpPlatformAndroidCostEffective() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(false);
        // Android
        when(envCtx.getClientType()).thenReturn(100501);
        when(ctx.getReq()).thenReturn(req);
        when(req.getSource()).thenReturn(RequestSourceEnum.COST_EFFECTIVE.getSource());
        when(req.getCityId()).thenReturn(2);
        when(req.getUserLat()).thenReturn(31.2304);
        when(req.getUserLng()).thenReturn(121.4737);

        // act
        Map<String, String> result = wrapper.buildRequest(ctx, 1, 10);
        // assert
        assertEquals("527", result.get("bizId"));
        assertEquals("2", result.get("platformType"));
        assertEquals("7", result.get("productType"));
        assertEquals("1", result.get("osType"));
        assertEquals("costEffective", result.get("locationCode"));
        assertEquals("2", result.get("cityId"));
        assertEquals("2", result.get("dpCityId"));
        assertEquals("31.2304", result.get("lat"));
        assertEquals("121.4737", result.get("lng"));
        assertEquals("1", result.get("pageNumber"));
        assertEquals("10", result.get("pageSize"));
    }

    /**
     * 测试美团平台、Android客户端、非特团渠道的场景
     */
    @Test
    public void testBuildRequestMtPlatformAndroidNonCostEffective() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(true);
        // Android
        when(envCtx.getClientType()).thenReturn(100501);
        when(ctx.getReq()).thenReturn(req);
        when(req.getSource()).thenReturn("homepage");
        when(req.getCityId()).thenReturn(3);
        when(req.getUserLat()).thenReturn(22.3964);
        when(req.getUserLng()).thenReturn(114.1095);

        // act
        Map<String, String> result = wrapper.buildRequest(ctx, 1, 10);
        // assert
        assertEquals("527", result.get("bizId"));
        assertEquals("1", result.get("platformType"));
        assertEquals("7", result.get("productType"));
        assertEquals("1", result.get("osType"));
        assertEquals("mainChannel", result.get("locationCode"));
        assertEquals("3", result.get("cityId"));
        assertEquals("22.3964", result.get("lat"));
        assertEquals("114.1095", result.get("lng"));
        assertEquals("1", result.get("pageNumber"));
        assertEquals("10", result.get("pageSize"));
    }

    /**
     * 测试点评平台、iOS客户端、特团渠道的场景
     */
    @Test
    public void testBuildRequestDpPlatformIosCostEffective() throws Throwable {
        // arrange
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.isMt()).thenReturn(false);
        // iOS
        when(envCtx.getClientType()).thenReturn(100502);
        when(ctx.getReq()).thenReturn(req);
        when(req.getSource()).thenReturn(RequestSourceEnum.COST_EFFECTIVE.getSource());
        when(req.getCityId()).thenReturn(4);
        when(req.getUserLat()).thenReturn(23.1291);
        when(req.getUserLng()).thenReturn(113.2644);

        // act
        Map<String, String> result = wrapper.buildRequest(ctx, 1, 10);
        // assert
        assertEquals("527", result.get("bizId"));
        assertEquals("2", result.get("platformType"));
        assertEquals("7", result.get("productType"));
        assertEquals("0", result.get("osType"));
        assertEquals("costEffective", result.get("locationCode"));
        assertEquals("4", result.get("cityId"));
        assertEquals("4", result.get("dpCityId"));
        assertEquals("23.1291", result.get("lat"));
        assertEquals("113.2644", result.get("lng"));
        assertEquals("1", result.get("pageNumber"));
        assertEquals("10", result.get("pageSize"));
    }
}
