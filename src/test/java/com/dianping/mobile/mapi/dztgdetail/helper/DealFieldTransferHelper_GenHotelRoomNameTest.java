package com.dianping.mobile.mapi.dztgdetail.helper;

import com.meituan.service.mobile.prometheus.model.DealModel;
import org.json.JSONObject;
import org.junit.runner.RunWith;
import java.util.HashMap;
import java.util.Map;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Cons;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealFieldTransferHelper_GenHotelRoomNameTest {

    /**
     * 测试 genHotelRoomName 方法，当 attrs 为空时，应返回空字符串
     */
    @Test
    public void testGenHotelRoomNameAttrsIsNull() throws Throwable {
        DealModel dealModel = new DealModel();
        dealModel.setAttrs(null);
        String result = DealFieldTransferHelper.genHotelRoomName(dealModel);
        assertEquals("", result);
    }

    /**
     * 测试 genHotelRoomName 方法，当 cate 是酒店代金券时，应返回 dealModel 的 mtitle
     */
    @Test
    public void testGenHotelRoomNameCateIsHotelCoupon() throws Throwable {
        DealModel dealModel = new DealModel();
        dealModel.setMtitle("mtitle");
        Map<Integer, String> attrs = new HashMap<>();
        attrs.put(Cons.ATTRS_CATE, String.valueOf(Cons.ATTRS_VALUE_HOTEL_COUPON_CATE));
        dealModel.setAttrs(attrs);
        String result = DealFieldTransferHelper.genHotelRoomName(dealModel);
        assertEquals("mtitle", result);
    }

    /**
     * 测试 genHotelRoomName 方法，当酒店信息不完整时，应返回空字符串
     */
    @Test
    public void testGenHotelRoomNameHotelInfoIncomplete() throws Throwable {
        DealModel dealModel = new DealModel();
        Map<Integer, String> attrs = new HashMap<>();
        attrs.put(1, "hotel info");
        dealModel.setAttrs(attrs);
        String result = DealFieldTransferHelper.genHotelRoomName(dealModel);
        assertEquals("", result);
    }

    /**
     * 测试 genHotelRoomName 方法，当 roomInfo 或 roomNames 为空时，应返回空字符串
     */
    @Test
    public void testGenHotelRoomNameRoomInfoOrRoomNamesIsNull() throws Throwable {
        DealModel dealModel = new DealModel();
        Map<Integer, String> attrs = new HashMap<>();
        attrs.put(1, "room info");
        attrs.put(2, "room names");
        dealModel.setAttrs(attrs);
        String result = DealFieldTransferHelper.genHotelRoomName(dealModel);
        assertEquals("", result);
    }

    /**
     * 测试 genHotelRoomName 方法，当 roomInfo 和 roomNames 类型为钟点房时，应返回相应的房间名称
     */
    @Test
    public void testGenHotelRoomNameRoomInfoAndRoomNamesTypeIsHourRoom() throws Throwable {
        DealModel dealModel = new DealModel();
        Map<Integer, String> attrs = new HashMap<>();
        attrs.put(Cons.ATTRS_ROOM_INFO, "{\"key\":\"HR\",\"label\":{\"hourCount\":\"2\"}}");
        attrs.put(Cons.ATTRS_ROOM_NAMES, "[{\"value\":\"room1\"},{\"value\":\"room2\"}]");
        dealModel.setAttrs(attrs);
        String result = DealFieldTransferHelper.genHotelRoomName(dealModel);
        assertEquals("入住2小时，room1/room22选1", result);
    }

    /**
     * 测试 genHotelRoomName 方法，当 roomInfo 和 roomNames 类型为全日房时，应返回相应的房间名称
     */
    @Test
    public void testGenHotelRoomNameRoomInfoAndRoomNamesTypeIsDayRoom() throws Throwable {
        DealModel dealModel = new DealModel();
        Map<Integer, String> attrs = new HashMap<>();
        attrs.put(Cons.ATTRS_ROOM_INFO, "{\"key\":\"D\",\"label\":{\"number\":\"2\"}}");
        attrs.put(Cons.ATTRS_ROOM_NAMES, "[{\"value\":\"room1\"},{\"value\":\"room2\"}]");
        dealModel.setAttrs(attrs);
        String result = DealFieldTransferHelper.genHotelRoomName(dealModel);
        assertEquals("入住2晚，room1/room22选1", result);
    }

    /**
     * 测试 genHotelRoomName 方法，当出现异常时，应返回 dealModel 的 mtitle
     */
    @Test
    public void testGenHotelRoomNameException() throws Throwable {
        DealModel dealModel = new DealModel();
        dealModel.setMtitle("mtitle");
        Map<Integer, String> attrs = new HashMap<>();
        attrs.put(Cons.ATTRS_ROOM_INFO, "{\"key\":\"D\",\"label\":{\"number\":\"abc\"}}");
        attrs.put(Cons.ATTRS_ROOM_NAMES, "[{\"value\":\"room1\"},{\"value\":\"room2\"}]");
        dealModel.setAttrs(attrs);
        String result = DealFieldTransferHelper.genHotelRoomName(dealModel);
        assertEquals("mtitle", result);
    }
}
