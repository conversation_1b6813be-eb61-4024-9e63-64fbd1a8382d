package com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail;

import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperCacheWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailTradeModuleProcessorIsEnableTest {

    @InjectMocks
    private ProductDetailTradeModuleProcessor processor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO categoryDTO;

    @Mock
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private DouHuService douHuService;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(processor, "mapperCacheWrapper", mapperCacheWrapper);
        ReflectionTestUtils.setField(processor, "douHuService", douHuService);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(mapperCacheWrapper.fetchSimilarDealId(anyInt(), anyString())).thenReturn(false);
    }

    /**
     * Test isEnable when country subsidy service type ID matches
     */
    @Test
    public void testIsEnable_WhenCountrySubsidyServiceTypeMatches() throws Throwable {
        // arrange
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(dealCtx.isMt()).thenReturn(true);
        when(categoryDTO.getServiceTypeId()).thenReturn(1234L);
        when(dealCtx.getDealId4P()).thenReturn(123);
        // Mock DouHuService response
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        // One of the EXP_RESULTS values
        abConfig.setExpResult("c");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuService.getMagicCouponEnhancementAbTestResult(any())).thenReturn(moduleAbConfig);
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test isEnable when country subsidy service type ID doesn't match
     */
    @Test
    public void testIsEnable_WhenCountrySubsidyServiceTypeDoesNotMatch() throws Throwable {
        // arrange
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(dealCtx.isMt()).thenReturn(false);
        when(categoryDTO.getServiceTypeId()).thenReturn(9999L);
        when(dealCtx.getDealId4P()).thenReturn(123);
        // Mock DouHuService response
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }
}
