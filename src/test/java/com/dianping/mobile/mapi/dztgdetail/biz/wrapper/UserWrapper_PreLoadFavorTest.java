package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.userremote.service.collection.FavorService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class UserWrapper_PreLoadFavorTest {

    @InjectMocks
    private UserWrapper userWrapper;

    @Mock
    private FavorService favorServiceFuture;

    @Mock
    private Future mockFuture;

    private MockedStatic<Lion> lionMockedStatic;

    private MockedStatic<FutureFactory> futureFactoryMockedStatic;

    @Before
    public void setUp() {
        futureFactoryMockedStatic = mockStatic(FutureFactory.class);
        futureFactoryMockedStatic.when(FutureFactory::getFuture).thenReturn(mockFuture);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        futureFactoryMockedStatic.close();
        lionMockedStatic.close();
    }

    @Test
    public void testPreLoadFavorUserIdLessThanOrEqualToZero() throws Throwable {
        Future result = userWrapper.preLoadFavor(0, 1);
        assertNull(result);
    }

    @Test
    public void testPreLoadFavorDealFavoriteServiceNotEnabled() throws Throwable {
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), eq(false))).thenReturn(false);
        Future result = userWrapper.preLoadFavor(1, 1);
        assertNull(result);
    }

    @Test
    public void testPreLoadFavorExceptionOccurred() throws Throwable {
        lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), eq(true))).thenReturn(true);
        doThrow(new RuntimeException()).when(favorServiceFuture).loadFaovr(anyLong(), anyInt(), anyString());
        Future result = userWrapper.preLoadFavor(1, 1);
        // Expecting null due to exception in service call
        assertNull(result);
    }
}
