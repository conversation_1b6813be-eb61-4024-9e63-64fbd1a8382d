package com.dianping.mobile.mapi.dztgdetail.mq;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.meituan.mafka.client.bean.MafkaConsumer;
import com.meituan.mafka.client.consumer.IMessageListener;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MafkaConfigTest {

    private MafkaConfig mafkaConfig = new MafkaConfig();

    @Mock
    private Environment environment;

    /**
     * 测试 unifiedMainInterfaceResultConsumer 方法是否返回非空实例
     */
    @Test
    public void testUnifiedMainInterfaceResultConsumer_ReturnsNonNull() {
        // act
        UnifiedMainInterfaceResultConsumer result = mafkaConfig.unifiedMainInterfaceResultConsumer();
        // assert
        assertNotNull("返回的 UnifiedMainInterfaceResultConsumer 实例不应为 null", result);
    }

    /**
     * 测试 unifiedMainInterfaceResultConsumer 方法返回正确类型的实例
     */
    @Test
    public void testUnifiedMainInterfaceResultConsumer_ReturnsCorrectType() {
        // act
        UnifiedMainInterfaceResultConsumer result = mafkaConfig.unifiedMainInterfaceResultConsumer();
        // assert
        assertTrue("返回的实例应该是 UnifiedMainInterfaceResultConsumer 类型", result instanceof UnifiedMainInterfaceResultConsumer);
    }

    /**
     * 测试多次调用 unifiedMainInterfaceResultConsumer 方法返回不同实例
     */
    @Test
    public void testUnifiedMainInterfaceResultConsumer_ReturnsNewInstanceEachTime() {
        // act
        UnifiedMainInterfaceResultConsumer firstInstance = mafkaConfig.unifiedMainInterfaceResultConsumer();
        UnifiedMainInterfaceResultConsumer secondInstance = mafkaConfig.unifiedMainInterfaceResultConsumer();
        // assert
        assertNotNull("第一个实例不应为 null", firstInstance);
        assertNotNull("第二个实例不应为 null", secondInstance);
        assertTrue("每次调用应该返回不同的实例", firstInstance != secondInstance);
    }

    /**
     * 测试dealChangeConsumer方法是否能正确创建并返回一个新的DealChangeConsumer对象
     */
    @Test
    public void testDealChangeConsumer() throws Throwable {
        // arrange
        MafkaConfig mafkaConfig = new MafkaConfig();
        // act
        DealChangeConsumer result = mafkaConfig.dealChangeConsumer();
        // assert
        assertNotNull("DealChangeConsumer should not be null", result);
        assertTrue("Result should be instance of DealChangeConsumer", result instanceof DealChangeConsumer);
    }

    /**
     * 测试多次调用dealChangeConsumer方法时是否每次都返回新的实例
     */
    @Test
    public void testDealChangeConsumerCreatesNewInstance() throws Throwable {
        // arrange
        MafkaConfig mafkaConfig = new MafkaConfig();
        // act
        DealChangeConsumer result1 = mafkaConfig.dealChangeConsumer();
        DealChangeConsumer result2 = mafkaConfig.dealChangeConsumer();
        // assert
        assertNotNull("First DealChangeConsumer should not be null", result1);
        assertNotNull("Second DealChangeConsumer should not be null", result2);
        assertNotSame("Should create different instances", result1, result2);
    }

    /**
     * 测试 mafkaDealChangeConsumer 方法
     * 验证：
     * 1. MafkaConsumer 对象创建成功
     * 2. 所有必要的属性都被正确设置
     * 3. DealChangeConsumer 被正确注入为监听器
     */
    @Test
    public void testMafkaDealChangeConsumerNormal() throws Throwable {
        // arrange
        MafkaConfig mafkaConfig = new MafkaConfig();
        // act
        MafkaConsumer result = mafkaConfig.mafkaDealChangeConsumer();
        // assert
        assertNotNull("MafkaConsumer should not be null", result);
        assertEquals("Namespace should be daozong", "daozong", result.getNamespace());
        assertEquals("Topic should be tpd_deal_publish_change", "tpd_deal_publish_change", result.getTopic());
        assertEquals("Group should be deal.sku.cache", "deal.sku.cache", result.getGroup());
        assertEquals("ClassName should be java.lang.String", "java.lang.String", result.getClassName());
        assertNotNull("Listener should not be null", result.getListener());
        assertTrue("Listener should be instance of DealChangeConsumer", result.getListener() instanceof DealChangeConsumer);
        // verify appkey is set (we can't verify the exact value since it comes from Environment.getAppName())
        assertNotNull("Appkey should not be null", result.getAppkey());
    }

    /**
     * 测试 mafkaDealChangeConsumer 方法的返回值不为空
     */
    @Test
    public void testMafkaDealChangeConsumerNotNull() throws Throwable {
        // arrange
        MafkaConfig mafkaConfig = new MafkaConfig();
        // act
        MafkaConsumer result = mafkaConfig.mafkaDealChangeConsumer();
        // assert
        assertNotNull("MafkaConsumer instance should not be null", result);
    }

    /**
     * 测试 mafkaDealChangeConsumer 方法中监听器的设置
     */
    @Test
    public void testMafkaDealChangeConsumerListener() throws Throwable {
        // arrange
        MafkaConfig mafkaConfig = new MafkaConfig();
        // act
        MafkaConsumer result = mafkaConfig.mafkaDealChangeConsumer();
        // assert
        assertNotNull("Listener should not be null", result.getListener());
        assertTrue("Listener should be instance of DealChangeConsumer", result.getListener() instanceof DealChangeConsumer);
    }
}
