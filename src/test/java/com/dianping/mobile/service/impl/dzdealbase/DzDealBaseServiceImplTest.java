package com.dianping.mobile.service.impl.dzdealbase;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import com.alibaba.fastjson.JSON;
import com.dianping.deal.dzdealbase.response.DzDealBaseResponse;
import com.dianping.mobile.framework.base.datatypes.HttpCode;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor.DzDealBaseExecutor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DzDealBaseServiceImplTest {

    @InjectMocks
    private DzDealBaseServiceImpl dzDealBaseService;

    @Mock
    private DzDealBaseExecutor dzDealBaseExecutor;

    /**
     * Test case for null input parameters
     */
    @Test
    public void testExecute_WhenBothParamsNull_ShouldReturnFailResponse() throws Throwable {
        // arrange
        String dealBaseReqStr = null;
        String envCtxStr = null;
        // act
        DzDealBaseResponse<String> response = dzDealBaseService.execute(dealBaseReqStr, envCtxStr);
        // assert
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for blank dealBaseReqStr
     */
    @Test
    public void testExecute_WhenDealBaseReqStrBlank_ShouldReturnFailResponse() throws Throwable {
        // arrange
        String dealBaseReqStr = "";
        String envCtxStr = "{\"clientType\":1}";
        // act
        DzDealBaseResponse<String> response = dzDealBaseService.execute(dealBaseReqStr, envCtxStr);
        // assert
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for blank envCtxStr
     */
    @Test
    public void testExecute_WhenEnvCtxStrBlank_ShouldReturnFailResponse() throws Throwable {
        // arrange
        String dealBaseReqStr = "{\"dealgroupid\":123}";
        String envCtxStr = "";
        // act
        DzDealBaseResponse<String> response = dzDealBaseService.execute(dealBaseReqStr, envCtxStr);
        // assert
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for missing required field in dealBaseReqStr
     */
    @Test
    public void testExecute_WhenMissingDealGroupId_ShouldReturnFailResponse() throws Throwable {
        // arrange
        String dealBaseReqStr = "{\"someOtherField\":\"value\"}";
        String envCtxStr = "{\"clientType\":1}";
        // act
        DzDealBaseResponse<String> response = dzDealBaseService.execute(dealBaseReqStr, envCtxStr);
        // assert
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for null DealGroupPBO in successful response
     */
    @Test
    public void testExecute_WhenNullDealGroupPBO_ShouldReturnFailResponse() throws Throwable {
        // arrange
        String dealBaseReqStr = "{\"dealgroupid\":123}";
        String envCtxStr = "{\"clientType\":1}";
        CommonMobileResponse successResponse = new CommonMobileResponse(null);
        successResponse.setStatusCode(HttpCode.HTTPOK);
        when(dzDealBaseExecutor.getExecuteResult(any(), any(), any(), eq(true))).thenReturn(successResponse);
        // act
        DzDealBaseResponse<String> response = dzDealBaseService.execute(dealBaseReqStr, envCtxStr);
        // assert
        assertFalse(response.isSuccess());
        assertTrue(response.getMsg().contains("查询失败"));
    }

    /**
     * Test case for successful execution
     */
    @Test
    public void testExecute_WhenSuccessful_ShouldReturnSuccessResponse() throws Throwable {
        // arrange
        String dealBaseReqStr = "{\"dealgroupid\":123}";
        String envCtxStr = "{\"clientType\":1}";
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        dealGroupPBO.setCategoryId(1);
        CommonMobileResponse successResponse = new CommonMobileResponse(dealGroupPBO);
        successResponse.setStatusCode(HttpCode.HTTPOK);
        when(dzDealBaseExecutor.getExecuteResult(any(), any(), any(), eq(true))).thenReturn(successResponse);
        // act
        DzDealBaseResponse<String> response = dzDealBaseService.execute(dealBaseReqStr, envCtxStr);
        // assert
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
    }

    /**
     * Test case for executor throwing exception
     */
    @Test
    public void testExecute_WhenExecutorThrowsException_ShouldReturnFailResponse() throws Throwable {
        // arrange
        String dealBaseReqStr = "{\"dealgroupid\":123}";
        String envCtxStr = "{\"clientType\":1}";
        String errorMessage = "Test error";
        when(dzDealBaseExecutor.getExecuteResult(any(), any(), any(), eq(true))).thenThrow(new RuntimeException(errorMessage));
        // act
        DzDealBaseResponse<String> response = dzDealBaseService.execute(dealBaseReqStr, envCtxStr);
        // assert
        assertFalse(response.isSuccess());
        assertEquals(errorMessage, response.getMsg());
    }

    /**
     * Test case for non-HTTPOK status code
     */
    @Test
    public void testExecute_WhenNonHttpOkStatus_ShouldReturnFailResponse() throws Throwable {
        // arrange
        String dealBaseReqStr = "{\"dealgroupid\":123}";
        String envCtxStr = "{\"clientType\":1}";
        CommonMobileResponse errorResponse = new CommonMobileResponse(new DealGroupPBO());
        errorResponse.setStatusCode(StatusCode.ERROR);
        when(dzDealBaseExecutor.getExecuteResult(any(), any(), any(), eq(true))).thenReturn(errorResponse);
        // act
        DzDealBaseResponse<String> response = dzDealBaseService.execute(dealBaseReqStr, envCtxStr);
        // assert
        assertFalse(response.isSuccess());
        assertTrue(response.getMsg().contains("查询失败"));
    }
}
