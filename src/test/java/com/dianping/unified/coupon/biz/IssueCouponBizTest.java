package com.dianping.unified.coupon.biz;

import com.dianping.pay.api.biz.activity.IssueCouponBiz;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponIssueActivityQueryContext;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedIssueResult;
import com.dianping.pay.api.util.JsonUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class IssueCouponBizTest extends TestBase {
    @Autowired
    private IssueCouponBiz issueCouponBiz;

    @Test
    @Ignore
    public void testIssueLQG() {
        CouponIssueActivityQueryContext context = new CouponIssueActivityQueryContext();
        context.setCouponGroupId(**********);
        context.setUnifiedCouponGroupId("**********");
        IssueCouponRequest issueCouponRequest = new IssueCouponRequest();
        issueCouponRequest.setIsDpClient(false);
        issueCouponRequest.setUserId(5095400922l);
        UnifiedIssueResult result = issueCouponBiz.issuePlatformCoupon(context, issueCouponRequest);
        System.out.println(JsonUtils.toJson(result));
        Assert.assertTrue(result != null);
    }
}
