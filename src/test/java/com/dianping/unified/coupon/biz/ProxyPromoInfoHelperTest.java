package com.dianping.unified.coupon.biz;

import com.alibaba.fastjson.JSON;
import com.dianping.api.constans.CommonConstants;
import com.dianping.pay.api.biz.activity.newloader.dto.CouponActivityContext;
import com.dianping.pay.api.entity.issuecoupon.CouponDetailInfo;
import com.dianping.pay.api.entity.issuecoupon.CouponPromoInfo;
import com.dianping.pay.api.enums.QueryShopCouponTypeEnum;
import com.dianping.pay.api.util.ProxyPromoInfoHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmktproxy.queryclient.proxy.*;

import java.util.HashMap;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class ProxyPromoInfoHelperTest extends TestBase{
    @Autowired
    private static ProxyPromoInfoHelper proxyPromoInfoHelper;

    @Test
    public void getPromotionsTest() {
        CouponActivityContext context = new CouponActivityContext();
        PromotionDTOResult promotionDTOResult = new PromotionDTOResult();
        context.setPromotionDTOResult(promotionDTOResult);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
        context.setDealPromoProxy(true);
        List<GetPromotionDTO> promotions = proxyPromoInfoHelper.getPromotions(context);
        Assert.assertTrue(CollectionUtils.isEmpty(promotions));
    }

    @Test
    public void genCouponInfoItemTest() {
        CouponActivityContext context = new CouponActivityContext();
        PromotionDTOResult promotionDTOResult = new PromotionDTOResult();
        context.setPromotionDTOResult(promotionDTOResult);
        context.setQueryShopCouponType(QueryShopCouponTypeEnum.BY_DEAL.getCode());
        context.setDealPromoProxy(true);
        CouponPromoInfo couponPromoInfo = ProxyPromoInfoHelper.genCouponInfoItem(context);
        Assert.assertTrue(couponPromoInfo == null);
    }

    @Test
    public void genFinancialGovConsumeCouponInfoItemTest() {
        CouponActivityContext couponActivityContext = new CouponActivityContext();
        PromotionDTOResult promotionDTOResult = new PromotionDTOResult();
        couponActivityContext.setPromotionDTOResult(promotionDTOResult);
        List<GetPromotionDTO> promos = Lists.newArrayList();
        promotionDTOResult.setGetPromotionDTO(promos);
        GetPromotionDTO getPromotionDTO = new GetPromotionDTO();
        promos.add(getPromotionDTO);
        PromotionDTO promotionDTO = new PromotionDTO();
        getPromotionDTO.setPromotionDTO(promotionDTO);
        FinancialCouponDTO consumeCouponDTO = new FinancialCouponDTO();
        promotionDTO.setFinancialCouponDTO(consumeCouponDTO);
        getPromotionDTO.setPromotionType(PromotionType.FINANCIAL_COUPON);
        consumeCouponDTO.setAssignedStatus(AssignedStatusEnum.ALREADY_ASSIGNED);
        consumeCouponDTO.setAvailable(true);
        consumeCouponDTO.setFinancialCouponCategoryEnum(FinancialCouponCategoryEnum.GOVERNMENT_CONSUME_COUPON);
        consumeCouponDTO.setFinancialCouponSubTypeEnum(FinancialCouponSubTypeEnum.INSTANT_DISCOUNT_COUPON);
        consumeCouponDTO.setCouponValue("1024.6");
        CouponPromoInfo couponPromoInfo = ProxyPromoInfoHelper.genFinancialGovConsumeCouponInfoItem(couponActivityContext);
        Assert.assertNotNull(couponPromoInfo);
    }

    @Test
    public void genFinancialGovConsumeCouponDetailItemTest() {
        PromotionDTO promotionDTO = new PromotionDTO();
        FinancialCouponDTO consumeCouponDTO = new FinancialCouponDTO();
        consumeCouponDTO.setAssignedStatus(AssignedStatusEnum.ALREADY_ASSIGNED);
        consumeCouponDTO.setAvailable(true);
        consumeCouponDTO.setFinancialCouponCategoryEnum(FinancialCouponCategoryEnum.GOVERNMENT_CONSUME_COUPON);
        consumeCouponDTO.setFinancialCouponSubTypeEnum(FinancialCouponSubTypeEnum.FULL_DISCOUNT_COUPON);
        consumeCouponDTO.setCouponValue("1024.6");
        consumeCouponDTO.setMinConsumption("50000");

        promotionDTO.setFinancialCouponDTO(consumeCouponDTO);

        CouponDTO couponDTO = new CouponDTO();
        Map<String, String> fieldMap = Maps.newHashMap();
        Map<String, String> extMap = new HashMap<>();
        extMap.put(CommonConstants.PACKAGE_SECRET_KEY, "SK312313");
        fieldMap.put(PromotionPropertyEnum.FINANCE_EXT.getValue(), JSON.toJSONString(extMap));
        couponDTO.setPromotionFieldMap(fieldMap);
        consumeCouponDTO.setPromotionFieldMap(fieldMap);
        promotionDTO.setCouponDTO(couponDTO);
        CouponDetailInfo couponDetailInfo = ProxyPromoInfoHelper.genFinancialGovConsumeCouponDetailItem(promotionDTO);
        Assert.assertNotNull(couponDetailInfo);
        Assert.assertEquals(couponDetailInfo.getPackageSecretKey(), "SK312313");
//        Assert.assertEquals(couponDetailInfo.getPackageSecretKey(), "");
    }
}
