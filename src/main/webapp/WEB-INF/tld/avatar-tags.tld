<?xml version="1.0" encoding="UTF-8"?>
<taglib version="2.0" xmlns="http://java.sun.com/xml/ns/j2ee"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd">
	<display-name>"DP Tags"</display-name>
	<tlib-version>1.0</tlib-version>
	<short-name>ava</short-name>
	<uri>/avatar-tags</uri>
	<tag>
		<name>content-place-holder</name>
		<tag-class>com.dianping.combiz.web.tag.common.ContentPlaceHolderTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<description><![CDATA[Which content in request attributes to render]]></description>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>content</name>
		<tag-class>com.dianping.combiz.web.tag.common.ContentTag</tag-class>
		<body-content>JSP</body-content>
		<attribute>
			<description><![CDATA[Which content in request attributes to render]]></description>
			<name>holderid</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>static-resource</name>
		<tag-class>com.dianping.combiz.web.tag.common.StaticResourceTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<description><![CDATA[Which resource to be retrieved]]></description>
			<name>resource</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[Which resources to be retrieved]]></description>
			<name>resources</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[resources' file type]]></description>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[whether decorate resource location by html element]]></description>
			<name>decorate</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[alt attribute if img type]]></description>
			<name>alt</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
		<tag>
		<name>profiler</name>
		<tag-class>com.dianping.combiz.web.tag.common.ProfilerTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<description><![CDATA[Whether to enable this tag]]></description>
			<name>enable</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>tracker</name>
		<tag-class>com.dianping.combiz.web.tag.common.TrackerTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<description><![CDATA[Whether to enable this tag]]></description>
			<name>enable</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
	
	<!-- for freemarker -->
	<tag>
		<name>contentPlaceHolder</name>
		<tag-class>com.dianping.combiz.web.tag.common.ContentPlaceHolderTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<description><![CDATA[Which content in request attributes to render]]></description>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>staticResource</name>
		<tag-class>com.dianping.combiz.web.tag.common.StaticResourceTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<description><![CDATA[Which resource to be retrieved]]></description>
			<name>resource</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[Which resources to be retrieved]]></description>
			<name>resources</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[resources' file type]]></description>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[whether decorate resource location by html element]]></description>
			<name>decorate</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[alt attribute if img type]]></description>
			<name>alt</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>extStaticResource</name>
		<tag-class>com.dianping.combiz.web.tag.biz.ExtStaticResourceTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<description><![CDATA[Which resource to be retrieved]]></description>
			<name>resource</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[Which resources to be retrieved]]></description>
			<name>resources</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[resources' file type]]></description>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[whether decorate resource location by html element]]></description>
			<name>decorate</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
		<attribute>
			<description><![CDATA[alt attribute if img type]]></description>
			<name>alt</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
</taglib>
