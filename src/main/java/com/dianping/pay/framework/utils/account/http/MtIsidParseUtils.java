package com.dianping.pay.framework.utils.account.http;

import com.dianping.pay.framework.utils.HttpClient;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class MtIsidParseUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(MtIsidParseUtils.class);

    private static final String MT_ISID_PARSE_URL = "http://i.vip.sankuai.com/api-in/v1/userinfo";

    public static long parseIsid(String isid) {
        long mtUserId = 0;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("isid", isid);
            String jsonResult = HttpClient.executeGet(param, MT_ISID_PARSE_URL, null);
            if (StringUtils.isNotBlank(jsonResult)) {
                IsidResponse response = new Gson().fromJson(jsonResult, IsidResponse.class);
                if (response.status == 0 && response.data != null) {
                    mtUserId = response.data.userId;
                } else {
                    LOGGER.warn(String.format("parseIsid failed: %s", jsonResult));
                }
            }
        } catch (Exception e) {
            LOGGER.error("parseIsid error", e);
        }
        LOGGER.info(String.format("parseIsid result: %s -> %s", isid, mtUserId));
        return mtUserId;
    }

    private class IsidResponse {
        int status;
        IsidData data;
    }

    private class IsidData {
        long userId;
    }
}
