package com.dianping.pay.framework.utils.shopUuid;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.entity.issuecoupon.IssueCouponRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuecouponRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuecouponcomponentRequest;
import com.dianping.pay.api.entity.issuecoupon.UnifiedissuemulticouponRequest;
import com.dianping.pay.api.util.DotUtils;
import com.dianping.pay.framework.utils.shopUuid.enums.ShopUuidStatusEnum;
import com.google.common.collect.Lists;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiUuidRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import lombok.extern.log4j.Log4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Component
@Log4j
public class ShopUuidUtils {

    @Autowired
    private DpPoiService sinaiDpPoiService;

    private static final int BATCH_SIZE = 50;

    private static final List<String> fields = Lists.newArrayList("shopId", "uuid");

    /**
     * UUID转化shopId
     *
     * @param uuId
     * @return
     */
    //单测
    public Long toShopId(String uuId) {
        if (uuId == null) {
            return null;
        }

        try {
            DpPoiUuidRequest dpPoiRequest = new DpPoiUuidRequest();
            dpPoiRequest.setUuids(Lists.newArrayList(uuId));
            dpPoiRequest.setFields(fields);
            List<DpPoiDTO> dpPoiDTOs = sinaiDpPoiService.findShopsByUuids(dpPoiRequest);
            if(CollectionUtils.isNotEmpty(dpPoiDTOs)) {
                return dpPoiDTOs.get(0).getShopId();
            }
        } catch (Exception e) {
            log.error("uuid toShopId error", e);
            DotUtils.dotForPoiIdException("findShopsByUuids", uuId);
        }
        return NumberUtils.toLong(uuId);
    }

    /**
     * UUID批量转化shopId
     *
     * @param uuIdList
     * @return
     */
    public List<Long> toShopIdList(List<String> uuIdList) {
        Map<String, Long> uUID2ShopIDMap = getUUID2ShopIDMap(uuIdList);
        if (MapUtils.isEmpty(uUID2ShopIDMap)) {
            return Collections.EMPTY_LIST;
        }

        List<Long> shopIdList = new ArrayList<>();
        for (String uuId : uuIdList) {
            if (uUID2ShopIDMap.containsKey(uuId) && uUID2ShopIDMap.get(uuId) != null) {
                shopIdList.add(uUID2ShopIDMap.get(uuId));
            } else {
                shopIdList.add(NumberUtils.toLong(uuId));
            }
        }
        return shopIdList;
    }

    /**
     * UUID批量转化shopId的map
     *
     * @param uuIdList
     * @return
     */
    public Map<String, Long> getUUID2ShopIDMap(List<String> uuIdList) {
        if (CollectionUtils.isEmpty(uuIdList)) {
            return MapUtils.EMPTY_MAP;
        }

        List<DpPoiDTO> dpPoiDTOList = getShopUuidDTOListByUUIdList(uuIdList);
        if (CollectionUtils.isEmpty(dpPoiDTOList)) {
            return MapUtils.EMPTY_MAP;
        }

        Map<String, Long> uUID2ShopIDMap = new HashMap<>();
        for (DpPoiDTO dpPoiDTO : dpPoiDTOList) {
            uUID2ShopIDMap.put(dpPoiDTO.getUuid(), dpPoiDTO.getShopId());
        }
        return uUID2ShopIDMap;
    }

    /**
     * 分批查询
     *
     * @param uuIdList
     * @return
     */
    public List<DpPoiDTO> getShopUuidDTOListByUUIdList(List<String> uuIdList) {
        List<DpPoiDTO> dpPoiDTOList = new ArrayList<>();
        List<List<String>> batchList = Lists.partition(uuIdList, BATCH_SIZE);
        try {
            for (List<String> list : batchList) {
                DpPoiUuidRequest dpPoiRequest = new DpPoiUuidRequest();
                dpPoiRequest.setUuids(list);
                dpPoiRequest.setFields(fields);
                dpPoiDTOList.addAll(sinaiDpPoiService.findShopsByUuids(dpPoiRequest));
            }
        } catch (Exception e) {
            log.error("uuid toShopDTO error", e);
            DotUtils.dotForPoiIdException("findShopsByUuids", StringUtils.join(uuIdList, ","));
        }
        return dpPoiDTOList;
    }

    private static final String PATTERN_SHOP= "\\w+";

    public boolean isShopUuid(String uuid) {
        return StringUtils.isNotBlank(uuid) && Pattern.matches(PATTERN_SHOP, uuid) && StringUtils.length(uuid) == 16;
    }

    public ShopUuidStatusEnum getUuidTransferStatus() {
        return ShopUuidStatusEnum.fromCode(Lion.getIntValue("mapi-pay-promo-web.uuid.transfer.status", 1));
    }



    //***********************************
    //对各种request的uuid处理
    //***********************************

    public void prepareUuidInRequest(IssueCouponRequest request, boolean isNative) {
        ShopUuidStatusEnum statusEnum = getUuidTransferStatus();
        if (statusEnum == ShopUuidStatusEnum.SHOPID_ONLY) {
            return;
        }
        if (statusEnum == ShopUuidStatusEnum.UUID_ONLY) {
            //如果只接受uid，则h5也不允许传int型的shopid
            if (!isNative) {
                if (StringUtils.isEmpty(request.getShopUuid()) && !isShopUuid(request.getShopIdStr())) {
                    Cat.logEvent("ShopId Forbidden", request.getShopIdStr());
                    throw new IllegalArgumentException("shopid forbidden");
                }
            }
        }

        //如果是H5的请求，需要兼容替换的模式
        if (!isNative) {
            if (StringUtils.isNotBlank(request.getShopIdStr()) && isShopUuid(request.getShopIdStr())) {
                request.setShopIdL(toShopId(request.getShopIdStr()));
            }
        }
        //如果shopUuid不为空，且shopId为空，则填充shopId
        if (StringUtils.isNotBlank(request.getShopUuid())) {
            Long shopId = toShopId(request.getShopUuid());
            if (shopId != null) {
                request.setShopIdL(shopId);
            }
        }
        //如果shopUuidList不为空，且shoIdList为空，则填充shopIdList
        if (CollectionUtils.isNotEmpty(request.getShopUuids())) {
            request.setShopIdLList(toShopIdList(request.getShopUuids()));
        }
    }

    public void prepareUuidInRequest(UnifiedissuecouponcomponentRequest request, boolean isNative) {
        //如果shopUuid不为空，则填充/覆盖shopId
        ShopUuidStatusEnum statusEnum = getUuidTransferStatus();
        if (statusEnum == ShopUuidStatusEnum.SHOPID_ONLY) {
            return;
        }
        if (statusEnum == ShopUuidStatusEnum.UUID_ONLY) {
            //如果只接受uid，则h5也不允许传int型的shopid
            if (!isNative) {
                if (StringUtils.isEmpty(request.getShopuuid()) && !isShopUuid(request.getShopIdStr())) {
                    Cat.logEvent("ShopId Forbidden", request.getShopIdStr());
                    throw new IllegalArgumentException("shopid forbidden");
                }
            }
        }
        //如果是H5的请求，需要兼容替换的模式
        if (!isNative) {
            if (StringUtils.isNotBlank(request.getShopIdStr()) && isShopUuid(request.getShopIdStr())) {
                request.setShopid(String.valueOf(toShopId(request.getShopIdStr())));
            }
        }

        if (StringUtils.isNotBlank(request.getShopuuid())) {
            request.setShopid(String.valueOf(toShopId(request.getShopuuid())));
        }
    }

    public void prepareUuidInRequest(UnifiedissuecouponRequest request, boolean isNative) {
        //如果shopUuid不为空，则填充/覆盖shopId
        ShopUuidStatusEnum statusEnum = getUuidTransferStatus();
        if (statusEnum == ShopUuidStatusEnum.SHOPID_ONLY) {
            return;
        }
        if (statusEnum == ShopUuidStatusEnum.UUID_ONLY) {
            //如果只接受uid，则h5也不允许传int型的shopid
            if (!isNative) {
                if (StringUtils.isEmpty(request.getShopuuid()) && !isShopUuid(request.getShopidStr())) {
                    Cat.logEvent("ShopId Forbidden", request.getShopidStr());
                    throw new IllegalArgumentException("shopid forbidden");
                }
            }
        }
        //如果是H5的请求，需要兼容替换的模式
        if (!isNative) {
            if (StringUtils.isNotBlank(request.getShopidStr()) && isShopUuid(request.getShopidStr())) {
                request.setShopidL(toShopId(request.getShopidStr()));
            }
        }

        if (StringUtils.isNotBlank(request.getShopuuid())) {
            request.setShopidL(toShopId(request.getShopuuid()));
        }
    }

    public void prepareUuidInRequest(UnifiedissuemulticouponRequest request, boolean isNative) {
        //如果shopUuid不为空，则填充/覆盖shopId
        ShopUuidStatusEnum statusEnum = getUuidTransferStatus();
        if (statusEnum == ShopUuidStatusEnum.SHOPID_ONLY) {
            return;
        }
        if (statusEnum == ShopUuidStatusEnum.UUID_ONLY) {
            //如果只接受uid，则h5也不允许传int型的shopid
            if (!isNative) {
                if (StringUtils.isEmpty(request.getShopuuid()) && !isShopUuid(request.getShopidStr())) {
                    Cat.logEvent("ShopId Forbidden", request.getShopidStr());
                    throw new IllegalArgumentException("shopid forbidden");
                }
            }
        }
        //如果是H5的请求，需要兼容替换的模式
        if (!isNative) {
            if (StringUtils.isNotBlank(request.getShopidStr()) && isShopUuid(request.getShopidStr())) {
                request.setShopid(toShopId(request.getShopidStr()) + "");
            }
        }

        if (StringUtils.isNotBlank(request.getShopuuid())) {
            request.setShopid(toShopId(request.getShopuuid()) + "");
        }
    }

    public void prepareUuidInRequest(GetPromoDeskRequest request, boolean isNative) {
        ShopUuidStatusEnum statusEnum = getUuidTransferStatus();
        if (statusEnum == ShopUuidStatusEnum.SHOPID_ONLY) {
            return;
        }
        if (statusEnum == ShopUuidStatusEnum.UUID_ONLY) {
            //如果只接受uuid，则h5也不允许传int型的shopid
            if (!isNative) {
                if (StringUtils.isEmpty(request.getShopuuid()) && !isShopUuid(request.getShopIdStr())) {
                    Cat.logEvent("ShopId Forbidden", request.getShopIdStr());
                    throw new IllegalArgumentException("shopid forbidden");
                }
            }
        }

        //如果是H5的请求，需要兼容替换的模式
        if (!isNative) {
            if (StringUtils.isNotBlank(request.getShopIdStr()) && isShopUuid(request.getShopIdStr())) {
                request.setShopIdL(toShopId(request.getShopIdStr()));
            }
        }
        //如果shopUuid不为空，则填充/覆盖shopId
        if (StringUtils.isNotBlank(request.getShopuuid())) {
            Long shopId = toShopId(request.getShopuuid());
            if (shopId != null) {
                request.setShopIdL(shopId);
            }
        }
    }
}