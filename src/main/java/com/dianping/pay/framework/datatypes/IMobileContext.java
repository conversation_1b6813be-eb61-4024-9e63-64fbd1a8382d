package com.dianping.pay.framework.datatypes;

import com.dianping.mobile.base.datatypes.enums.DeviceType;
import com.dianping.mobile.framework.cache.CacheContext;
import com.dianping.mobile.framework.datatypes.AppType;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.pay.framework.datatypes.enums.ClientType;
import org.apache.commons.fileupload.FileItem;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Set;

public interface IMobileContext {

    Set<String> getParameterKeys();

    String getParameter(String paramName);

    MobileHeader getHeader();

    String getUserIp();

    ClientType getClient();

    String getVersion();

    String getSource();

    String getCellPhoneType();

    String getUserAgent();

    long getUserId();

    void setUserId(long userId);

    Map<String, FileItem> getMultiParamMap();

    String getOs();

    String getProtocolVersion();

    boolean isPost();

    String getRequestId();

    String getRequestIdReffer();

    String getActionKey();

    /**
     * 获取原始的httpRequest
     */
    HttpServletRequest getRequest();

    byte[] getPicFile();

    // 缓存的上下文信息
    CacheContext getCacheContext();

    AppType getAppType();

    boolean isMtClient();

    DeviceType getDeviceType();
}
