package com.dianping.pay.api.biz.activity.newloader.dto;

import com.dianping.pay.promo.common.enums.User;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Data
public class ShopCartPromotionRequestContext {
    private String cx;
    private Double latitude;
    private Double longitude;
    private String dpid;

    private int cityId;

    private long dpUserId;
    private long mtUserId;

    private long dpShopIdL;
    private long mtShopIdL;

    private int payPlatform;
    //0:点评 1:美团
    private User userType;

    private List<Integer> skuIds;
    private List<Integer> dpDealGroupIds;
    private List<Integer> mtDealGroupIds;



    public boolean isMt(){
        return userType == User.MT;
    }


    public long getUserId(){
        return isMt()?mtUserId:dpUserId;
    }

    public long getShopIdL() {
        return isMt() ? mtShopIdL : dpShopIdL;
    }
}
