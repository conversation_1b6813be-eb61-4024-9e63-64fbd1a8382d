package com.dianping.pay.api.biz.discount;

import com.dianping.cat.Cat;
import com.dianping.combiz.spring.util.PropertiesLoaderSupportUtils;
import com.dianping.pay.api.beans.GetPromoDeskRequest;
import com.dianping.pay.api.beans.PromoDTOGroup;
import com.dianping.pay.api.beans.PromoProduct;
import com.dianping.pay.api.entity.promodesk.DiscountPromoEvent;
import com.dianping.pay.api.entity.promodesk.DiscountPromoEventGroup;
import com.dianping.pay.api.entity.promodesk.DiscountPromoTool;
import com.dianping.pay.common.enums.ProductCode;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.common.enums.PromoType;
import com.dianping.pay.promo.execute.service.dto.PromoExecuteDetailDTO;
import com.dianping.pay.promo.execute.service.dto.request.MtCampaignQueryRequest;
import com.dianping.pay.promo.execute.service.dto.request.MtMaitonCampaignQueryRequest;
import com.dianping.pay.promo.execute.service.dto.request.PromoExecuteQueryRequest;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;

public class MTPayPromoQueryMapper {

    private static final Map<Integer, List<Integer>> mutexProductCodesConfig = new HashMap<Integer, List<Integer>>();

    private static Map<Integer, Integer> templateIdMap = new HashMap<Integer, Integer>();
    static {
        Gson gson = new Gson();
        Type typeOfMap = new TypeToken<HashMap<Integer, Integer>>(){}.getType();
        templateIdMap = gson.fromJson(PropertiesLoaderSupportUtils.getProperty("mapi-pay-promo-web.queryAvailable.templateIdMap"), typeOfMap);
        mutexProductCodesConfig.put(ProductCode.MO2O2PAY.getCode(), Lists.newArrayList(ProductCode.TUANGOU.getCode()));
    }

    public static List<MtCampaignQueryRequest> buildRequest(GetPromoDeskRequest request, Collection<PromoProduct> promoProductList) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.discount.MTPayPromoQueryMapper.buildRequest(com.dianping.pay.api.beans.GetPromoDeskRequest,java.util.Collection)");
        List<MtCampaignQueryRequest> resultList = new ArrayList<MtCampaignQueryRequest>();
        for (PromoProduct product : promoProductList) {
            ProductType productType = ProductType.fromProductCode(product.getProductCode(), request.isMtClient());
            if (productType == null) {
                continue;
            }
            MtCampaignQueryRequest promoQueryRequest = new MtCampaignQueryRequest();
            promoQueryRequest.setUserId(request.getUserId());
            promoQueryRequest.setUuid(request.getClientInfo().getDpId());
            promoQueryRequest.setDealId(product.getProductId());
            promoQueryRequest.setClientVersion(request.getClientInfo().getVersion().getVersion());
            promoQueryRequest.setCityId(request.getCityId());
            promoQueryRequest.setPlatform(request.getClientInfo().getPlatform());
            promoQueryRequest.setPrice(product.getPrice());
            resultList.add(promoQueryRequest);
        }
        return resultList;
    }

    public static List<MtMaitonCampaignQueryRequest> buildShanhuiRequest(GetPromoDeskRequest request, Collection<PromoProduct> promoProductList) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.biz.discount.MTPayPromoQueryMapper.buildShanhuiRequest(com.dianping.pay.api.beans.GetPromoDeskRequest,java.util.Collection)");
        List<MtMaitonCampaignQueryRequest> resultList = new ArrayList<MtMaitonCampaignQueryRequest>();
        for (PromoProduct product : promoProductList) {
            ProductType productType = ProductType.fromProductCode(product.getProductCode(), request.isMtClient());
            if (productType == null) {
                continue;
            }
            MtMaitonCampaignQueryRequest promoQueryRequest = new MtMaitonCampaignQueryRequest();
            promoQueryRequest.setUserId(request.getUserId());
            promoQueryRequest.setUuid(request.getClientInfo().getDpId());
            promoQueryRequest.setPoiId(product.getProductId());
            promoQueryRequest.setClientVersion(request.getClientInfo().getVersion().getVersion());
            promoQueryRequest.setCityId(request.getCityId());
            promoQueryRequest.setPlatform(request.getClientInfo().getPlatform());
            promoQueryRequest.setPrice(product.getPrice());
            resultList.add(promoQueryRequest);
        }
        return resultList;
    }

    public static int toProductCode(int productType) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.biz.discount.MTPayPromoQueryMapper.toProductCode(int)");
        if (productType == ProductType.tuangou.value || productType == ProductType.mt_tuangou.value) {
            return ProductCode.TUANGOU.getCode();
        } else if (productType == ProductType.movie.value) {
            return ProductCode.MOVIE.getCode();
        } else if (productType == ProductType.takeaway.value) {
            return ProductCode.TAKEAWAY.getCode();
        } else if (productType == ProductType.shanhui.value || productType == ProductType.mt_shanhui.value) {
            return ProductCode.MO2O2PAY.getCode();
        } else if (productType == ProductType.toHome.value) {
            return ProductCode.TOHOME.getCode();
        } else if (productType == ProductType.ktv.value || productType == ProductType.mt_ktv.value) {
            return ProductCode.KTV.getCode();
        } else if (productType == ProductType.joy.value || productType == ProductType.mt_joy.value) {
            return ProductCode.JOY.getCode();
        } else {
            throw new IllegalArgumentException("unsupported promo productType");
        }
    }
}
