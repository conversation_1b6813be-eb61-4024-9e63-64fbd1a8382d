package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.pay.cashier.biz.enums.PaymentRule;

import java.io.Serializable;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

public class HongBaoPromoTool extends DPEncoder implements Serializable {

    private int id;
    private double balance;
    private String promoCipher;
    private List<Integer> mutexTools;
    private List<Integer> productCodes;
    private boolean canUse;

    public HongBaoPromoTool() {
        this.id = PaymentRule.HONGBAO.code;
        this.mutexTools = Arrays.asList(PaymentRule.COUPON.code, PaymentRule.SHOPCOUPON.code);
    }

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("id", 0x91b));
        l.add(new KeyValuePair("balance", 0x49bd));
        l.add(new KeyValuePair("promoCipher", 0xc706));
        l.add(new KeyValuePair("mutexTools", 0x2468));
        l.add(new KeyValuePair("productCodes", 0x2020));
        l.add(new KeyValuePair("canUse", 0x9069));
    }

    @Override
    public int getClassId() {
        return 0xcf6d;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }

    public String getPromoCipher() {
        return promoCipher;
    }

    public void setPromoCipher(String promoCipher) {
        this.promoCipher = promoCipher;
    }

    public List<Integer> getMutexTools() {
        return mutexTools;
    }

    public void setMutexTools(List<Integer> mutexTools) {
        this.mutexTools = mutexTools;
    }

    public List<Integer> getProductCodes() {
        return productCodes;
    }

    public void setProductCodes(List<Integer> productCodes) {
        this.productCodes = productCodes;
    }

    public boolean isCanUse() {
        return canUse;
    }

    public void setCanUse(boolean canUse) {
        this.canUse = canUse;
    }
}
