package com.dianping.pay.api.entity.issuecoupon;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by drin<PERSON> on 18/6/14.
 */

@Data
public class UnifiedIssueDetailResult  {

    private boolean isSuccess;

    private String resultMessage;

    private String unifiedCouponGroupId;

    private String unifiedCouponId;

    private Date beginTime;

    private Date endTime;

    private String couponGroupName;

    private String useUrl;

    private String toastMsg;

    public UnifiedIssueDetailResult() {
    }

    public UnifiedIssueDetailResult(boolean isSuccess, String resultMessage, String unifiedCouponGroupId) {
        this.isSuccess = isSuccess;
        this.resultMessage = resultMessage;
        this.unifiedCouponGroupId = unifiedCouponGroupId;
    }
}
