package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.cat.Cat;
import com.dianping.pay.cashier.biz.enums.PaymentRule;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class DiscountPromoTool extends DPEncoder implements Serializable {

    private int id;
    private List<DiscountPromoEventGroup> discountPromoEventGroups;

    public DiscountPromoTool() {
        this.id = PaymentRule.REDUCTION.code;
        this.discountPromoEventGroups = new ArrayList<DiscountPromoEventGroup>();
    }

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("id", 0x91b));
        l.add(new KeyValuePair("discountPromoEventGroups", 0x4f28));
    }

    @Override
    public int getClassId() {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.pay.api.entity.promodesk.DiscountPromoTool.getClassId()");
        return 0x4073;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.entity.promodesk.DiscountPromoTool.getSerializeList(com.dianping.api.domain.ClientInfo,com.dianping.api.domain.Version)");
        return l;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public List<DiscountPromoEventGroup> getDiscountPromoEventGroups() {
        return discountPromoEventGroups;
    }

    public void setDiscountPromoEventGroups(List<DiscountPromoEventGroup> discountPromoEventGroups) {
        this.discountPromoEventGroups = discountPromoEventGroups;
    }
}
