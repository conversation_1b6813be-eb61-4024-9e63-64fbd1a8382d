package com.dianping.pay.api.entity.issuecoupon;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class IssueCouponComponent extends DPEncoder implements Serializable {

    private String title;
    private List<String> promotionTagList = new ArrayList<String>();
    private List<IssueCouponOption> couponOptionList = new ArrayList<IssueCouponOption>();
    private List<IssueCouponTag> couponTagList = new ArrayList<IssueCouponTag>();

    private static final LinkedList<KeyValuePair> l = new LinkedList<KeyValuePair>();
    static {
        l.add(new KeyValuePair("title", 0x36e9));
        l.add(new KeyValuePair("promotionTagList", 0x8801));
        l.add(new KeyValuePair("couponOptionList", 0x4061));
        l.add(new KeyValuePair("couponTagList", 0x8275));
    }

    @Override
    public int getClassId() {
        return 0x9d2c;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return l;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getPromotionTagList() {
        return promotionTagList;
    }

    public void setPromotionTagList(List<String> promotionTagList) {
        this.promotionTagList = promotionTagList;
    }

    public List<IssueCouponOption> getCouponOptionList() {
        return couponOptionList;
    }

    public void setCouponOptionList(List<IssueCouponOption> couponOptionList) {
        this.couponOptionList = couponOptionList;
    }

    public List<IssueCouponTag> getCouponTagList() {
        return couponTagList;
    }

    public void setCouponTagList(List<IssueCouponTag> couponTagList) {
        this.couponTagList = couponTagList;
    }
}
