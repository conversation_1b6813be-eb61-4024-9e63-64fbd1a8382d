package com.dianping.pay.api.entity.promodesk;

import com.dianping.api.common.util.KeyValuePair;
import com.dianping.api.domain.ClientInfo;
import com.dianping.api.domain.Version;
import com.dianping.api.netio.DPEncoder;
import com.dianping.pay.api.entity.Discount;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

public class OptimalPromoTool extends DPEncoder implements Serializable {
    private boolean usePoint; // always false
    private boolean useHongBao;
    private PromoDeskCoupon coupon;
    private PromoDeskCoupon shopCoupon;
    private int discountId;
    private int quantity;
    private int productCode;

    private static final List<KeyValuePair> LIST = Arrays.asList(
            new KeyValuePair("usePoint", 0x924c),
            new KeyValuePair("useHongBao", 0xf0d6),
            new KeyValuePair("coupon", 0xbce1),
            new KeyValuePair("shopCoupon", 0xfd7c),
            new KeyValuePair("discountId", 0x988e),
            new KeyValuePair("quantity", 0x7fe9),
            new KeyValuePair("productCode", 0x8d25)
    );

    @Override
    public int getClassId() {
        return 0xa043;
    }

    @Override
    public List<KeyValuePair> getSerializeList(ClientInfo clientInfo, Version version) {
        return LIST;
    }

    public boolean isUsePoint() {
        return usePoint;
    }

    public void setUsePoint(boolean usePoint) {
        this.usePoint = usePoint;
    }

    public boolean isUseHongBao() {
        return useHongBao;
    }

    public void setUseHongBao(boolean useHongBao) {
        this.useHongBao = useHongBao;
    }

    public PromoDeskCoupon getCoupon() {
        return coupon;
    }

    public void setCoupon(PromoDeskCoupon coupon) {
        this.coupon = coupon;
    }

    public PromoDeskCoupon getShopCoupon() {
        return shopCoupon;
    }

    public void setShopCoupon(PromoDeskCoupon shopCoupon) {
        this.shopCoupon = shopCoupon;
    }

    public int getDiscountId() {
        return discountId;
    }

    public void setDiscountId(int discountId) {
        this.discountId = discountId;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public int getProductCode() {
        return productCode;
    }

    public void setProductCode(int productCode) {
        this.productCode = productCode;
    }
}
