package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.common.util.ContextUtils;
import com.meituan.mtrace.Endpoint;
import com.meituan.mtrace.Span;
import com.meituan.mtrace.Tracer;
import com.meituan.service.mobile.mtthrift.util.ClientInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * @Author: clark
 * Created At: 2019/2/1 上午11:36
 */
@Slf4j
public class AppkeyUtils {

    public static String getAppkey(String appkey){
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.pay.api.util.AppkeyUtils.getAppkey(java.lang.String)");
        return StringUtils.isBlank(appkey)?"UNKNOWN":appkey;
    }

    /**
     * 获取调用链最上游appkey
     * @return
     */
    public static String getSourceAppkey(){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.AppkeyUtils.getSourceAppkey()");
        String sourceAppkey = "unknown";
        try {
            sourceAppkey = (String) ContextUtils.getLocalContext("SOURCE_APP");

            if(StringUtils.isBlank(sourceAppkey)){
                sourceAppkey = Tracer.getContext("MTRootAppKey");
            }
        } catch (Exception e){
            log.warn("get source appkey exception!", e);
            Cat.logError("FAIL_GetSourceAppkey", e);
        }
        return sourceAppkey;
    }


    /**
     * 获取上游服务appkey
     * @return
     */
    public static String getClientAppkey(){
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.pay.api.util.AppkeyUtils.getClientAppkey()");
        String clientAppkey = "unknown";
        try {
            clientAppkey = (String) ContextUtils.getLocalContext("CLIENT_APP");

            if(StringUtils.isBlank(clientAppkey)){
                clientAppkey = ClientInfoUtil.getClientAppKey();
            }

            if(StringUtils.isBlank(clientAppkey)){
                Span span = Tracer.getServerSpan();
                if (span != null) {
                    Endpoint remote = span.getRemote();
                    if (remote != null) {
                        clientAppkey = remote.getAppkey();
                    }
                }
            }
        } catch (Exception e){
            log.warn("get client appkey exception!", e);
            Cat.logError("FAIL_GetClientAppkey", e);
        }
        return clientAppkey;
    }
}
