package com.dianping.pay.api.util;

import com.dianping.cat.Cat;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;

import java.util.Comparator;

public class CouponComparator implements Comparator<UnifiedCouponDTO> {

    @Override
    public int compare(UnifiedCouponDTO coupon1, UnifiedCouponDTO coupon2) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.pay.api.util.CouponComparator.compare(com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO,com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO)");
        int priceCompare = coupon2.getCouponGroupDTO().getDiscountAmount().compareTo(coupon1.getCouponGroupDTO().getDiscountAmount());
        if (priceCompare != 0) { // 价格降序
            return priceCompare;
        } else { // 过期时间升序
            return coupon1.getEndTime().compareTo(coupon2.getEndTime());
        }
    }
}
