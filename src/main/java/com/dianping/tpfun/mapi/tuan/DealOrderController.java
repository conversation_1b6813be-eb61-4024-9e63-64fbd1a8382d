package com.dianping.tpfun.mapi.tuan;

import com.dianping.tpfun.mapi.Controller;
import com.dianping.tpfun.mapi.tuan.dto.KTVMarketInfoDO;
import com.dianping.tpfun.mapi.tuan.widget.KTVMarketInfoWidget;
import org.springframework.beans.factory.annotation.Autowired;

public class DealOrderController extends Controller{
    private org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(this.getClass());

    @Autowired
    private KTVMarketInfoWidget marketInfoWidget;

    public Object ktvDealOrderMarketInfo() {
        int dealGroupId = getIntegerParameter("dealid", 0);
        int source = getIntegerParameter("source", 0);
        KTVMarketInfoDO marketInfoDO = new KTVMarketInfoDO();
        try {
            marketInfoDO = marketInfoWidget.getWidget(dealGroupId, source);
        } catch (Exception e) {
            LOGGER.error("[KTVDealOrderMarketInfo] exception , dealId:" + dealGroupId + ", source:" + source, e);
        }
        return marketInfoDO;
    }

}
