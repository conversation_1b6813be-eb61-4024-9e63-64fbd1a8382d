package com.dianping.tpfun.mapi.fuctor;


import com.dianping.tpfun.product.api.ktv.dto.KTVPromo;
import com.google.common.base.Function;

/**
 * shopdo object transfers.
 */
public class KTVBoookTransfers {

    public final static KTVPromo2Name KTVPROMO_2_NAME = new KTVPromo2Name();

    private static class KTVPromo2Name implements Function<KTVPromo, String> {

        @Override
        public String apply(KTVPromo input) {
            return input.getPromoName();
        }

    }
}
