package com.dianping.tpfun.mapi.shop.widget.shoplist;

import com.dianping.lion.client.Lion;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.tpfun.mapi.dto.shop.list.BaseSearchShopParam;
import com.dianping.tpfun.mapi.dto.shop.list.PageDealGroupDo;
import com.dianping.tpfun.mapi.dto.shop.list.PageShopDo;
import com.dianping.tpfun.mapi.shop.widget.shoplist.enums.Constants;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by z<PERSON><PERSON> on 15/8/28.
 */
@Component
public class OperationEventFacade {

    private static final org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(OperationEventFacade.class);

    @Autowired
    private BatchPromoService batchPromoService;

    public void setShopOperationEventInfo(BaseSearchShopParam param, List<PageShopDo> pageShopDoList) {
        if(pageShopDoList == null || pageShopDoList.size() == 0){
            return;
        }

        String value = "false";
        try {
            value = Lion.get("tpfun-mapi-web.OperationEventPromo-enabled", "false");
        }catch(Exception e){
            LOGGER.error("", e);
        }

        if(!Boolean.parseBoolean(value)){
            return;
        }

        List<Product> productList = new ArrayList<Product>();

        int maxDealCount = 3;
        for(PageShopDo pageShopDo : pageShopDoList){
            if(pageShopDo.getDeals() != null && pageShopDo.getDeals().getList() != null
                    && pageShopDo.getDeals().getList().size() > 0){
                int dealCount = 0;
                for(PageDealGroupDo pageDealGroupDo : pageShopDo.getDeals().getList()){
                    Product product = new Product();
                    product.setProductId(pageDealGroupDo.getId());
                    product.setPrice(BigDecimal.valueOf(pageDealGroupDo.getPrice()));
                    productList.add(product);

                    //最多取三个团单
                    dealCount++;
                    if(dealCount >= maxDealCount){
                        break;
                    }
                }
            }
        }

        if(productList.size() == 0){
            return;
        }

        Map<Integer, List<PromoDisplayDTO>> promoDispalyResult =
                batchPromoService.batchGetPromoInfo(productList,
                        Constants.PROMO_TEMPLATE_EVENT, Constants.PROMO_PRODUCT_TUAN, param);

        if(promoDispalyResult != null){
            for(PageShopDo pageShopDo : pageShopDoList){
                String promoInfo = batchPromoService.getShopDealPromoInfo(pageShopDo, Constants.DEAL_TYPE_ALL, promoDispalyResult,null);

                if(StringUtils.isNotBlank(promoInfo)){
                    pageShopDo.setAuthorityLabel(promoInfo);
                    pageShopDo.setAuthorityLabelType(Constants.BLUE);
                }
            }
        }
    }


}
