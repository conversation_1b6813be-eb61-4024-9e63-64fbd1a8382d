package com.dianping.tpfun.mapi.shop.widget.biz.impl;

import com.alibaba.fastjson.JSONArray;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.ktv.stock.api.dto.KtvStockDTO;
import com.dianping.lion.client.Lion;
import com.dianping.tpfun.mapi.dto.TextJsonModel;
import com.dianping.tpfun.mapi.dto.ktv.booktable.*;
import com.dianping.tpfun.mapi.shop.widget.biz.KTVBookTableSkuBiz;
import com.dianping.tpfun.mapi.shop.widget.biz.KTVProductQueryBiz;
import com.dianping.tpfun.mapi.shop.widget.biz.KTVTimeBiz;
import com.dianping.tpfun.mapi.utils.BookUrlUtils;
import com.dianping.tpfun.mapi.utils.PriceUtils;
import com.dianping.tpfun.mapi.utils.UrlUtil;
import com.dianping.tpfun.product.api.common.utils.DateDateUtils;
import com.dianping.tpfun.product.api.common.utils.StrStrUtils;
import com.dianping.tpfun.product.api.ktv.enums.KTVPeriodTypeEnum;
import com.dianping.tpfun.product.api.ktv.enums.KTVSaleTypeEnum;
import com.dianping.tpfun.product.api.ktv.utils.KTVAttributeNameSpace;
import com.dianping.tpfun.product.api.sku.common.enums.FunChannel;
import com.dianping.tpfun.product.api.sku.model.ItemAttribute;
import com.dianping.tpfun.product.api.sku.model.ProductItem;
import com.dianping.tpfun.product.api.sku.servicecharge.dto.ServiceFeeChargeRule;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;

import static com.dianping.tpfun.product.api.common.utils.StrStrUtils.money2strStripTrailingZeros;
import static com.dianping.tpfun.product.api.ktv.utils.KTVAttributeNameSpace.KTV.ktvStockKey;

/**
 * @Date 2019-07-19
 */
@Service("ktvBookTableSkuBizV2")
public class KTVBookTableSkuBizImplV2 implements KTVBookTableSkuBiz {

    private static Logger LOGGER = LoggerFactory.getLogger(KTVBookTableSkuBizImplV2.class);

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 用于计算，CPU消耗型，线程池数不能设置太大
     */
    private static final ExecutorService executorForCompute = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors(),
            5000, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<Runnable>(200),
            new ThreadFactoryBuilder().setNameFormat("sku-compute" + "-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    private static final long HOUR_MILLISECONDS = (long) 60 * 60 * 1000;

    @Autowired
    private KTVProductQueryBiz ktvProductQueryBiz;

    @Autowired
    private KTVTimeBiz ktvTimeBiz;

    private static final Comparator<KTVBookPackageDo> SKU_COMPARATOR = new Comparator<KTVBookPackageDo>() {
        @Override
        public int compare(KTVBookPackageDo package0, KTVBookPackageDo package1) {
            //可预订排前 价格从低到高
            //if (package0.getItemStatus() != package1.getItemStatus()) {
            //    return package0.getItemStatus() - package1.getItemStatus();
            //}
            //return package0.getPrice().compareTo(package1.getPrice());

            if ((package0 != null && package0.getItemStatus() == 1) && (package1 != null && package1.getItemStatus() != 1)) {
                return -1;
            } else if ((package0 != null && package0.getItemStatus() != 1) && (package1 != null && package1.getItemStatus() == 1)) {
                return 1;
            }
            return package0.getPrice().compareTo(package1.getPrice());
        }
    };


    private static final Comparator<KTVBookTableCombineDetailDo> ROOM_COMPARATOR = new Comparator<KTVBookTableCombineDetailDo>() {

        @Override
        public int compare(KTVBookTableCombineDetailDo package0, KTVBookTableCombineDetailDo package1) {
            //可预订排前面 后面按包房人数从小到大
            if (package0.getStatus() != package1.getStatus()) {
                return package0.getStatus() - package1.getStatus();
            }
            int[] capacity0 = package0.getRoom().getRoomCapacityInt();
            int[] capacity1 = package1.getRoom().getRoomCapacityInt();
            if (capacity0 == null || capacity1 == null) {
                return 0;
            }
            if (capacity0[0] != capacity1[0]) {
                return capacity0[0] - capacity1[0];
            } else {
                return capacity0[1] - capacity1[1];
            }
        }
    };


    @Override
    public void fillSkuInfo(KTVBookTableWeekPeriodDo weekPeriodDo, final Integer shopId,final String shopUuid, int channel, Integer mtShopId, Future<List<ProductItem>> productQueryFuture) {

        final List<KTVBookTableDateDo> ktvBookTableDates = weekPeriodDo.getKtvBookTableDates();
        if (CollectionUtils.isEmpty(ktvBookTableDates)) {
            return;
        }
        List<ProductItem> products = null;
        try {
            if (productQueryFuture != null) {
                products = productQueryFuture.get();
            } else {
                products = ktvProductQueryBiz.getOnlineProductItemList(shopId);
            }
        } catch (Exception e) {
            LOGGER.error("productQuery failed", e);
        }
        if (CollectionUtils.isEmpty(products)) {
            return;
        }
        //合并特殊节假日
        final List<ProductItem> productItems = products;
        Future<List<KTVBookTableCombineDetailDo>> initSkuFuture = executorForCompute.submit(new Callable<List<KTVBookTableCombineDetailDo>>() {
            @Override
            public List<KTVBookTableCombineDetailDo> call() throws Exception {
                Map<String, String> bookDateWeekMap = getAvailableBookDayWeeks(ktvBookTableDates);

                Map<String, List<ProductItem>> mergeWeekProductItemMap = processSpecialDays(productItems, bookDateWeekMap);
                return initBaseSkuInfoList(getProductItems(mergeWeekProductItemMap));
            }
        });

        //立减标签
        Map<Integer, String> promoTagMap = weekPeriodDo.getPromoTagMap();
        ServiceFeeChargeRule serviceFeeChargeRule = ktvProductQueryBiz.getServiceFeeChargeRule(productItems.get(0).getProductId(), shopId);
        Transaction transaction = Cat.newTransaction("Monitor", "Stock");
        Map<Long, Map<String, KtvStockDTO>> stockByDateMap = ktvProductQueryBiz.getProductStockMap(shopId, Util.getDateList(ktvBookTableDates), Util.getStockNoList(productItems));
        transaction.complete();

        final KTVSkuContext ktvSkuContext = KTVSkuContext.builder()
                .channel(channel)
                .shopId(shopId)
                .shopUuid(shopUuid)
                .promoTagMap(promoTagMap)
                .mtShopId(mtShopId)
                .serviceFeeChargeRule(serviceFeeChargeRule)
                .build();

        transaction = Cat.newTransaction("Monitor", "fill");
        try {
            //初始化SKU的基本信息
            List<KTVBookTableCombineDetailDo> roomSkuInfoList = initSkuFuture.get();
            if (CollectionUtils.isEmpty(roomSkuInfoList)) {
                return;
            }

            for (KTVBookTableDateDo ktvBookTableDateDo : ktvBookTableDates) {
                List<KTVBookTablePhasePeriodDo> phasePeriods = ktvBookTableDateDo.getPhasePeriods();
                if (CollectionUtils.isEmpty(phasePeriods)) {
                    continue;
                }
                BigDecimal dateDiscount = ktvBookTableDateDo.getDateDiscount();

                //库存
                //Map<String, KtvStockDTO> stockDTOMap = stockByDateMap.get(ktvBookTableDateDo.getBookDateTimestamp());
                //填充价格
                fillPriceAndStockInfo(roomSkuInfoList, dateDiscount, ktvSkuContext);
                //房型下，SKU整体有序
                //for (KTVBookTableCombineDetailDo ktvBookTableCombineDetailDo : roomSkuInfoList) {
                //    Collections.sort(ktvBookTableCombineDetailDo.getPackages(), SKU_COMPARATOR);
                //}

                List<Future> futureList = Lists.newArrayListWithCapacity(phasePeriods.size());

                final Map<Long, Map<String, KtvStockDTO>> stockByDate = stockByDateMap;

                for (KTVBookTablePhasePeriodDo ktvBookTablePhasePeriodDo : phasePeriods) {

                    final List<KTVBookTablePeriodDo> periods = ktvBookTablePhasePeriodDo.getPeriods();
                    final List<KTVBookTableCombineDetailDo> newRoomSkuInfoList = roomSkuInfoList;

                    Future future = executorForCompute.submit(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                for (KTVBookTablePeriodDo ktvBookTablePeriodDo : periods) {

                                    List<KTVBookTableCombineDetailDo> roomPackageCombines = createRealSkuInfoList(newRoomSkuInfoList, stockByDate, ktvBookTablePeriodDo, ktvSkuContext);
                                    ktvBookTablePeriodDo.setRoomPackageCombines(roomPackageCombines);
                                    if (CollectionUtils.isEmpty(roomPackageCombines)) {
                                        continue;
                                    }
                                    for (KTVBookTableCombineDetailDo ktvBookTableCombineDetailDo : roomPackageCombines) {
                                        Collections.sort(ktvBookTableCombineDetailDo.getPackages(), SKU_COMPARATOR);
                                    }
                                    ktvBookTablePeriodDo.setMinPrice(getRoomMinPrice(roomPackageCombines));
                                    Collections.sort(ktvBookTablePeriodDo.getRoomPackageCombines(), ROOM_COMPARATOR);

                                }
                            } catch (Exception e) {
                                LOGGER.error("[submit] error, shopId is {}", shopId, e);
                            }
                        }
                    });
                    futureList.add(future);
                }
                for (Future future : futureList) {
                    future.get();
                }
            }
        } catch (Exception e) {
            LOGGER.error("fillSkuInfo failed.", e);
            throw new RuntimeException(e);
        } finally {
            transaction.complete();
        }
    }

    private Map<String, String> getAvailableBookDayWeeks(List<KTVBookTableDateDo> ktvBookTableDates) {
        Map<String, String> bookDateWeekMap = Maps.newLinkedHashMapWithExpectedSize(ktvBookTableDates.size());
        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);
        for (KTVBookTableDateDo dataDo : ktvBookTableDates) {
            if (CollectionUtils.isEmpty(dataDo.getPhasePeriods())) {
                continue;
            }
            String bookDateStr = format.format(dataDo.getBookDateTimestamp());
            if (dataDo.getWeek().equals("今天")) {
                try {
                    String dayWeek = getDayWeek(format.parse(bookDateStr).getTime());
                    bookDateWeekMap.put(bookDateStr, dayWeek);
                } catch (Exception e) {
                }
            } else {
                bookDateWeekMap.put(bookDateStr, dataDo.getWeek());
            }
        }
        return bookDateWeekMap;
    }

    private String getDayWeek(long dayStr) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(dayStr));
        String cnDisplayWeek = DateDateUtils.getCnDisplayWeek(calendar);
        return cnDisplayWeek;
    }

    private Map<String, List<ProductItem>> processSpecialDays(List<ProductItem> productItems, Map<String, String> bookDateWeekMap) {
        if (MapUtils.isEmpty(bookDateWeekMap)) {
            return Collections.emptyMap();
        }
        //eg 2019-07-12,周五 bookDateWeekMap
        //遍历sku 判断是否是特殊日期的sku
        //正常 周几
        Map<String, List<ProductItem>> normalWeekProductItemMap = Maps.newLinkedHashMapWithExpectedSize(bookDateWeekMap.size());
        Map<String, List<ProductItem>> specialWeekProductItemMap = Maps.newLinkedHashMapWithExpectedSize(bookDateWeekMap.size());
        for (ProductItem productItem : productItems) {
            String holidayStr = productItem.getAttrFirstValue(KTVAttributeNameSpace.KTV.ktvHoliday);
            if (StringUtils.isNotBlank(holidayStr)) {
                if (bookDateWeekMap.get(holidayStr) == null) {
                    continue;
                }
                //eg 2019-07-12,周五
                SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);
                try {
                    Date holidayDate = format.parse(holidayStr);
                    String specialWeek = getDayWeek(holidayDate.getTime());
                    if (specialWeekProductItemMap.get(specialWeek) == null) {
                        specialWeekProductItemMap.put(specialWeek, Lists.newArrayList(productItem));
                    } else {
                        specialWeekProductItemMap.get(specialWeek).add(productItem);
                    }
                } catch (Exception e) {

                }
            } else {
                String normalWeek = productItem.getAttrFirstValue(KTVAttributeNameSpace.KTV.ktvWeek);
                if (normalWeekProductItemMap.get(normalWeek) == null) {
                    normalWeekProductItemMap.put(normalWeek, Lists.newArrayList(productItem));
                } else {
                    normalWeekProductItemMap.get(normalWeek).add(productItem);
                }
            }
        }
        if (MapUtils.isNotEmpty(specialWeekProductItemMap)) {
            //合并
            for (Map.Entry<String, List<ProductItem>> entry : specialWeekProductItemMap.entrySet()) {
                if (normalWeekProductItemMap.get(entry.getKey()) != null) {
                    normalWeekProductItemMap.put(entry.getKey(), entry.getValue());
                } else {
                    normalWeekProductItemMap.put(entry.getKey(), Lists.newArrayList(entry.getValue()));
                }
            }
        }
        return normalWeekProductItemMap;
    }

    private List<ProductItem> getProductItems(Map<String, List<ProductItem>> weekProductItemMap) {
        List<ProductItem> totalProductItems = Lists.newArrayList();
        for (Map.Entry<String, List<ProductItem>> entry : weekProductItemMap.entrySet()) {
            if (entry.getValue() != null) {
                totalProductItems.addAll(entry.getValue());
            }
        }
        return totalProductItems;
    }

    private String getRoomMinPrice(List<KTVBookTableCombineDetailDo> roomPackageCombines) {
        KTVBookTableCombineDetailDo minPriceRoom = Collections.min(roomPackageCombines, new Comparator<KTVBookTableCombineDetailDo>() {
            @Override
            public int compare(KTVBookTableCombineDetailDo package0, KTVBookTableCombineDetailDo package1) {
                return package0.getRoom().getMinPriceNum().compareTo(package1.getRoom().getMinPriceNum());
            }
        });
        return minPriceRoom.getRoom().getMinPrice();
    }

    @Getter
    @Builder
    private static class KTVSkuContext {
        private int shopId;
        private String shopUuid;
        private int channel;
        private Integer mtShopId;
        private Map<Integer, String> promoTagMap;
        private ServiceFeeChargeRule serviceFeeChargeRule;
    }


    private void fillPriceAndStockInfo(List<KTVBookTableCombineDetailDo> roomSkuInfoList, BigDecimal dateDiscount, KTVSkuContext ktvSkuContext) {
        for (KTVBookTableCombineDetailDo ktvBookTableCombineDetailDo : roomSkuInfoList) {
            for (KTVBookPackageDo skuInfo : ktvBookTableCombineDetailDo.getPackages()) {
                //fillStockInfo(skuInfo, stockDTOMap.get(skuInfo.getKtvAttribute().getStockNo()), ktvSkuContext);
                fillPriceInfo(skuInfo, dateDiscount, ktvSkuContext.getPromoTagMap().get(skuInfo.getItemId()), ktvSkuContext);
            }
        }
    }

    static class Util {
        static List<String> getStockNoList(List<ProductItem> productItems) {
            Set<String> stockNOSet = new HashSet<String>();
            for (ProductItem productItem : productItems) {
                try {
                    String stockNO = productItem.getAttrFirstValue(ktvStockKey);
                    if (StringUtils.isNotBlank(stockNO)) {
                        stockNOSet.add(StringUtils.trim(stockNO));
                    }
                } catch (Exception e) {
                    LOGGER.error("[filterStockNOs] error, productItemId is {}", productItem.getId(), e);
                }
            }
            if (CollectionUtils.isEmpty(stockNOSet)) {
                return Collections.emptyList();
            }
            return Lists.newArrayList(stockNOSet);
        }

        static List<Date> getDateList(List<KTVBookTableDateDo> ktvBookTableDates) {
            List<Date> dateList = Lists.newArrayListWithCapacity(ktvBookTableDates.size());
            for (KTVBookTableDateDo ktvBookTableDateDo : ktvBookTableDates) {
                dateList.add(new Date(ktvBookTableDateDo.getBookDateTimestamp()));
            }
            return dateList;
        }
    }


    private List<KTVBookTableCombineDetailDo> createRealSkuInfoList(final List<KTVBookTableCombineDetailDo> skuInfoList, Map<Long, Map<String, KtvStockDTO>> stockByDate, KTVBookTablePeriodDo ktvBookTablePeriodDo, KTVSkuContext ktvSkuContext) {
        List<KTVBookTableCombineDetailDo> result = Lists.newArrayListWithCapacity(skuInfoList.size());
        for (KTVBookTableCombineDetailDo ktvBookTableCombineDetailDo : skuInfoList) {
            KTVBookTableCombineDetailDo ktvBookTableCombineDetailDo1 = new KTVBookTableCombineDetailDo();
            List<KTVBookPackageDo> packageDoList = Lists.newArrayListWithCapacity(ktvBookTableCombineDetailDo.getPackages().size());
            for (KTVBookPackageDo ktvBookPackageDo : ktvBookTableCombineDetailDo.getPackages()) {
                if (!belongToThisPeriodNew(ktvBookTablePeriodDo, ktvBookPackageDo.getKtvAttribute())) {
                    continue;
                }
                packageDoList.add(createRealSkuInfo(ktvBookPackageDo, stockByDate, ktvBookTablePeriodDo.getStartTime(), ktvSkuContext));
            }
            if (CollectionUtils.isEmpty(packageDoList)) {
                continue;
            }
            ktvBookTableCombineDetailDo1.setPackages(packageDoList);
            ktvBookTableCombineDetailDo1.setRoom(createRealRoom(ktvBookTableCombineDetailDo.getRoom(), packageDoList));
            ktvBookTableCombineDetailDo1.setStatus(getRoomStatus(packageDoList));
            result.add(ktvBookTableCombineDetailDo1);
        }
        return result;
    }

    private KTVBookTableRoomDo createRealRoom(KTVBookTableRoomDo ktvBookTableRoomDo, List<KTVBookPackageDo> skuInfoList) {
        KTVBookTableRoomDo result = new KTVBookTableRoomDo();
        KTVBookPackageDo minPriceSku = Collections.min(skuInfoList, new Comparator<KTVBookPackageDo>() {
            @Override
            public int compare(KTVBookPackageDo package0, KTVBookPackageDo package1) {
                return package0.getPrice().compareTo(package1.getPrice());
            }
        });
        result.setMinPrice(minPriceSku.getItemPrice());
        result.setMinPriceNum(minPriceSku.getPrice());
        result.setRoomCapacity(ktvBookTableRoomDo.getRoomCapacity());
        result.setRoomDisplayName(ktvBookTableRoomDo.getRoomDisplayName());
        result.setRoomName(ktvBookTableRoomDo.getRoomName());
        result.setRoomCapacityInt(ktvBookTableRoomDo.getRoomCapacityInt());
        return result;
    }

    private void fillPriceInfo(KTVBookPackageDo skuInfo, BigDecimal dateDiscount, String promoTag, KTVSkuContext ktvSkuContext) {
        //初始化
        skuInfo.setItemPromoTag(null);
        skuInfo.setPriceUnit(null);
        skuInfo.setItemMarketPrice(null);
        skuInfo.setPrice(null);
        skuInfo.setItemPrice(StrStrUtils.money2strStripTrailingZeros(skuInfo.getProductItem().getPrice()));


        if (dateDiscount != null && dateDiscount.compareTo(BigDecimal.ZERO) > 0) {
            skuInfo.setItemPromoTag(String.format("已享%s折", dateDiscount.stripTrailingZeros().toPlainString()));
            BigDecimal discountedPrice = new BigDecimal(skuInfo.getItemPrice()).multiply(dateDiscount.divide(BigDecimal.valueOf(10)));
            BigDecimal payPrice = discountedPrice.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
            skuInfo.setItemPrice(money2strStripTrailingZeros(payPrice));
        } else if (StringUtils.isNotBlank(promoTag)) {
            skuInfo.setItemPromoTag(promoTag);
        }

        if (skuInfo.getKtvAttribute().getDrinkPrice() != null) {
            skuInfo.setItemMarketPrice(StrStrUtils.money2strStripTrailingZeros(skuInfo.getKtvAttribute().getDrinkPrice().add(skuInfo.getProductItem().getPrice())));
        }

        if (ktvSkuContext.getServiceFeeChargeRule() != null) {
            if (skuInfo.getKtvAttribute().getKtvSaleTypeEnum() == KTVSaleTypeEnum.SoldByRoom) {
                BigDecimal serviceFee = PriceUtils.calculateServiceFeeByRule(ktvSkuContext.getServiceFeeChargeRule(), skuInfo.getProductItem().getPrice().setScale(2));
                //按包房
                skuInfo.setItemPrice(StrStrUtils.money2strStripTrailingZeros(new BigDecimal(skuInfo.getItemPrice()).add(serviceFee)));
                if (skuInfo.getKtvAttribute().getDrinkPrice() != null) {
                    skuInfo.setItemMarketPrice(StrStrUtils.money2strStripTrailingZeros(new BigDecimal(skuInfo.getItemMarketPrice()).add(serviceFee)));
                }
            } else {
                skuInfo.setPriceUnit("/人");
            }
        }
        skuInfo.setPrice(new BigDecimal(skuInfo.getItemPrice()));
    }

    private void fillStockInfo(KTVBookPackageDo skuInfo, KtvStockDTO ktvStockDTO, KTVSkuContext ktvSkuContext) {
        //初始化
        skuInfo.setItemStatus(0);
        skuInfo.setRoomStockTag(null);
        if (ktvStockDTO == null) {
            return;
        }
        if (ktvStockDTO.isInSale()) {
            skuInfo.setItemStatus(1);
        }
        //roomStockTag
        int remainCount = 0;
        if (ktvStockDTO.getDayStock() > ktvStockDTO.getSaleCount()) {
            remainCount = ktvStockDTO.getDayStock() - ktvStockDTO.getSaleCount();
        }
        List<TextJsonModel> stockTagList = Lists.newArrayListWithExpectedSize(2);
        if (remainCount > 0) {
            //库存模式且有库存 立即确认
            String verifyStyle = (ktvSkuContext.getChannel() == FunChannel.DP.getCode()) ? "#FF6633" : "#FF6200";
            TextJsonModel verifyModel = new TextJsonModel("立即确认", 12, verifyStyle);
            stockTagList.add(verifyModel);
        }
        if (remainCount > 0 && remainCount <= 5) {
            //仅剩X间
            String remainStyle = (ktvSkuContext.getChannel() == FunChannel.DP.getCode()) ? "#FF6633" : "#FF4A4A";
            TextJsonModel remainModel = new TextJsonModel("仅剩" + remainCount + "间", 12, remainStyle);
            stockTagList.add(remainModel);
        }
        if (CollectionUtils.isNotEmpty(stockTagList)) {
            skuInfo.setRoomStockTag(JSONArray.toJSONString(stockTagList));
        }
    }


    private KTVBookPackageDo createRealSkuInfo(KTVBookPackageDo skuInfo, Map<Long, Map<String, KtvStockDTO>> stockByDate, long bookStartTime, KTVSkuContext ktvSkuContext) {
        KTVBookPackageDo result = copySkuInfo(skuInfo);
        SimpleDateFormat format = new SimpleDateFormat("HH:mm");

        //跨天时段 库存是前一天; 零点开始的 00:00:00,06:00:00 提交订单会校验bookDateTimeStamp
        long bookDateTimeStamp = ktvTimeBiz.calBookDateTS(bookStartTime, skuInfo.getKtvAttribute().getKtvPeriods());

        //库存
        Map<String, KtvStockDTO> stockDTO = stockByDate.get(bookDateTimeStamp);
        if (MapUtils.isEmpty(stockDTO)) {
            LOGGER.error("[createRealSkuInfo] get stockByDate nul, bookDateTimeStamp is {}, bookStartTime is {}, skuId is {}", bookDateTimeStamp, bookStartTime, skuInfo.getItemId());
        } else {
            fillStockInfo(result, stockDTO.get(skuInfo.getKtvAttribute().getStockNo()), ktvSkuContext);
        }

        //实际欢唱小时
        long actualSingMilliseconds = ktvTimeBiz.getActualSingMilliseconds(bookDateTimeStamp, bookStartTime, skuInfo.getKtvAttribute());
        long endTime = bookStartTime + actualSingMilliseconds;
        String endTimeStr = format.format(endTime);
        result.setBookingUrl(buildBookUrl(bookStartTime, skuInfo, ktvSkuContext, bookDateTimeStamp, (double) actualSingMilliseconds / HOUR_MILLISECONDS,ktvSkuContext.getShopUuid()));
        result.setItemName(buildPackageName(skuInfo.getKtvAttribute(), (double) actualSingMilliseconds / HOUR_MILLISECONDS));
        result.getPackageDetail().setSingHours(format.format(bookStartTime) + "-" + endTimeStr);
        result.getPackageDetail().setItemName(result.getItemName());
        List<String> itemDescTags = Lists.newArrayList("唱至" + endTimeStr);
        itemDescTags.addAll(skuInfo.getItemDescTags());
        result.setItemDescTags(itemDescTags);
        return result;
    }

    private String buildPackageName(KTVBookPackageDo.KTVAttribute ktvAttributeValue, double actualSingHour) {
        if (StringUtils.isNotBlank(ktvAttributeValue.getKtvDrinkName())) {
            return String.format("%s小时欢唱+%s", BigDecimal.valueOf(actualSingHour).stripTrailingZeros().toPlainString(), ktvAttributeValue.getKtvDrinkName());
        } else {
            return String.format("%s小时欢唱", BigDecimal.valueOf(actualSingHour).stripTrailingZeros().toPlainString());
        }
    }

    private boolean belongToThisPeriodNew(KTVBookTablePeriodDo ktvBookTablePeriodDo, KTVBookPackageDo.KTVAttribute ktvAttributeValue) {

        long startTime = ktvBookTablePeriodDo.getStartTime();
        long endTime = ktvBookTablePeriodDo.getEndTime();

        long minSingTimes = 0l;
        if (ktvAttributeValue.getMinSingMilliseconds() != null) {
            minSingTimes = ktvAttributeValue.getMinSingMilliseconds();
        } else {
            minSingTimes = ktvAttributeValue.getKtvMilliseconds();
        }

        int endSize = ktvAttributeValue.getSkuEndTimeList().size();

        Long skuStart = ktvAttributeValue.getSkuStartTimeList().get(0);
        Long skuEnd = ktvAttributeValue.getSkuEndTimeList().get(endSize - 1);

        //进场
        if (KTVPeriodTypeEnum.Arrive == ktvAttributeValue.getKtvPeriodType()) {
            if (skuStart <= startTime && (endTime <= skuEnd || endTime - HOUR_MILLISECONDS / 2 == skuEnd)) {
                return true;
            } //提前可订+ 进场
        } else {
            if (skuStart <= startTime && endTime <= skuEnd && startTime <= skuEnd - minSingTimes) {
                return true;
            } //提前可订+ 包段
        }

        //for (int i = 0; i < ktvAttributeValue.getSkuStartTimeList().size(); i++) {
        //    long skuStartTime = ktvAttributeValue.getSkuStartTimeList().get(i);
        //    long skuEndTime = ktvAttributeValue.getSkuEndTimeList().get(i);
        //    if (KTVPeriodTypeEnum.Arrive == ktvAttributeValue.getKtvPeriodType()) { //进场
        //        if (skuStartTime <= startTime && (endTime <= skuEndTime || endTime - HOUR_MILLISECONDS / 2 == skuEndTime)) {
        //            return true;
        //        } //提前可订+ 进场
        //    } else {
        //        if (skuStartTime <= startTime && endTime <= skuEndTime && startTime <= skuEndTime - minSingTimes) {
        //            return true;
        //        } //提前可订+ 包段
        //    }
        //}

        return false;
    }

    private KTVBookPackageDo copySkuInfo(KTVBookPackageDo skuInfo) {
        KTVBookPackageDo result = new KTVBookPackageDo();
        result.setBookingUrl(skuInfo.getBookingUrl());
        result.setItemId(skuInfo.getItemId());
        result.setItemMarketPrice(skuInfo.getItemMarketPrice());
        result.setItemName(skuInfo.getItemName());
        result.setItemPrice(skuInfo.getItemPrice());
        result.setItemMarketPrice(skuInfo.getItemMarketPrice());
        result.setPriceUnit(skuInfo.getPriceUnit());
        result.setItemPromoTag(skuInfo.getItemPromoTag());
        result.setRoomStockTag(skuInfo.getRoomStockTag());
        result.setItemStatus(skuInfo.getItemStatus());
        result.setPrice(skuInfo.getPrice());
        result.setPackageDetail(copyKTVBookTablePackageDetailDo(skuInfo.getPackageDetail()));
        return result;
    }

    private KTVBookTablePackageDetailDo copyKTVBookTablePackageDetailDo(KTVBookTablePackageDetailDo ktvBookTablePackageDetailDo) {
        KTVBookTablePackageDetailDo packageDetail = new KTVBookTablePackageDetailDo();
        packageDetail.setItemName(ktvBookTablePackageDetailDo.getItemName());
        packageDetail.setPackageDesc(ktvBookTablePackageDetailDo.getPackageDesc());
        packageDetail.setRoomDisplayName(ktvBookTablePackageDetailDo.getRoomDisplayName());
        return packageDetail;
    }


    private List<KTVBookTableCombineDetailDo> initBaseSkuInfoList(List<ProductItem> productItems) {
        Map<String, KTVBookTableCombineDetailDo> roomMap = Maps.newHashMap();
        for (ProductItem productItem : productItems) {
            KTVBookPackageDo ktvBookPackageDo = initBaseSkuInfo(productItem);
            String roomDisplayName = ktvBookPackageDo.getKtvAttribute().getRoomDisplayName();
            if (!roomMap.containsKey(roomDisplayName)) {
                KTVBookTableRoomDo bookTableRoomDo = initBookTableRoom(ktvBookPackageDo.getKtvAttribute());
                roomMap.put(ktvBookPackageDo.getKtvAttribute().getRoomDisplayName(), buildNullBookTableCombineDetail(bookTableRoomDo));
            }
            roomMap.get(roomDisplayName).getPackages().add(ktvBookPackageDo);
        }
        return Lists.newArrayList(roomMap.values());
    }

    private int getRoomStatus(List<KTVBookPackageDo> packages) {
        for (KTVBookPackageDo ktvBookPackageDo : packages) {
            if (ktvBookPackageDo.getItemStatus() == 1) {
                return 1;
            }
        }
        return 0;
    }

    private KTVBookPackageDo initBaseSkuInfo(ProductItem productItem) {

        List<ItemAttribute> itemAttributeList = productItem.getAttributes();
        Map<String, String> attrMap = Maps.newHashMap();
        for (int i = itemAttributeList.size() - 1; i >= 0; i--) {
            ItemAttribute itemAttribute = itemAttributeList.get(i);
            if (StringUtils.isNotEmpty(itemAttribute.getAttrValue())) {
                attrMap.put(itemAttribute.getName(), itemAttribute.getAttrValue());
            }
        }


        KTVBookPackageDo skuInfo = new KTVBookPackageDo();
        skuInfo.setItemId(productItem.getId());

        //套餐原价
        String drinkPriceStr = attrMap.get(KTVAttributeNameSpace.KTV.ktvDrinkPrice);
        BigDecimal drinkPrice = null;
        if (StringUtils.isNotBlank(drinkPriceStr)) {
            drinkPrice = new BigDecimal(drinkPriceStr);
        }

        List<String> descTags = Lists.newArrayList();
        String ktvDrinkContent = attrMap.get(KTVAttributeNameSpace.KTV.ktvDrinkContent);

        if (StringUtils.isNotBlank(ktvDrinkContent)) {
            descTags.add(ktvDrinkContent);
        }
        skuInfo.setItemDescTags(descTags);

        String roomType = attrMap.get(KTVAttributeNameSpace.KTV.ktvRoomType);
        String roomCapacity = attrMap.get(KTVAttributeNameSpace.KTV.ktvRoomCapacity);

        String roomDisplayName = getRoomDisplayName(roomType, roomCapacity);

        //package详情
        KTVBookTablePackageDetailDo detailDo = new KTVBookTablePackageDetailDo();
        detailDo.setItemName(skuInfo.getItemName());
        detailDo.setPackageDesc(ktvDrinkContent);
        detailDo.setRoomDisplayName(roomDisplayName);
        skuInfo.setPackageDetail(detailDo);

        KTVSaleTypeEnum ktvSaleTypeEnum = KTVSaleTypeEnum.SoldByPeopleNum;
        String saleTypeStr = attrMap.get(KTVAttributeNameSpace.KTV.saleType);//售卖方式 1人头 0包房
        if (StringUtils.isNotBlank(saleTypeStr) && Integer.parseInt(saleTypeStr) == KTVSaleTypeEnum.SoldByRoom.getValue()) {
            ktvSaleTypeEnum = KTVSaleTypeEnum.SoldByRoom;
        }

        String minSingHourStr = attrMap.get(KTVAttributeNameSpace.KTV.ktvMinSingHours);
        Long minSingMilliseconds = null;
        if (StringUtils.isNotEmpty(minSingHourStr)) {
            minSingMilliseconds = ((Double) Double.parseDouble(minSingHourStr)).longValue() * HOUR_MILLISECONDS;
        }
        String ktvHours = attrMap.get(KTVAttributeNameSpace.KTV.ktvHours);
        Integer ktvHour = Integer.parseInt(ktvHours);
        String periodTypeStr = attrMap.get(KTVAttributeNameSpace.KTV.ktvPeriodType);

        String[] ktvPeriods = attrMap.get(KTVAttributeNameSpace.KTV.ktvPeriod).split(",");
        KTVBookPackageDo.KTVAttribute ktvAttributeValue = KTVBookPackageDo.KTVAttribute.builder()
                .skuStartTimeList(ktvTimeBiz.getSkuStartTime(attrMap))
                .skuEndTimeList(ktvTimeBiz.getSkuEndTime(attrMap))
                .minSingMilliseconds(minSingMilliseconds)
                .ktvPeriodType("1".equals(periodTypeStr) ? KTVPeriodTypeEnum.Arrive : KTVPeriodTypeEnum.Period)
                .ktvMilliseconds(ktvHour * HOUR_MILLISECONDS)
                .ktvHour(ktvHour)
                .stockNo(attrMap.get(ktvStockKey))
                .roomDisplayName(roomDisplayName)
                .roomType(roomType)
                .roomCapacity(roomCapacity)
                .ktvSaleTypeEnum(ktvSaleTypeEnum)
                .drinkPrice(drinkPrice)
                .ktvPeriods(ktvPeriods)
                .ktvPeriodsLong(new long[]{StrStrUtils.parseTime(ktvPeriods[0]), StrStrUtils.parseTime(ktvPeriods[1])})
                .build();
        skuInfo.setKtvAttribute(ktvAttributeValue);

        skuInfo.setProductItem(productItem);
        return skuInfo;

    }

    private KTVBookTableCombineDetailDo buildNullBookTableCombineDetail(KTVBookTableRoomDo ktvBookTableRoomDo) {
        KTVBookTableCombineDetailDo ktvBookTableCombineDetailDo = new KTVBookTableCombineDetailDo();
        ktvBookTableCombineDetailDo.setRoom(ktvBookTableRoomDo);
        ktvBookTableCombineDetailDo.setPackages(Lists.<KTVBookPackageDo>newArrayList());
        return ktvBookTableCombineDetailDo;
    }

    // 构建新包房
    private KTVBookTableRoomDo initBookTableRoom(KTVBookPackageDo.KTVAttribute ktvAttribute) {
        KTVBookTableRoomDo roomDo = new KTVBookTableRoomDo();
        roomDo.setRoomName(ktvAttribute.getRoomType());
        roomDo.setRoomCapacity(formatCapacity(ktvAttribute.getRoomCapacity(), '-'));
        roomDo.setRoomDisplayName(ktvAttribute.getRoomDisplayName());
        roomDo.setRoomCapacityInt(getRoomCapacity(roomDo.getRoomCapacity()));
        return roomDo;
    }

    private int[] getRoomCapacity(String roomCapacity) {
        if (StringUtils.isBlank(roomCapacity)) {
            return null;
        }
        try {
            String[] persons = StringUtils.split(roomCapacity, "-");
            if (persons == null || persons.length < 2) {
                return null;
            }
            int min = Integer.parseInt(persons[0]);
            int max = Integer.parseInt(persons[1]);
            int result[] = {min, max};
            return result;
        } catch (Exception e) {
            LOGGER.error("[KTVBookTableSkuBizImpl] getRoomCapacity error, roomCapacity is {}", roomCapacity, e);
            return null;
        }
    }


    private String getRoomDisplayName(String roomType, String roomCapacity) {
        if (StringUtils.isNotBlank(roomType) && StringUtils.isNotBlank(roomCapacity)) {
            return String.format("%s(%s人)", roomType, formatCapacity(roomCapacity, '-'));
        }
        return roomType;
    }

    private String formatCapacity(String capacity, char delimiter) {
        String capacity2 = StrStrUtils.delimit2(capacity, ',', delimiter);
        if (StringUtils.isEmpty(capacity2)) {
            return capacity;
        }
        return capacity2;
    }


    private String buildBookUrl(long startTime, KTVBookPackageDo skuInfo, KTVSkuContext ktvSkuContext, long bookDateTimeStamp, double actualSingHours,String shopUuid) {


        //跨天时段 库存是前一天; 零点开始的 00:00:00,06:00:00 提交订单会校验bookDateTimeStamp
        //long bookDateTimeStamp = ktvTimeBiz.calBookDateTS(startTime, skuInfo.getKtvAttribute().getKtvPeriodsLong());

        //String actualSingHour = BigDecimal.valueOf((double) ktvTimeBiz.getActualSingMilliseconds(bookDateTimeStamp, startTime, skuInfo.getKtvAttribute()) / HOUR_MILLISECONDS).stripTrailingZeros().toPlainString();
        String actualSingHour = BigDecimal.valueOf(actualSingHours).stripTrailingZeros().toPlainString();

        try {
            //时段和包房会有中文，需要encode
            if (ktvSkuContext.getChannel() == FunChannel.DP.getCode()) {
                String ktvBookDomain = Lion.get("tpfun-mapi-web.preorder.book-url", "https://h5.dianping.com/tuan/fun/ktvbook/index-no-time.html?token=!&");
                String ktvBookUrl = ktvBookDomain + "shopid=" + ktvSkuContext.getShopId()
                        + "&roomtype=" + URLEncoder.encode(skuInfo.getKtvAttribute().getRoomType())
                        + "&itemid=" + skuInfo.getItemId()
                        + "&bookdatetimestamp=" + bookDateTimeStamp
                        + "&arrivetime=" + startTime
                        + "&actualhour=" + actualSingHour
                        + "&versionflag=1";
                ktvBookUrl = BookUrlUtils.addShopUUid(ktvBookUrl,shopUuid);
                return UrlUtil.getDpAppH5Url(ktvBookUrl);
            } else {
                String mtBookDomain = Lion.get("tpfun-mapi-web.preorder.mt-book-url", "https://h5.dianping.com/tuan/fun/mt-ktvbook/index-no-time.html");
                String mtBookUrl = mtBookDomain + "?shopid=" + ktvSkuContext.getShopId() + "&mtshopid=" + ktvSkuContext.getMtShopId()
                        + "&roomtype=" + URLEncoder.encode(skuInfo.getKtvAttribute().getRoomType())
                        + "&productid=" + skuInfo.getProductItem().getProductId()
                        + "&itemid=" + skuInfo.getItemId()
                        + "&bookdatetimestamp=" + bookDateTimeStamp
                        + "&arrivetime=" + startTime
                        + "&actualhour=" + actualSingHour
                        + "&versionflag=1";
                //mtBookUrl = URLEncoder.encode(mtBookUrl, "utf8");
                mtBookUrl = UrlUtil.getMtAppH5Url(mtBookUrl);
                return mtBookUrl;
            }

        } catch (Exception e) {
            LOGGER.error("[buildBookUrl] error, itemId is {}, channel is {}, dpShopId is {}", skuInfo.getItemId(), ktvSkuContext.getChannel(), ktvSkuContext.getShopId());
        }
        return StringUtils.EMPTY;
    }


}
