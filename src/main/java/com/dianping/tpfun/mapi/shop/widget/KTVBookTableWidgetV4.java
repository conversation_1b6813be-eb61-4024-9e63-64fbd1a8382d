package com.dianping.tpfun.mapi.shop.widget;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.tpfun.mapi.GodBiz;
import com.dianping.tpfun.mapi.dto.ktv.KTVBookTable;
import com.dianping.tpfun.mapi.dto.ktv.booktable.*;
import com.dianping.tpfun.mapi.shop.widget.biz.KTVBookTableBiz;
import com.dianping.tpfun.mapi.shop.widget.biz.KTVProductQueryBiz;
import com.dianping.tpfun.mapi.shop.widget.biz.KtvWidgetContext;
import com.dianping.tpfun.product.api.common.KTVChannel;
import com.dianping.tpfun.product.api.common.KTVClientContext;
import com.dianping.tpfun.product.api.sku.model.ProductItem;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 已废弃，没有流量了，不再进行poi升级改造！
 * 19版ktv预订价目表
 */
@Deprecated
@Component("ktvBookTableWidgetV4")
public class KTVBookTableWidgetV4 {

    private static org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(KTVBookTableWidgetV4.class);

    @Autowired
    private GodBiz godBiz;
    @Autowired
    private KTVBookTableWidgetV2 ktvBookTableWidgetV2;

    @Resource
    private KTVBookTableBiz ktvBookTableBiz;

    @Autowired
    private KTVProductQueryBiz ktvProductQueryBiz;

    private static final ExecutorService executorForQueryProduct = new ThreadPoolExecutor(
            30,
            30,
            5000, TimeUnit.MILLISECONDS,
            new SynchronousQueue<Runnable>(),
            new ThreadFactoryBuilder().setNameFormat("product-query" + "-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());


    public KTVBookTableWeekPeriodDo getNavKTVBookTableWeekPeriodDo(final KtvWidgetContext widgetContext) {
        fillKTVBookTable(widgetContext);
        KTVBookTableWeekPeriodDo weekPeriod = ktvBookTableBiz.getWeekPeriod(widgetContext);
        removeRoomTypeInfo(weekPeriod);
        return weekPeriod;
    }

    public KTVBookTableRoomPackageDo getRoomPackageDo(KtvWidgetContext widgetContext) {

        KTVBookTableRoomPackageDo roomPackageDo = new KTVBookTableRoomPackageDo();
        List<KTVBookTableCombineDo> combineList = Lists.newArrayList();
        roomPackageDo.setCombineList(combineList);

        long beginTime = System.currentTimeMillis();

        List<KTVBookTablePhasePeriodDo> ktvBookTablePhasePeriodDo = getKTVBookTablePhasePeriodDo(widgetContext); /////todo


        if (CollectionUtils.isNotEmpty(ktvBookTablePhasePeriodDo)) {
            //build 分组
            for (KTVBookTablePhasePeriodDo phasePeriodDo : ktvBookTablePhasePeriodDo) {

                List<KTVBookTablePeriodDo> periods = phasePeriodDo.getPeriods();
                for (KTVBookTablePeriodDo periodDo : periods) {

                    KTVBookTableCombineDo ktvBookTableCombineDo = new KTVBookTableCombineDo();
                    combineList.add(ktvBookTableCombineDo);

                    long startTime = periodDo.getStartTime();
                    ktvBookTableCombineDo.setStartTime(startTime);
                    if (CollectionUtils.isNotEmpty(periodDo.getRoomPackageCombines())) {
                        ktvBookTableCombineDo.setRoomPackageCombines(periodDo.getRoomPackageCombines());
                    }
                }
            }
        }
        //long endTime = System.currentTimeMillis();
        //long cost = endTime - beginTime;
        //System.out.println("[getRoomPackageDo] cost: " + cost);

        return roomPackageDo;
    }

    public List<KTVBookTablePhasePeriodDo> getKTVBookTablePhasePeriodDo(final KtvWidgetContext widgetContext) {
        fillKTVBookTable(widgetContext);
        KTVBookTableWeekPeriodDo weekPeriod = ktvBookTableBiz.getWeekPeriod(widgetContext);
        return findSpecifiedKTVBookTablePhasePeriodDo(weekPeriod, widgetContext);
    }

    private void removeRoomTypeInfo(KTVBookTableWeekPeriodDo weekPeriod) {

        if (weekPeriod == null) {
            return;
        }
        List<KTVBookTableDateDo> ktvBookTableDates = weekPeriod.getKtvBookTableDates();
        if (CollectionUtils.isEmpty(ktvBookTableDates)) {
            return;
        }
        for (KTVBookTableDateDo dateDo : ktvBookTableDates) {
            List<KTVBookTablePhasePeriodDo> phasePeriods = dateDo.getPhasePeriods();
            for (KTVBookTablePhasePeriodDo phasePeriodDo : phasePeriods) {
                List<KTVBookTablePeriodDo> periods = phasePeriodDo.getPeriods();
                for (KTVBookTablePeriodDo periodDo : periods) {
                    periodDo.setRoomPackageCombines(Collections.<KTVBookTableCombineDetailDo>emptyList());
                }
            }
        }

    }

    private List<KTVBookTablePhasePeriodDo> findSpecifiedKTVBookTablePhasePeriodDo(KTVBookTableWeekPeriodDo weekPeriod, KtvWidgetContext widgetContext) {
        List<KTVBookTableDateDo> ktvBookTableDates = weekPeriod.getKtvBookTableDates();
        if (CollectionUtils.isEmpty(ktvBookTableDates)) {
            return null;
        }
        for (KTVBookTableDateDo ktvBookTableDateDo : ktvBookTableDates) { //[“周三”, "周四”]
            if (ktvBookTableDateDo.getDayOfWeek() == widgetContext.getDayOfWeek()) {
                return ktvBookTableDateDo.getPhasePeriods();
            }
        }
        return null;
    }

    private void fillKTVBookTable(final KtvWidgetContext widgetContext) {

        KTVBookTable ktvBookTable = getKtvBookTable(widgetContext);
        widgetContext.setKtvBookTable(ktvBookTable);
    }

    /**
     * 注意：该方法执行后会把widgetContext的shopid设置为点评的shopid
     * 如果需要参数中的shopid，需要判断dp/mt，mtshopId在extra中
     *
     * @param widgetContext
     * @return
     */
    private KTVBookTable getKtvBookTable(final KtvWidgetContext widgetContext) {

        Map<String, String> clientContext = (Map<String, String>) widgetContext.getExtras().get("clientContext");
        //参数传递的是点评或者美团的shopid
        Integer shopId = widgetContext.getShopId().intValue();
        int channel = KTVChannel.descOf(clientContext.get(KTVClientContext.CHANNEL)).code;
        if (channel == KTVChannel.MT.getCode()) {
            widgetContext.setMtShopId(new Long(shopId));
            try {
                int dpShopId = godBiz.loadDpShopIdByMtShopId(shopId);
                if (dpShopId <= 0) {
                    return KTVBookTable.EMPTY_TABLE;
                }
                //最终会把shopid设置为点评shopid
                widgetContext.setShopId(new Long(dpShopId));
            } catch (Exception e) {
                LOGGER.error("[ktv book table 4 ] error in loadDpShopIdByMtShopId ,mt shopId is {}", widgetContext.getShopId(), e);
                return KTVBookTable.EMPTY_TABLE;
            }
        }
        Future<List<ProductItem>> future = executorForQueryProduct.submit(new Callable<List<ProductItem>>() {
            @Override
            public List<ProductItem> call() throws Exception {
                return ktvProductQueryBiz.getOnlineProductItemList(widgetContext.getShopId().intValue());
            }
        });
        widgetContext.setProductQueryFuture(future);
        KTVBookTable ktvBookTable = (KTVBookTable) ktvBookTableWidgetV2.getWidget(widgetContext);
        return ktvBookTable;
    }

}
