package com.dianping.tpfun.mapi.zhpintuan.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> wanru.wang <p>
 * @version 1.0 2017-09-27
 * @since beauty-resources-web 1.0
 */
@Data
public class TuanDetail implements Serializable
{
    private Integer productId;

    private Integer skuId;

    private BigDecimal price;//价格

    private Integer status;   // status   1:拼团中  2:拼成功   3:超时  4:过期

    private Long teamLdUserId;

    private List<UserInfo> joinUsers;

    private Integer needMembers;

    private Integer joinMembers;

    private Integer leftMembers;//剩余人数

    private Long pinTuanId;

    private Boolean joiner;

    private String quan;//消费券码

    private Integer quanStatus;   //券状态见https://wiki.sankuai.com/pages/viewpage.action?pageId=883298113

    private Long leftTime;   //拼团剩余时间,仅当拼团状态为进行中时才需要

}
          