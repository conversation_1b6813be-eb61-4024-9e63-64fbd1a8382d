package com.dianping.tpfun.mapi.zhpintuan.dto;

import com.dianping.tpfun.mapi.zhbook.dto.prepay.StructProductDetail;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: yangweiqin
 * @Date: 2019-07-11
 */
public class PinProductContent implements Serializable {

    /**
     * 图文列表
     */
    private List<RichText> richTextDetail;

    /**
     * 打包商品详情
     */
    private Object productContent;

    /**
     * 温馨提醒
     */
    private String warmTip;

    /**
     * 购买后有效时长
     */
    private String useDaysAfterOrder;
    /**
     * 可用起始时间
     */
    private String useBeginTime;
    /**
     * 可用结束时间
     */
    private String useEndTime;

    /**
     * 提前预约时间
     */
    private String bookAheadTime;
    /**
     * 是否预约
     */
    private Boolean bookable;

    /**
     * 预付市场价
     */
    private BigDecimal marketPrice;

    /**
     * 预付优惠后的实际售价
     */
    private BigDecimal price;
    /**
     * 新版医美预付商详页数据
     */
    private StructProductDetail structProductDetail;

    public List<RichText> getRichTextDetail() {
        return richTextDetail;
    }

    public void setRichTextDetail(List<RichText> richTextDetail) {
        this.richTextDetail = richTextDetail;
    }

    public Object getProductContent() {
        return productContent;
    }

    public void setProductContent(Object productContent) {
        this.productContent = productContent;
    }

    public String getWarmTip() {
        return warmTip;
    }

    public void setWarmTip(String warmTip) {
        this.warmTip = warmTip;
    }

    public String getUseDaysAfterOrder() {
        return useDaysAfterOrder;
    }

    public void setUseDaysAfterOrder(String useDaysAfterOrder) {
        this.useDaysAfterOrder = useDaysAfterOrder;
    }

    public String getUseBeginTime() {
        return useBeginTime;
    }

    public void setUseBeginTime(String useBeginTime) {
        this.useBeginTime = useBeginTime;
    }

    public String getUseEndTime() {
        return useEndTime;
    }

    public void setUseEndTime(String useEndTime) {
        this.useEndTime = useEndTime;
    }

    public String getBookAheadTime() {
        return bookAheadTime;
    }

    public void setBookAheadTime(String bookAheadTime) {
        this.bookAheadTime = bookAheadTime;
    }

    public Boolean getBookable() {
        return bookable;
    }

    public void setBookable(Boolean bookable) {
        this.bookable = bookable;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public StructProductDetail getStructProductDetail() {
        return structProductDetail;
    }

    public void setStructProductDetail(StructProductDetail structProductDetail) {
        this.structProductDetail = structProductDetail;
    }
}
