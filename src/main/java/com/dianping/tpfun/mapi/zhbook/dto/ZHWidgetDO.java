package com.dianping.tpfun.mapi.zhbook.dto;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * 综合预订展示模块类型
 *
 */
@MobileDo(name = "ZHWidget")
public class ZHWidgetDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 不展示Widget
     */
    public final static ZHWidgetDO NO_SHOW_WIDGET = new ZHWidgetDO();

    static {
        NO_SHOW_WIDGET.setShowable(false);
        NO_SHOW_WIDGET.setWidgetName("unShowWidget");
    }

    /**
     * 模块名称
     */
    @MobileDo.MobileField(name = "WidgetName")
    private String widgetName;

    @MobileDo.MobileField(name = "URL")
    private String url;

    @MobileDo.MobileField(name = "Showable")
    private boolean showable;

    public String getWidgetName() {
        return widgetName;
    }

    public void setWidgetName(String widgetName) {
        this.widgetName = widgetName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isShowable() {
        return showable;
    }

    public void setShowable(boolean showable) {
        this.showable = showable;
    }

}
