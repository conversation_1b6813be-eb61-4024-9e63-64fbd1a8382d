package com.dianping.tpfun.mapi.zhbook.bean;

import java.util.List;

import com.google.common.collect.Lists;

public class BeautyBookProcessInfo {
	/* 默认退款时间限制 60分钟内可退 */
	private static final int Default_RefundTimeLen_InMinutes = 60; 
	/* 商品ID */
	private int productId;
	/* 服务流程 */
	private List<BeautyBookServiceProcessInfo> serviceprocess;
	/* 退款规则-服务开始xx时间前可退, 单位分钟 */
	//private int refundTimeLen = Default_RefundTimeLen_InMinutes;
	/* 补充说明 */
	//private List<String> otherInfos;

	public BeautyBookProcessInfo(int productId) {
		this.productId = productId;
		this.serviceprocess = Lists.newArrayList();
		//this.otherInfos = Lists.newArrayList();
	}
	
	public void appendServiceProcess(BeautyBookServiceProcessInfo bbsp) {
		if (null == bbsp) {
			return;
		}
		this.serviceprocess.add(bbsp);
	}
	
//	public void appendOtherInfo(String otherInfo) {
//		if (StringUtils.isEmpty(otherInfo)) {
//			return;
//		}
//		this.otherInfos.add(otherInfo);
//	}
	
	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public List<BeautyBookServiceProcessInfo> getServiceprocess() {
		return serviceprocess;
	}

	public void setServiceprocess(List<BeautyBookServiceProcessInfo> serviceprocess) {
		this.serviceprocess = serviceprocess;
	}

}
