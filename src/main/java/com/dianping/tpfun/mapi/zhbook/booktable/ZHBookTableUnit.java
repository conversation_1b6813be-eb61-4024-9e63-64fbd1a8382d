package com.dianping.tpfun.mapi.zhbook.booktable;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: jy
 * Date: 2017/7/18
 * Time: 下午3:00
 * To change this template use File | Settings | File Templates.
 */
@TypeDoc(description = "BookTableUnit")
@MobileDo(name = "BookTableUnit")
public class ZHBookTableUnit implements Serializable {
    @FieldDoc(description = "Name")
    @MobileDo.MobileField(name = "Name")
    private String name;

    @FieldDoc(description = "SubName")
    @MobileDo.MobileField(name = "SubName")
    private String subName;

    @FieldDoc(description = "DescItems")
    @MobileDo.MobileField(name = "DescItems")
    private List<String> descItems;

    @FieldDoc(description = "Price")
    @MobileDo.MobileField(name = "Price")
    private String price;

    @FieldDoc(description = "PriceUnit")
    @MobileDo.MobileField(name = "PriceUnit")
    private String priceUnit;

    @FieldDoc(description = "PriceDesc")
    @MobileDo.MobileField(name = "PriceDesc")
    private String priceDesc;

    @FieldDoc(description = "Tag")
    @MobileDo.MobileField(name = "Tag")
    private String tag;

    /**
     * 优惠信息，最多两个字
     */
    @FieldDoc(description = "Promo")
    @MobileDo.MobileField(name = "Promo")
    private String promo;

    @FieldDoc(description = "ButtonName")
    @MobileDo.MobileField(name = "ButtonName")
    private String buttonName;

    @FieldDoc(description = "BookUrlSuffix")
    @MobileDo.MobileField(name = "BookUrlSuffix")
    private String bookUrlSuffix;

    @FieldDoc(description = "Clickable")
    @MobileDo.MobileField(name = "Clickable")
    private boolean clickable;

    @FieldDoc(description = "MarketPrice")
    @MobileDo.MobileField(name = "MarketPrice")
    private String marketPrice;

    /**
     * 点击预订按钮的交互行为、按业务区分
     * 0-默认跳转到新页面
     * 1-弹出酒吧套餐选择浮层
     */
    @FieldDoc(description = "ClickType")
    @MobileDo.MobileField(name = "ClickType")
    private int clickType;
    @FieldDoc(description = "ButtonSubName")
    @MobileDo.MobileField(name = "ButtonSubName")
    private String buttonSubName;

    /**
     * 商品id
     */
    @FieldDoc(description = "ProductId")
    @MobileDo.MobileField(name = "ProductId")
    private int productId;

    /**
     * 用于打点上报的商品id
     */
    private int productIdForLabs;

    /**
     * 富文本描述, string类型，用于移动端解析用
     */
    @FieldDoc(description = "richTextDesc")
    @MobileDo.MobileField(name = "richTextDesc")
    private String richTextDesc;

    @FieldDoc(description = "兼容MRN，从richTextDesc提取出的纯文本")
    @MobileDo.MobileField(name = "plainRichText")
    private List<String> plainRichText;

    /**
     * item id
     */
    @FieldDoc(description = "productItemId")
    @MobileDo.MobileField(name = "productItemId")
    private long productItemId;

    /**
     * 时段开始时间
     */
    @MobileDo.MobileField(name = "beginTime")
    private Long beginTime;

    /**
     * 时段结束时间
     */
    @MobileDo.MobileField(name = "endTime")
    private Long endTime;

    @MobileDo.MobileField(name = "promoTags")
    private List<BookTablePromoTag> promoTags;

    @FieldDoc(description = "玩乐卡信息，为乐兼容MRN，从promoTags分离出来")
    @MobileDo.MobileField(name = "playCard")
    private BookTablePromoTag playCard;

    @FieldDoc(description = "优惠立减信息，为乐兼容MRN，从promoTags分离出来")
    @MobileDo.MobileField(name = "promoTagsForMRN")
    private List<BookTablePromoTag> promoTagsForMRN;

    /**
     *
     */
    @MobileDo.MobileField(key = 0xce7c)
    private String productUrl;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x273c)
    private DzBookTablePoolInfo poolInfo;

    @MobileDo.MobileField(key = 0x90b4)
    private String labs;

    public String getProductUrl() {
        return productUrl;
    }

    public void setProductUrl(String productUrl) {
        this.productUrl = productUrl;
    }

    public DzBookTablePoolInfo getPoolInfo() {
        return poolInfo;
    }

    public void setPoolInfo(DzBookTablePoolInfo poolInfo) {
        this.poolInfo = poolInfo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public List<String> getDescItems() {
        return descItems;
    }

    public void setDescItems(List<String> descItems) {
        this.descItems = descItems;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getPriceUnit() {
        return priceUnit;
    }

    public void setPriceUnit(String priceUnit) {
        this.priceUnit = priceUnit;
    }

    public String getPriceDesc() {
        return priceDesc;
    }

    public void setPriceDesc(String priceDesc) {
        this.priceDesc = priceDesc;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getPromo() {
        return promo;
    }

    public void setPromo(String promo) {
        this.promo = promo;
    }

    public String getButtonName() {
        return buttonName;
    }

    public void setButtonName(String buttonName) {
        this.buttonName = buttonName;
    }

    public String getBookUrlSuffix() {
        return bookUrlSuffix;
    }

    public void setBookUrlSuffix(String bookUrlSuffix) {
        this.bookUrlSuffix = bookUrlSuffix;
    }

    public boolean isClickable() {
        return clickable;
    }

    public void setClickable(boolean clickable) {
        this.clickable = clickable;
    }

    public String getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(String marketPrice) {
        this.marketPrice = marketPrice;
    }

    public int getClickType() {
        return clickType;
    }

    public void setClickType(int clickType) {
        this.clickType = clickType;
    }

    public String getButtonSubName() {
        return buttonSubName;
    }

    public void setButtonSubName(String buttonSubName) {
        this.buttonSubName = buttonSubName;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public String getRichTextDesc() {
        return this.richTextDesc;
    }

    public void setRichTextDesc(String richTextDesc) {
        this.richTextDesc = richTextDesc;
    }

    public long getProductItemId() {
        return productItemId;
    }

    public void setProductItemId(long productItemId) {
        this.productItemId = productItemId;
    }

    public Long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public List<BookTablePromoTag> getPromoTags() {
        return promoTags;
    }

    public void setPromoTags(List<BookTablePromoTag> promoTags) {
        this.promoTags = promoTags;
    }

    public List<String> getPlainRichText() {
        return plainRichText;
    }

    public void setPlainRichText(List<String> plainRichText) {
        this.plainRichText = plainRichText;
    }

    public BookTablePromoTag getPlayCard() {
        return playCard;
    }

    public void setPlayCard(BookTablePromoTag playCard) {
        this.playCard = playCard;
    }

    public List<BookTablePromoTag> getPromoTagsForMRN() {
        return promoTagsForMRN;
    }

    public void setPromoTagsForMRN(List<BookTablePromoTag> promoTagsForMRN) {
        this.promoTagsForMRN = promoTagsForMRN;
    }

    public void setLabs(String labs) {
        this.labs = labs;
    }

    public String getLabs() {
        return labs;
    }

    public int getProductIdForLabs() {
        return productIdForLabs;
    }

    public void setProductIdForLabs(int productIdForLabs) {
        this.productIdForLabs = productIdForLabs;
    }
}
