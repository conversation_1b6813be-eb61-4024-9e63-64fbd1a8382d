package com.dianping.tpfun.mapi.zhbook.dto.stock;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

/**
 * @Author: huqi
 * @Date: 2020/11/19 9:05 下午
 */
@MobileDo(id = 0x5b90)
public class DzProductOcean implements Serializable {
    /**
     *
     */
    @MobileField(key = 0x9dbb)
    private String bidView;

    /**
     *
     */
    @MobileField(key = 0xcba3)
    private String bidClick;

    /**
     *
     */
    @MobileField(key = 0xf312)
    private String abtest;

    /**
     *
     */
    @MobileField(key = 0xbffc)
    private String category;

    /**
     *
     */
    @MobileField(key = 0x90b4)
    private String labs;

    public String getBidView() {
        return bidView;
    }

    public void setBidView(String bidView) {
        this.bidView = bidView;
    }

    public String getBidClick() {
        return bidClick;
    }

    public void setBidClick(String bidClick) {
        this.bidClick = bidClick;
    }

    public String getAbtest() {
        return abtest;
    }

    public void setAbtest(String abtest) {
        this.abtest = abtest;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getLabs() {
        return labs;
    }

    public void setLabs(String labs) {
        this.labs = labs;
    }
}
