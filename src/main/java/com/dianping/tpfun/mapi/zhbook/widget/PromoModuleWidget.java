package com.dianping.tpfun.mapi.zhbook.widget;

import com.alibaba.fastjson.JSONArray;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.dianping.tpfun.mapi.dto.promo.ProductPromoInfo;
import com.dianping.tpfun.mapi.tohome.context.PageWidgetContext;
import com.dianping.tpfun.mapi.zhbook.helper.PromoHelper;
import com.dianping.tpfun.product.api.sku.ProductService;
import com.dianping.tpfun.product.api.sku.promo.PromoInfoService;
import com.dianping.tpfun.product.api.sku.promo.dto.SkuPromoInfo;
import com.dianping.tpfun.product.api.sku.promo.request.QuerySkuPromoInfoRequest;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class PromoModuleWidget {

    private static Logger LOGGER = LoggerFactory.getLogger(PromoModuleWidget.class);

    @Reference(timeout = 1000, url = "http://service.dianping.com/tpfunService/skuProductService_1.0.0")
    private ProductService productService;

    @Reference(timeout = 1000, url = "http://service.dianping.com/tpfunService/skuPromoInfoService_1.0.0")
    private PromoInfoService promoInfoService;

    @Autowired
    PromoHelper promoHelper;

    private static final int PROMO_MODULE_TEMPLATEID = 237;

    public ProductPromoInfo getProductPromoModule(PageWidgetContext context) {
        //获取skuid
        List<Integer> skuIds = getSkuIds(context.getProductId());
        QuerySkuPromoInfoRequest querySkuPromoInfoRequest = promoHelper.buildSkuPromoQueryRequest(skuIds,
                context, PROMO_MODULE_TEMPLATEID);
        Map<Integer, SkuPromoInfo> promoMap = promoInfoService.querySkuPromoInfo(querySkuPromoInfoRequest);
        if (MapUtils.isEmpty(promoMap)) {
            LOGGER.info("[getProductPromoModule] No PromoFound for skuList, skuList = {}", JSONArray.toJSONString(skuIds));
            return new ProductPromoInfo();
        }

        return promoHelper.buildPromoModule(promoMap, context.getProductId(), context.getChannel());
    }

    private List<Integer> getSkuIds(int productId) {
        Map<Integer, List<Integer>> skuMap = productService.getItemIdsByProductIds(Lists.newArrayList(productId));
        if (MapUtils.isEmpty(skuMap) || CollectionUtils.isEmpty(skuMap.get(productId))) {
            LOGGER.warn("[getProductPromoModule] No sku info found!, productId={}", productId);
            return Collections.emptyList();
        }
        return skuMap.get(productId);
    }
}
