package com.dianping.tpfun.mapi.zhbook.utils;

import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.FinalPaySceneEnum;
import com.dianping.tpfun.order.api.enums.Platform;
import com.dianping.tpfun.product.api.sku.common.enums.CooperationBizTypeEnum;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by wangzi07 on 2018/10/15.
 */
public class PayUtils {
    private static final Map<Integer, PayPlatform> orderPlatformToPayPlatformMap =
            Collections.unmodifiableMap(new HashMap<Integer, PayPlatform>() {{
                put(Platform.dp_iphone_native.getCode(), PayPlatform.dp_iphone_native);
                put(Platform.dp_ipad_native.getCode(), PayPlatform.dp_iphone_native);
                put(Platform.dp_android_native.getCode(), PayPlatform.dp_android_native);

                put(Platform.dp_iphone_weixin.getCode(), PayPlatform.weixin_bank);
                put(Platform.dp_android_weixin.getCode(), PayPlatform.weixin_bank);
                put(Platform.dp_ipad_weixin.getCode(), PayPlatform.weixin_bank);

                put(Platform.mt_iphone_native.getCode(), PayPlatform.mt_iphone_native);
                put(Platform.mt_android_native.getCode(), PayPlatform.mt_android_native);
                put(Platform.mt_ipad_native.getCode(), PayPlatform.mt_iphone_native);

                put(Platform.dp_iphone_miniprogram.getCode(), PayPlatform.weixin_api);
                put(Platform.dp_ipad_miniprogram.getCode(), PayPlatform.weixin_api);
                put(Platform.dp_android_miniprogram.getCode(), PayPlatform.weixin_api);

                put(Platform.dp_iphone_solominiprogram.getCode(), PayPlatform.weixin_api);
                put(Platform.dp_ipad_solominiprogram.getCode(), PayPlatform.weixin_api);
                put(Platform.dp_android_solominiprogram.getCode(), PayPlatform.weixin_api);

                put(Platform.dp_iphone_mobileqq.getCode(), PayPlatform.mobileQ_iphone_m);
                put(Platform.dp_ipad_mobileqq.getCode(), PayPlatform.mobileQ_iphone_m);
                put(Platform.dp_android_mobileqq.getCode(), PayPlatform.mobileQ_android_m);

                put(Platform.dp_iphone_dzmcminiprogram.getCode(), PayPlatform.common_weixin_api);
                put(Platform.dp_android_dzmcminiprogram.getCode(), PayPlatform.common_weixin_api);
                put(Platform.dp_ipad_dzmcminiprogram.getCode(), PayPlatform.common_weixin_api);
                put(Platform.dp_winphone_dzmcminiprogram.getCode(), PayPlatform.common_weixin_api);

                put(Platform.mt_iphone_miniprogram.getCode(), PayPlatform.mt_weixin_api);
                put(Platform.mt_ipad_miniprogram.getCode(), PayPlatform.mt_weixin_api);
                put(Platform.mt_android_miniprogram.getCode(), PayPlatform.mt_weixin_api);

                put(Platform.mt_iphone_solominiprogram.getCode(), PayPlatform.mt_weixin_api);
                put(Platform.mt_ipad_solominiprogram.getCode(), PayPlatform.mt_weixin_api);
                put(Platform.mt_android_solominiprogram.getCode(), PayPlatform.mt_weixin_api);

                put(Platform.mt_iphone_mobileqq.getCode(), PayPlatform.mobileQ_iphone_m);
                put(Platform.mt_ipad_mobileqq.getCode(), PayPlatform.mobileQ_iphone_m);
                put(Platform.mt_android_mobileqq.getCode(), PayPlatform.mobileQ_android_m);
            }});

    private static final Map<Integer, FinalPaySceneEnum> orderBizTypeToFinalPaySceneMap =
            Collections.unmodifiableMap(new HashMap<Integer, FinalPaySceneEnum>() {{
                put(CooperationBizTypeEnum.MEDICALBEAUTY_PREPAY.getBizType(), FinalPaySceneEnum.MedicalBeautyFinalPay);
            }});

    public static int getPayPlatform(int platform) {
        PayPlatform result = orderPlatformToPayPlatformMap.get(platform);
        if (null != result) {
            return result.getCode();
        }

        if (Platform.isMTOrderChannel(platform)) {
            result = PayPlatform.mt_wap_m;
        } else {
            result = PayPlatform.tg_wap_m;
        }
        return result.getCode();
    }

    public static int getFinalPayScene(int cooperationBizType) {
        FinalPaySceneEnum result = orderBizTypeToFinalPaySceneMap.get(cooperationBizType);
        return result != null ? result.sceneCode : 0;
    }
}
