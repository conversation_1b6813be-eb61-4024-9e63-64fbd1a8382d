package com.dianping.tpfun.mapi.zhbook.widget;

import com.alibaba.fastjson.JSON;
import com.dianping.gmkt.activity.enums.AppPlatform;
import com.dianping.ktv.shop.api.enums.CoopShopConfigAttrIdNameEnum;
import com.dianping.ktv.shop.api.protocol.ShopResponse;
import com.dianping.ktv.shop.coop.dto.CoopShopConfigDTO;
import com.dianping.ktv.shop.coop.request.CoopShopConfigQueryRequest;
import com.dianping.ktv.shop.coop.service.CoopShopConfigRemoteService;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.dianping.shopremote.remote.ShopService;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.shopremote.remote.dto.ShopUuidDTO;
import com.dianping.tpfun.mapi.CatMonitorKey;
import com.dianping.tpfun.mapi.GodBiz;
import com.dianping.tpfun.mapi.agent.ShopUuidAgent;
import com.dianping.tpfun.mapi.agent.bean.MyShopUuidDTO;
import com.dianping.tpfun.mapi.helper.BookTableHelper;
import com.dianping.tpfun.mapi.utils.DateUtils;
import com.dianping.tpfun.mapi.utils.LionConfigUtils;
import com.dianping.tpfun.mapi.utils.UrlUtil;
import com.dianping.tpfun.mapi.zhbook.booktable.*;
import com.dianping.tpfun.mapi.zhbook.enums.PoolType;
import com.dianping.tpfun.mapi.zhbook.utils.CatMetricUtils;
import com.dianping.tpfun.mapi.zhbook.utils.TimeConfig;
import com.dianping.tpfun.product.api.common.IResponse;
import com.dianping.tpfun.product.api.sku.ProductService;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.dianping.tpfun.product.api.stocklogic.dto.DayStockDTO;
import com.dianping.tpfun.product.api.stocklogic.dto.QueryDayCountStockRequest;
import com.dianping.tpfun.product.api.stocklogic.service.StockQueryService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: huqi
 * @Date: 2021/2/24 8:17 下午
 */
@Component
@Slf4j
public class PoolPopupWidget {
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static final String SHOPID = "shopid";
    @Autowired
    private BookTableHelper bookTableHelper;
    @Reference(url="com.dianping.ktv.shop.coop.service.CoopShopConfigRemoteService")
    private CoopShopConfigRemoteService coopShopConfigRemoteService;
    @Reference(timeout = 3000, url = "http://service.dianping.com/shopService/shopService_2.0.0")
    private ShopService shopService;
    @Autowired
    private StockQueryService stockQueryService;
    @Autowired
    public ProductService skuService;
    @Autowired
    private GodBiz godBiz;

    @Autowired
    @Qualifier("sinaiDpPoiService")
    private DpPoiService dpPoiService;

    @Autowired
    private ShopUuidAgent shopUuidAgent;

    public DzPopupResponse popup(long shopId, int productId, String shopUuid, AppPlatform appPlatform, boolean isH5, int relationId){
        long dpShopId = shopId;
        if (AppPlatform.DP == appPlatform && dpShopId == 0 && StringUtils.isNotBlank(shopUuid)){
            MyShopUuidDTO shopUuidDTO = shopUuidAgent.loadByUuid(shopUuid);
            dpShopId = shopUuidDTO != null ? shopUuidDTO.getShopId() : 0;
        }
        if (AppPlatform.MT == appPlatform){
            dpShopId = godBiz.loadDpShopIdByMtShopIdL(shopId);
        }
        if (StringUtils.isBlank(shopUuid) && appPlatform == AppPlatform.DP) {
            DpPoiRequest dpPoiRequest = new DpPoiRequest();
            dpPoiRequest.setShopIds(Lists.newArrayList(shopId));
            dpPoiRequest.setFields(Lists.newArrayList("uuid", "shopId"));
            List<DpPoiDTO> dpPoiDTOs = null;
            try {
                dpPoiDTOs = dpPoiService.findShopsByShopIds(dpPoiRequest);
                shopUuid = CollectionUtils.isNotEmpty(dpPoiDTOs) ? dpPoiDTOs.get(0).getUuid() : "";
            } catch (TException e) {
                log.error("[findShopsByShopIds] encounts exception, dpPoiRequest {} ", JSON.toJSONString(dpPoiRequest), e);
            }

        }
        DzPopupResponse poolPopResponse = new DzPopupResponse();
        DzPopupComponent dzPopupComponent = new DzPopupComponent();
        dzPopupComponent.setTitleComponent(buildTitle());
        FilterComponent filterTabs = new FilterComponent();
        filterTabs.setFilterBtns(getDzFiltersBans(dpShopId,productId,shopUuid,appPlatform,isH5,relationId,shopId));
        dzPopupComponent.setFilterComponent(filterTabs);
        poolPopResponse.setDzPopupComponent(dzPopupComponent);

        CatMetricUtils.addMetricForCount(CatMonitorKey.BasicView.poolpopup_display_count, appPlatform == null ? null : appPlatform.getDesc(), isH5 ? "native" : "h5");
        return poolPopResponse;
    }

    private List<FilterButton> getDzFiltersBans(long shopId, int productId, String shopUuid, AppPlatform app,
                                                boolean isH5, int relationId, long mtShopId) {
        List<FilterButton> dzFilters = bookTableHelper.buildDzFilterButton();
        Long skuId = getSkuId(productId);
        Long subSkuId = relationId > 0 ? getSkuId(relationId) : 0;
        Product product =  skuService.getBaseProductById(relationId > 0 ? relationId : productId);
        int spuType = product.getSpuType();
        Boolean isOpenPool =  Boolean.valueOf(product.getFirstAttrValue("isOpenPool"));
        Map<Long, TimeConfig> map = getShopTimeConfig(shopId,subSkuId > 0 ? subSkuId : skuId,dzFilters);
        boolean isBarPackRelationPool = isBarPackRelationPool(relationId,product);
        dzFilters.forEach(dzFiltersBtn -> {
            dzFiltersBtn.setExtra(buildExtra(dzFiltersBtn.getFilterBtnId()));
            if (map != null && map.containsKey(dzFiltersBtn.getFilterBtnId())){
                TimeConfig timeConfig = map.get(dzFiltersBtn.getFilterBtnId());
                if (timeConfig.isDateDisable()){
                    dzFiltersBtn.setTag("当前无可订时间");
                }
                FilterButton filterButton2 = new FilterButton(PoolType.making_block.getValue(),"包场预订",
                        getDzFilters(dzFiltersBtn,timeConfig, skuId, app == AppPlatform.DP ? shopId : mtShopId, shopUuid, spuType, PoolType.making_block.getValue(), app,isH5,isBarPackRelationPool,subSkuId));
                if (isOpenPool && relationId == 0){
                    FilterButton filterButton = new FilterButton(PoolType.pool.getValue(),"拼场预订",
                            getDzFilters(dzFiltersBtn,timeConfig,skuId,app == AppPlatform.DP ? shopId : mtShopId,shopUuid,spuType,PoolType.pool.getValue(), app,isH5,isBarPackRelationPool,subSkuId));
                    dzFiltersBtn.setChildren(Lists.newArrayList(filterButton,filterButton2));
                }else{
                    dzFiltersBtn.setChildren(Lists.newArrayList(filterButton2));
                }
            }else{
                dzFiltersBtn.setTag("座位已订满，请选择其他日期");
            }
        });
        return dzFilters;
    }

    private boolean isBarPackRelationPool(int relationId, Product product) {
        try {
            if (relationId == 0) {
                return true;
            }
            return bookTableHelper.isOpenPool(product);
        } catch (Exception e) {
            log.error("getPackPool error", e);
        }
        return true;
    }


    private List<FilterButton> getDzFilters(FilterButton dzFiltersBtn, TimeConfig timeConfig, Long skuId, long shopId,
                                            String shopUuid, int spuType, int value, AppPlatform app, boolean isH5,
                                            boolean isBarPackRelationPool, long relationId) {
        long today = DateUtils.buildLastTimeDay(dzFiltersBtn.getFilterBtnId()).getTime();
        return timeConfig.getList().stream().map(time -> {
            boolean cross = time > today;
            FilterButton btn = new FilterButton();
            btn.setExtra(buildExtra(dzFiltersBtn.getFilterBtnId(),skuId,shopId,shopUuid,spuType,value,cross,time,
                    app,isH5,isBarPackRelationPool,relationId));
            btn.setTitle(buildTitle(time));
            btn.setFilterBtnId(time);
            btn.setTag(cross ? "次日" : "");
            btn.setSubTitle(buildSubTitle(dzFiltersBtn,cross,btn.getTitle().getText()));
            return btn;
        }).collect(Collectors.toList());
    }

    private RichLabel buildSubTitle(FilterButton dzFiltersBtn, boolean cross, String text) {
        if (!cross){
            return new RichLabel(buildText(dzFiltersBtn,text));
        }
        List<FilterButton> list = bookTableHelper.buildDzFilterButton(8);
        for (int i = 0; i < 7; i++) {
            if (list.get(i).getFilterBtnId() == dzFiltersBtn.getFilterBtnId()){
                    return new RichLabel(buildText(list.get(i+1),text));
            }
        }
        return null;
    }

    private String buildText(FilterButton dzFiltersBtn, String text) {
        String template = "%s(%s) %s到店";
        return String.format(template,dzFiltersBtn.getTitle().getText(),dzFiltersBtn.getSubTitle().getText(),text);
    }


    private String buildExtra(long filterBtnId, Long skuId, long shopId, String shopUuid, int spuType, int type, boolean cross, long arriveTime, AppPlatform app,boolean isH5,boolean isBarPackRelationPool,long relationId) {
        Map<String,String> map = new HashMap<>();
        map.put("url",getUrl(shopId,shopUuid,filterBtnId,skuId.intValue(),spuType,app,type,arriveTime,cross,isH5,isBarPackRelationPool,relationId));
        map.put("selectDate",String.valueOf(filterBtnId));
        Gson gs = new GsonBuilder().create();
        return StringEscapeUtils.unescapeJava(gs.toJson(map));
    }


    private String buildExtra(long filterBtnId) {
        Map<String,String> map = new HashMap<>();
        map.put("selectDate",String.valueOf(filterBtnId));
        Gson gs = new GsonBuilder().create();
        return StringEscapeUtils.unescapeJava(gs.toJson(map));
    }

    public String getUrl(long shopId, String shopUuId, long selectDate, int skuId, int spuType, AppPlatform appPlatform, int backRoomMode, long arriveTime, Boolean cross, boolean isH5, boolean isBarPackRelationPool,long relationId){
        String url = LionConfigUtils.getPoolTradeUrlNew() + "&shopid=" + shopId +
                "&shopuuid=" + shopUuId +
                "&arriveTime=" + arriveTime +
                "&purchasedate=" + selectDate +
                "&productitemid=" + (relationId > 0 ? relationId : skuId) +
                "&sputype=" + spuType +
                (isBarPackRelationPool ? ("&backRoomMode=" + backRoomMode) : "")  +
                (relationId > 0 ? ("&subproductitemid=" + skuId) : "") +
                "&crossday=" + cross.toString() +
                "&product=" + (appPlatform == AppPlatform.DP ? "dpapp" : "mtapp");
        return UrlUtil.getAPPUrl4H5(url,appPlatform,isH5);
    }

    private List<Long> getStockDates(long shopId, Long skuId, List<FilterButton> dzFilters) {
        List<Long> selectDate = dzFilters.stream().map(FilterButton::getFilterBtnId).collect(Collectors.toList());
        IResponse<List<DayStockDTO>> response = stockQueryService.queryDayCountStock(buildRequest(shopId, skuId, selectDate));
        if (response != null && response.isSuccess()) {
            return response.getResult().stream().filter(dayStockDTO -> dayStockDTO.getStock() > 0)
                                                    .map(dayStockDTO -> dayStockDTO.getCountDate().getTime())
                                                        .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private QueryDayCountStockRequest buildRequest(long shopId, Long skuId, List<Long> selectDate) {
        QueryDayCountStockRequest queryDayCountStockRequest = new QueryDayCountStockRequest();
        queryDayCountStockRequest.setSkuId(skuId);
        Map<String, String> map = new HashMap<>();
        map.put(SHOPID,String.valueOf(shopId));
        queryDayCountStockRequest.setParams(map);
        queryDayCountStockRequest.setStartDate(DateUtils.buildStartTimeDay(selectDate.get(0)));
        queryDayCountStockRequest.setEndDate(DateUtils.buildLastTimeDay(selectDate.get(selectDate.size()-1)));
        return queryDayCountStockRequest;
    }

    private Long getSkuId(int productId) {
        Map<Integer, List<Integer>> map = skuService.getItemIdsByProductIds(Lists.newArrayList(productId));
        if (map != null && map.containsKey(productId)){
            return map.get(productId).stream().map(Integer::longValue).findFirst().orElse(0L);
        }
        return 0L;
    }

    private MainTitleComponent buildTitle() {
        MainTitleComponent shelfTitle = new MainTitleComponent();
        shelfTitle.setTitle("请选择到店时间");
        return shelfTitle;
    }

    private RichLabel buildTitle(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM);
        return new RichLabel(sdf.format(DateUtils.getDateByMill(time)).substring(11));
    }

    /**
     * 获取商家营业时间
     * @param shopId
     * @param skuId
     * @param dzFilters
     * @return
     * @throws Exception
     */
    private Map<Long, TimeConfig> getShopTimeConfig(long shopId, Long skuId, List<FilterButton> dzFilters) {
        List<Long> selectDate = getStockDates(shopId, skuId, dzFilters);
        if (CollectionUtils.isEmpty(selectDate)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM);
        try {
            CoopShopConfigQueryRequest request = new CoopShopConfigQueryRequest();
            request.setLongShopId(shopId);
            request.setCooperationBizType(100088);
            request.setAttrNameList(Lists.newArrayList(CoopShopConfigAttrIdNameEnum.Reserve_Period.getAttrName(),
                    CoopShopConfigAttrIdNameEnum.Disable_Date.getAttrName(),
                    CoopShopConfigAttrIdNameEnum.Add_Order_Before.getAttrName()));
            ShopResponse<List<CoopShopConfigDTO>> shopResponse = coopShopConfigRemoteService.findShopConfigList(request);
            if (shopResponse != null && CollectionUtils.isNotEmpty(shopResponse.getResult())) {
                String[] reserveDate = null;
                Integer addTime = null;
                List<CoopShopConfigDTO> configs = shopResponse.getResult();
                for (CoopShopConfigDTO config : configs) {
                    if (config.getAttrName().equals(CoopShopConfigAttrIdNameEnum.Reserve_Period.getAttrName())) {
                        reserveDate = config.getAttrValue().split(",");
                    }
                    if (config.getAttrName().equals(CoopShopConfigAttrIdNameEnum.Add_Order_Before.getAttrName())) {
                        addTime = Integer.parseInt(config.getAttrValue());
                    }
                }

                final String[] reserveDateline = reserveDate;
                final Integer addTimeline = addTime;
                if (reserveDateline != null && reserveDateline.length >= 2){
                    return selectDate.stream()
                            .collect(Collectors.toMap(t -> t, t -> {
                                try {
                                    String yyyyMM = getYm(t);
                                    if (CollectionUtils.isNotEmpty(configs)){
                                        List<String[]> disableDates =
                                                configs.stream().filter(config -> config.getAttrName().equals(CoopShopConfigAttrIdNameEnum.Disable_Date.getAttrName()))
                                                        .map(config -> config.getAttrValue().split(";")).collect(Collectors.toList());
                                        if (inDate(disableDates, t)) {
                                            return TimeConfig.buildDisable();
                                        }
                                    }
                                    Date startTime = sdf.parse(yyyyMM + " " + reserveDateline[0]);
                                    Date endTime = sdf.parse(yyyyMM + " " + reserveDateline[1]);
                                    return TimeConfig.buildTime(startTime,endTime,addTimeline);
                                } catch (ParseException e) {
                                    log.error("build time error", e);
                                }
                                return TimeConfig.buildTime(DateUtils.buildStartTimeDay(t), DateUtils.buildLastTimeDay(t), addTimeline);
                            }));
                }
            }
        } catch (Exception e) {
            log.error("getShopTimeConfig error", e);
        }
        return selectDate.stream()
                .collect(Collectors.toMap(t -> t, t -> TimeConfig.buildTime(DateUtils.buildStartTimeDay(t), DateUtils.buildLastTimeDay(t), 0)));
    }

    private boolean inDate(List<String[]> disableDatelines, Long t) {
        if (CollectionUtils.isNotEmpty(disableDatelines)) {
            for (String[] disableDateline : disableDatelines){
                if (disableDateline != null && disableDateline.length >= 2){
                    if(t >= Long.parseLong(disableDateline[0]) && t <= Long.parseLong(disableDateline[1])){
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private String getYm(Long t) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(DateUtils.getDateByMill(t));
    }



}
