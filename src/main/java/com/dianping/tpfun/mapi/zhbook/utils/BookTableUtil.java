package com.dianping.tpfun.mapi.zhbook.utils;

import com.dianping.lion.client.Lion;
import com.dianping.tpfun.mapi.utils.LionConfigUtils;
import com.dianping.tpfun.product.api.common.utils.JsonJsonUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON>ghua on 18/3/24.
 */
public class BookTableUtil {

    private static Logger LOGGER = LoggerFactory.getLogger(BookTableUtil.class);

    /**
     * 获取spuTyp与综合预订默认展示日期的大小的对应关系
     *
     * @return
     */
    public static Map<Integer, Integer> getSpuDefaultDisplayDateSizeMap() {
        String spuInitLoadSizeLionConfig = Lion.get("tpfun-mapi-web.zhbook.sputype.defaultDisplayDateSize");
        if (StringUtils.isBlank(spuInitLoadSizeLionConfig)) {
            LOGGER.error("[BookTableUtil], error in getSpuDefaultDisplayDateSizeMap, lion tpfun-mapi-web.zhbook.sputype.defaultDisplayDateSize not configured");
            return Collections.emptyMap();
        }
        Map<Integer, Integer> result = new HashMap<Integer, Integer>();
        try {
            Map<String, Object> map = JsonJsonUtils.fromStrToMap(spuInitLoadSizeLionConfig);
            if (map == null) {
                return Collections.emptyMap();
            }
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                result.put(Integer.valueOf(StringUtils.trim(entry.getKey())), (Integer) entry.getValue());
            }
            return result;
        } catch (IOException e) {
            LOGGER.error("[BookTableUtil], error in getSpuDefaultDisplayDateSizeMap", e);
            return Collections.emptyMap();
        }
    }

    public static Integer getDefaultDisplayDateSize(Integer spuType) {
        Map<Integer, Integer> defaultDisplayDateSizeMap = getSpuDefaultDisplayDateSizeMap();
        if (MapUtils.isEmpty(defaultDisplayDateSizeMap) || defaultDisplayDateSizeMap.get(spuType) == null) {
            return 0;
        }
        return defaultDisplayDateSizeMap.get(spuType);
    }
}
