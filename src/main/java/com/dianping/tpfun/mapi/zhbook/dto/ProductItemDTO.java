package com.dianping.tpfun.mapi.zhbook.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class ProductItemDTO implements Serializable{

    /**
     * skuid
     */
    private int productItemId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 数量
     */
    private int quantity;

    /**
     * 价格版本
     */
    private String priceNo;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 总价
     */
    private BigDecimal price;

    public int getProductItemId() {
        return productItemId;
    }

    public void setProductItemId(int productItemId) {
        this.productItemId = productItemId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public String getPriceNo() {
        return priceNo;
    }

    public void setPriceNo(String priceNo) {
        this.priceNo = priceNo;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getPrice() { return price; }

    public void setPrice(BigDecimal price) { this.price = price; }
}
