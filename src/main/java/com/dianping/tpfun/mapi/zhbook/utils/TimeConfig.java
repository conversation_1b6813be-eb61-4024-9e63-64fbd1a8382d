package com.dianping.tpfun.mapi.zhbook.utils;

import lombok.Data;
import org.apache.commons.lang.time.DateUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: huqi
 * @Date: 2021/3/2 5:47 下午
 */
@Data
public class TimeConfig {
    Date startTime;
    Date endTime;
    //分隔后的时间
    List<Long> list = new ArrayList<>();
    //间隔一个小时
    private static long unit = 1000 * 60 * 60;

    boolean dateDisable = false;


    public static TimeConfig buildTime(Date startTime, Date endTime, Integer addTimeline) {
        TimeConfig timeConfig = new TimeConfig();
        if (endTime.compareTo(startTime) <= 0) {
            endTime = DateUtils.addDays(endTime, 1);
        }
        Date nowDate = new Date();
        timeConfig.setStartTime(startTime);
        timeConfig.setEndTime(endTime);
        if (addTimeline == null){
            addTimeline = 0;
        }
        if (isTwoDaysIn(startTime,endTime,nowDate) && (nowDate.getTime() + ( addTimeline * 60 * 1000 )) > endTime.getTime()) {
            return TimeConfig.buildDisable();
        }
        long now = System.currentTimeMillis() + ( addTimeline * 60 * 1000 );
        for (long i = startTime.getTime(); i < endTime.getTime(); i =  i + unit){
            if (now < i) {
                timeConfig.getList().add(i);
            }
        }
        return timeConfig;
    }

    private static boolean isTwoDaysIn(Date startTime, Date endTime, Date nowDate) {
        return DateUtils.isSameDay(startTime,nowDate) || DateUtils.isSameDay(endTime,nowDate);
    }

    public static TimeConfig buildDisable() {
        TimeConfig timeConfig = new TimeConfig();
        timeConfig.setDateDisable(true);
        return timeConfig;
    }
}
