package com.dianping.tpfun.mapi.zhbook.widget;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.account.MeituanUserService;
import com.dianping.account.dto.MeituanUserInfoDTO;
import com.dianping.deal.common.builder.RichTextBuilder;
import com.dianping.lion.client.Lion;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.dianping.poi.areacommon.AreaCommonService;
import com.dianping.tpfun.mapi.CatMonitorKey;
import com.dianping.tpfun.mapi.WidgetContext;
import com.dianping.tpfun.mapi.dto.PromoInstructionDTO;
import com.dianping.tpfun.mapi.dto.StrandardCityPriceDTO;
import com.dianping.tpfun.mapi.shop.widget.shoplist.utils.NumberUtils;
import com.dianping.tpfun.mapi.zhbook.utils.CatMetricUtils;
import com.dianping.tpfun.product.api.sku.aggregate.ProductStaticDetailBySpuService;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductDTO;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetStandardProductPriceRequest;
import com.dianping.tpfun.product.api.sku.common.enums.FunChannel;
import com.google.common.collect.Lists;
import com.meituan.data.uss.thrift.USSRequest;
import com.meituan.data.uss.thrift.USSResponse;
import com.meituan.data.uss.thrift.USSServiceV2;
import com.meituan.data.uss.thrift.USSTag;
import com.meituan.travel.mbox.order.annotation.response.UserCountResp;
import com.meituan.travel.mbox.order.annotation.service.IOrderService;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by ljh on 18/9/4.
 * 静态涉及模块参考 https://km.sankuai.com/page/69802184#id-ProductStaticDetailService
 */
@Component
public class StandardProductPriceWidget {

    private static final Logger logger = LoggerFactory.getLogger(StandardProductPriceWidget.class);

    @Reference(url = "http://service.dianping.com/tpfunService/productstaticdetailbyspuservice_1.0.0")
    private ProductStaticDetailBySpuService productStaticDetailService;
    @Reference(url = "http://service.dianping.com/com.dianping.poi.areacommon.AreaCommonService_1.0.0")
    private AreaCommonService areaCommonService;
    @Resource
    private USSServiceV2.Iface ussService;
    @Resource
    private IOrderService iOrderService;
    @Resource
    private UserMergeQueryService.Iface userMergeQueryService;

    @Resource
    private MeituanUserService meituanUserService;

    private static String red = "#ff6633";
    private static String black = "#111111";

    public Object getStandardProductPrice(WidgetContext context) {
        String spuId = String.valueOf(context.getExtras().get("spuId"));
        String channelString = String.valueOf(context.getExtras().get("channel"));
        String platformString = String.valueOf(context.getExtras().get("platform"));
        String mobileNO = String.valueOf(context.getExtras().get("mobileNO"));
        String dpId = String.valueOf(context.getExtras().get("dpId"));
        FunChannel funChannel = FunChannel.DP;
        if (StringUtils.isNotEmpty(channelString)) {
            funChannel = FunChannel.descOf(channelString);
        }

        GetStandardProductPriceRequest request = new GetStandardProductPriceRequest();
        request.setCityId(context.getCityId());
        request.setSpuId(Long.parseLong(spuId));
        request.setMobileNO(mobileNO);
        request.setDpId(dpId);
        Long userId = context.getUserId();
        request.setUserId(userId);
        request.setVersion(context.getVersion());

        if (funChannel == FunChannel.MT) {

            request.setMtCityId(context.getCityId());
            request.setCityId(areaCommonService.getDpCityByMtCity(context.getCityId()));
            request.setClientType(ProductType.mt_generalTrade.getValue());
            request.setPlatform(getCode("mt_"+platformString+"_native",PayPlatform.mt_iphone_native.getCode()));
            boolean switchEnable = Lion.getBooleanValue("tpfun-mapi-web.standard.userid.switch", false);

            if (userId > 0 && switchEnable && !context.isUserIdFromParam()) {
                MeituanUserInfoDTO userInfoDTO = meituanUserService.loadMTUserByDPUidLocal(userId);

                if (userInfoDTO != null && userInfoDTO.getMTUid() != null && userInfoDTO.getMTUid() > 0) {
                    userId = userInfoDTO.getMTUid();
                    request.setUserId(userId);
                }
            }

        } else {
            request.setClientType(ProductType.generalTrade.getValue());
            request.setPlatform(getCode("dp_"+platformString+"_native",PayPlatform.dp_iphone_native.getCode()));
        }

        StandardProductDTO standardProductDTO =  productStaticDetailService.getSpuPrice(request);
        StrandardCityPriceDTO strandardCityPriceDTO = new StrandardCityPriceDTO();

        if (standardProductDTO != null) {
            strandardCityPriceDTO.setMinPrice(getPrice(standardProductDTO.getMinPrice()));
            strandardCityPriceDTO.setPrice(getPrice(standardProductDTO.getPrice()));
            strandardCityPriceDTO.setMarketPrice(getPrice(standardProductDTO.getMarketPrice()));
            strandardCityPriceDTO.setPriceDesc(buildTextItemList(standardProductDTO.getPromoDisplayDesc(), standardProductDTO.getReducePrice()));
            strandardCityPriceDTO.setPriceTag(buildTextItem(standardProductDTO.getPromoDisplayTag(), red));
            strandardCityPriceDTO.setProductId(standardProductDTO.getProductId());
            strandardCityPriceDTO.setSkuId(standardProductDTO.getSkuIdL());
            strandardCityPriceDTO.setPromoInstruction(buildPromInstruction(standardProductDTO));
        }
        
        strandardCityPriceDTO.setFreshCustomer(queryBabyFreshCustomer(spuId, userId, funChannel));
        strandardCityPriceDTO.setCouldBuy(Lion.getBooleanValue("tpfun-mapi-web.spu.couldbuy", true));

        CatMetricUtils.addMetricForCount(CatMonitorKey.BasicView.dzbook_comproductdetailbyspuprice, funChannel.desc, String.valueOf(context.getExtras().get("clientType")));
        return strandardCityPriceDTO;
    }

    private PromoInstructionDTO buildPromInstruction(StandardProductDTO standardProductDTO) {
        if (standardProductDTO.getReducePrice() == null || CollectionUtils.isEmpty(standardProductDTO.getPromoItems())) {
            return null;
        }
        PromoInstructionDTO promoInstructionDTO = new PromoInstructionDTO();
        promoInstructionDTO.setPromoType("共优惠：");
        promoInstructionDTO.setPromoPriceText("-￥" + standardProductDTO.getReducePrice().toPlainString());
        promoInstructionDTO.setPromoItems(standardProductDTO.getPromoItems().stream().map(promo -> {
            PromoInstructionDTO promoIns = new PromoInstructionDTO();
            promoIns.setPromoPriceText(promo.getOrDefault("promoPriceText", ""));
            promoIns.setPromoType(promo.getOrDefault("promoType", ""));
            promoIns.setPromoDesc(promo.getOrDefault("promoDesc", ""));
            return promoIns;
        }).collect(Collectors.toList()));
        return promoInstructionDTO;
    }

    public boolean queryBabyFreshCustomer(String spuId, long userId, FunChannel funChannel) {
        return judgeByUss(userId, funChannel) && judgeBySpuOrder(spuId, userId, funChannel);
    }

    private boolean judgeByUss(long userId, FunChannel funChannel) {
        if (userId<=0) {
            return false;
        }
        try {
            //入参设置详见文档：https://km.sankuai.com/page/70934365
            USSRequest request = new USSRequest();
            request.setCatalog(0);//查询订单支付数据
            USSTag ussTag = new USSTag();
            request.addToTags(ussTag);
            if (funChannel == FunChannel.DP) {
                request.setDpUserId(String.valueOf(userId));
                ussTag.setTagName("dp_cat0_qinzi_new");
                ussTag.setScope(4);//点评userId
            } else {
                request.setMtUserId(String.valueOf(userId));
                ussTag.setTagName("mt_cat0_qinzi_new");
                ussTag.setScope(2);//美团userId
            }
            USSResponse resp = ussService.getUSSResponse(request);
            if (resp.getStatusCode() == 0) {
                JSONObject jsonObject = JSON.parseObject(resp.getValue());
                JSONObject data;
                if (funChannel == FunChannel.DP) {
                    data = jsonObject.getJSONObject("dp_cat0_qinzi_new");
                } else {
                    data = jsonObject.getJSONObject("mt_cat0_qinzi_new");
                }
                int cnt = data.getInteger("cnt");
                if (cnt>0) {
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error("ussService error, userId:{}, funChannel:{}", userId, funChannel.desc, e);
            return false;
        }
        return true;
    }

    private boolean judgeBySpuOrder(String spuId, long userId, FunChannel funChannel) {
        try {
            if (userId <= 0) {
                return false;
            }
            Map<String, String> lionConfig = Lion.getMap("tpfun-mapi-web.spu.magicboxid", String.class, new HashMap<String, String>());
            String magicBoxIDConfigStr = lionConfig.get(spuId);
            if (StringUtils.isEmpty(magicBoxIDConfigStr)) {
                return true;
            }
            String[] magicBoxIDList = magicBoxIDConfigStr.split(",");
            for (String magicBoxID : magicBoxIDList) {
                if (funChannel == FunChannel.DP) {
                    try {
                        BindRelationResp bindRelationResp = userMergeQueryService.getOrCreateVirtualBindByDpUserId(userId);
                        if (bindRelationResp != null && bindRelationResp.isSuccess() && bindRelationResp.getData() != null && bindRelationResp.getData().getMtUserId() != null) {
                            userId = bindRelationResp.getData().getMtUserId().getId();
                        } else {
                            logger.error("[judgeBySpuOrder] failed getOrCreateVirtualBindByDpUserId with dpUserId is {}", userId);
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("[judgeBySpuOrder] error getOrCreateVirtualBindByDpUserId with dpUserId is {}", userId, e);
                        return false;
                    }
                }
                try {
                    UserCountResp userCountResp = iOrderService.queryUserBuyCount(userId, Long.parseLong(magicBoxID));
                    if (userCountResp == null || userCountResp.getCount() > 0) {
                        return false;
                    }
                } catch (Exception e) {
                    logger.error("[judgeBySpuOrder] error queryUserBuyCount, userId:{}, magicBoxID:{}", userId, magicBoxID);
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error("[judgeBySpuOrder] error queryUserBuyCount, userId:{}, spuId:{}", userId, spuId);
            return false;
        }
        return true;
    }

    private BigDecimal getPrice(BigDecimal price) {
        try {
            if (price == null) {
                return null;
            }
            String minPrice = price
                    .setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
            return new BigDecimal(minPrice);
        } catch (Exception e) {
            logger.error("getMinPrice err {}", price, e);
            return price;
        }
    }

    private int getCode(String platformString,int defaultCode) {
        for (PayPlatform payPlatform : PayPlatform.values()) {
            if (payPlatform.getMessage().contains(platformString)) {
                return payPlatform.getCode();
            }
        }
        return defaultCode;
    }

    private RichTextBuilder.TextItem buildTextItem(String value, String color) {
        if (StringUtils.isNotBlank(value)) {
            RichTextBuilder.TextItem textItem = new RichTextBuilder.TextItem();
            textItem.setText(value);
            textItem.setTextcolor(color);
            return textItem;
        }
        return null;
    }

    private List<RichTextBuilder.TextItem> buildTextItemList(String value, BigDecimal price) {
        if (StringUtils.isEmpty(value) || price == null) {
            return null;
        }
        String split = "减" +  NumberUtils.trimDecimalZeros(price.toString()) + "元";
        String[] list = value.split(split);
        if (list == null) {
            return null;
        }
        List<RichTextBuilder.TextItem> result
                = Lists.newArrayList(buildTextItem(list[0], black), buildTextItem(split, red));
        if (list.length > 1) {
            result.add(buildTextItem(list[1], black));
        }
        return result;
    }

}
