package com.dianping.tpfun.mapi.framework;

import com.dianping.mobile.base.datatypes.enums.ClientType;
import com.dianping.mobile.base.datatypes.enums.Platform;
import com.dianping.mobile.base.datatypes.enums.Product;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileContext;
import com.dianping.mobile.framework.io.ResponseContent;
import com.dianping.tpfun.mapi.Controller;
import com.dianping.tpfun.mapi.framework.config.Configuration;
import com.dianping.tpfun.mapi.framework.interceptor.APIInterceptor;
import com.dianping.tpfun.mapi.skulist.data.enums.GcSkuResultListDo;
import com.dianping.tpfun.mapi.utils.SpringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 返回jsonp数据
 */
public class NewJsonpServlet extends HttpServlet {

    private static org.slf4j.Logger LOG = org.slf4j.LoggerFactory.getLogger(NewJsonpServlet.class);

    private static final String JSONP_CONFIGURATION = "configurationForJsonp";

    private static WebApplicationContext context;

    private static Configuration configuration;

    private static MyMobileContextBuilder defaultMobileContextBuilder = SpringUtils.getBean("ktvMobileContextBuilder");

    private static HttpUserIDParser httpUserIDParser = SpringUtils.getBean("httpUserIDParser");

    public void init() {
        LOG.info("JsonpServlet servlet is loading");
        context = WebApplicationContextUtils.getRequiredWebApplicationContext(getServletContext());
        //初始化配置
        initConfiguration();
    }

    private void initConfiguration() {
        configuration = (Configuration) context.getBean(JSONP_CONFIGURATION);
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        handleRequest(request, response, false);

    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        handleRequest(request, response, true);

    }

    protected void handleRequest(HttpServletRequest request, HttpServletResponse httpResponse, boolean isPost)
            throws ServletException, IOException {

        int status = 200;
        Object result;

        Controller controller = null;

        try {
            request.setCharacterEncoding("UTF-8");
            controller = parseController(request);// -----------parse controller
            if (controller == null) {
                return;
            }
            MobileContext mobileContext = buildJsonContext(request, httpResponse, getServletContext(), isPost);
            controller.setMobileContext(mobileContext);
            //请求前处理
            preHandler(controller, mobileContext);
            result = controller.doRequest(); // -----handle business logic
        } catch (Exception e) {
            status = 500;
            result = null;
            LOG.error("[handleRequest error]", e);
        }
        responseJsonp(result, httpResponse, controller);

    }

    private void preHandler(Controller controller, IMobileContext iMobileContext) throws Exception {
        if (CollectionUtils.isEmpty(configuration.getPreInterceptors())) {
            return;
        }
        for (APIInterceptor apiInterceptor : configuration.getPreInterceptors()) {
            apiInterceptor.preHandler(iMobileContext, controller);
        }
    }

    private MobileContext buildJsonContext(HttpServletRequest request, HttpServletResponse response, ServletContext servletContext, boolean isPost) {
        MobileContext mobileContext = new MobileContext(request, response);
        //先从request中获取userId，defaultMobileContextBuilder.parseParamters要在这之后执行，否则inputstream会被消费掉
        httpUserIDParser.setUserId(request, response, mobileContext);
        try {
            request.setCharacterEncoding(ResponseContent.ENCODING);
            /**
             * hints：要获得http里head的参数可采用，HeadParamUtil工具类直接获得
             */
            defaultMobileContextBuilder.parseHeader(mobileContext, request);
            defaultMobileContextBuilder.parseRequestIp(mobileContext, request);
            defaultMobileContextBuilder.parseParamters(mobileContext, servletContext, request, isPost, false, false);
            defaultMobileContextBuilder.parseUserAgent(mobileContext);
        } catch (Exception e) {
            LOG.error("build mobile context failed ! ", e);
        }
        mobileContext.setClient(new ClientType(Platform.Unknow, Product.API));

        return mobileContext;
    }

    private Controller parseController(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String contextPath = request.getContextPath();
        Controller controller = null;
        if (uri.length() <= contextPath.length() + 1) {
            LOG.error("request URI is wrong:" + uri);
            return null;
        }
        String controllerKey = uri.substring(contextPath.length() + 1).toLowerCase();
        controllerKey = StringUtils.replace(controllerKey, "/", "_");
        if (StringUtils.contains(controllerKey, ".jsonp")) {
            controllerKey = StringUtils.replace(controllerKey, ".jsonp", ".fn");

        }
        try {
            controller = (Controller) context.getBean(controllerKey);
        } catch (Exception e) {
            LOG.error("can't find controller:" + controllerKey, e);
            return null;
        }
        if (controller != null) {// spring will make this true
            controller.setControllerKey(controllerKey);
            controller.setJsonRequest(true);
        }

        return controller;
    }


    private void responseJsonp(Object result, HttpServletResponse response, Controller controller)
            throws IOException {
        String callback = "";
        if (controller.getMobileContext() != null) {
            callback = controller.getMobileContext().getParameter("callback");
        }
        try {
            //trick做法,对于这种类型(GcSkuResultListDo),用com开头包jackson的序列化方法,
            //否则com.dianping.scrum.shell.io.datatypes.ResultList会序列化出问题
            if (result instanceof GcSkuResultListDo) {
                NewStandardJsonpResponder.sendExposedSpecial(response, result, callback);
                return;
            }

            NewStandardJsonpResponder.sendExposed(response, result, callback);
        } catch (Exception e) {
            LOG.error("error serializing as jsonp", e);
        }
    }
}
