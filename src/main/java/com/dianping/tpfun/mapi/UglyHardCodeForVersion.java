package com.dianping.tpfun.mapi;

import com.dianping.mobile.base.datatypes.utils.VersionUtil;
import com.dianping.tpfun.mapi.dto.ktv.KTVBookTable;
import com.dianping.tpfun.mapi.dto.ktv.booktable.KTVBookDate;
import com.dianping.tpfun.mapi.dto.ktv.booktable.KTVBookRoom;
import com.dianping.tpfun.mapi.dto.ktv.booktable.KTVRoomType;
import com.dianping.tpfun.mapi.dto.shop.KTVShopFeatureTagDO;
import com.dianping.tpfun.mapi.dto.shop.KTVShopInfoWidgetDO;
import com.dianping.tpfun.mapi.zhbook.booktable.BookDate;
import com.dianping.tpfun.mapi.zhbook.booktable.BookTableItem;
import com.dianping.tpfun.mapi.zhbook.booktable.ZHBookTable;
import com.dianping.tpfun.mapi.zhbook.booktable.ZHBookTableUnit;
import com.dianping.tpfun.product.api.common.KTVChannel;
import com.dianping.tpfun.product.api.common.KTVClientContext;
import com.dianping.tpfun.product.api.sku.common.enums.FunClientContext;
import com.dianping.tpfun.product.api.sku.common.enums.FunPlatform;
import com.dianping.tpfun.product.api.tuangou.dto.KTVDealTable;
import com.dianping.tpfun.product.api.tuangou.dto.dealtable.DrinkDealItem;
import com.dianping.tpfun.product.api.tuangou.dto.dealtable.DrinkTable;
import com.dianping.tpfun.product.api.tuangou.dto.dealtable.KTVSelectItem;
import com.dianping.tpfun.product.api.tuangou.dto.dealtable.TrieCell;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 所有涉及版本适配的逻辑集中营
 *
 * <AUTHOR> , 2015/3/28
 */
@Component
public class UglyHardCodeForVersion {

    public static class Shop {
        /**
         * 适用App：ios
         * 适用Version:美团7.5以前的版本兼容
         * BUG: 兼容客户端旧版本，ios10的"生日服务"字数过长、换行显示，紧走了下面的富文本。
         *
         * <AUTHOR> 2016/11/21
         */
        public static Object handle_before_MT_7_5_ChangeBirthdayDisplayName(Object object, Map<String, String> clientContext) {
            // 数据对象及版本过滤
            if (object == null || !(object instanceof KTVShopInfoWidgetDO)) {
                return object;
            }
            KTVChannel ktvChannel = KTVChannel.descOf(clientContext.get(KTVClientContext.CHANNEL));
            String version = clientContext.get(KTVClientContext.VERSION);
            boolean beforeMt75Version = ktvChannel.code == KTVChannel.MT.code && VersionUtil.compare(version, "7.5.0") <= 0;
            if (!beforeMt75Version) {
                return object;
            }
            // 将美团7.5以前的版本换成"过生日"
            KTVShopInfoWidgetDO ktvShopInfoWidgetDO = (KTVShopInfoWidgetDO) object;
            if (CollectionUtils.isEmpty(ktvShopInfoWidgetDO.getFeatureTags())) {
                return object;
            }
            for (KTVShopFeatureTagDO ktvShopFeatureTagDO : ktvShopInfoWidgetDO.getFeatureTags()) {
                if (StringUtils.equalsIgnoreCase("生日服务", ktvShopFeatureTagDO.getName())) {
                    ktvShopFeatureTagDO.setName("过生日");
                }
            }
            return object;
        }

        /**
         * 适用app：android
         * 适用version：美团9.13.800 点评 10.11
         *
         * @return
         */

        public static Object handle_Android_BookTableDisplayBug(Object object, Map<String, String> clientContext) {
            if (object == null || !(object instanceof ZHBookTable)) {
                return object;
            }

            ZHBookTable bookTable = (ZHBookTable) object;
            String versionStr = clientContext.get(FunClientContext.VERSION);
            KTVChannel ktvChannel = KTVChannel.descOf(clientContext.get(KTVClientContext.CHANNEL));
            String platformStr = clientContext.get(FunClientContext.PLATFORM);
            if (platformStr.equals("android")) {
                if ((ktvChannel.code == KTVChannel.MT.code && VersionUtil.compare(versionStr, "9.13.800") >= 0) ||
                        (ktvChannel.code == KTVChannel.DP.code && VersionUtil.compare(versionStr, "10.11") >= 0)) {
                    if (bookTable.getBookDates() == null) {
                        return bookTable;
                    }
                    for (BookDate bookDate : bookTable.getBookDates()) {
                        if (bookDate.getBookTableItems() == null) {
                            continue;
                        }
                        for (BookTableItem bookTableItem : bookDate.getBookTableItems()) {
                            if (bookTableItem.getBookTableUnits() == null) {
                                continue;
                            }
                            for (ZHBookTableUnit zhBookTableUnit : bookTableItem.getBookTableUnits()) {
                                if (zhBookTableUnit.getSubName() == null) {
                                    zhBookTableUnit.setSubName(" ");
                                }
                            }
                        }
                    }
                }
                return bookTable;
            }

            return object;
        }

        /**
         * 前端修美团变黄bug
         * 影响范围： iOS 美团 10.2.200 版本
         * 紧急修复方案：mtshopdetailktvbooktable.fn 接口，每个 SKU 的返回中， status 字段对 iOS 美团App >=10.2.200, < 10.2.400 版本的请求，固定返回 1（可订状态）
         * 修复后，仅在受影响版本上，满房情况下按钮也会是橙色，按钮可点击并展示弹窗，进入下单页后会提示没有库存不能下单了
         *
         * @param object
         * @param clientContext
         * @param os
         * @return
         */
        public static Object handle_MT_IOS_KTVBookTableColor(Object object, Map<String, String> clientContext, String os) {
            if (object == null || !(object instanceof KTVBookTable)) {
                return object;
            }

            KTVBookTable bookTable = (KTVBookTable) object;
            String versionStr = clientContext.get(FunClientContext.VERSION);
            KTVChannel ktvChannel = KTVChannel.descOf(clientContext.get(KTVClientContext.CHANNEL));
            String platformStr = clientContext.get(FunClientContext.PLATFORM);
            String osVersion = getOsVersion(os);

            // ios mt
            if (platformStr.equals(FunPlatform.IPHONE.desc)) {
                if ((ktvChannel.code == KTVChannel.MT.code && (VersionUtil.compare(versionStr, "10.2.200") >= 0) && (VersionUtil.compare(versionStr, "10.2.400") < 0))) {
                    setKtvStatus(bookTable, 1);
                }
                if (ktvChannel.code == KTVChannel.MT.code && StringUtils.isNotBlank(osVersion) && VersionUtil.compare(osVersion, "13.0.0") >= 0 && VersionUtil.compare(versionStr, "10.0.200") < 0) {
                    setKtvStatus(bookTable, 2);
                }
                return bookTable;
            }
            return object;
        }
    }

    private static String getOsVersion(String os) {
        if (StringUtils.isBlank(os)) {
            return StringUtils.EMPTY;
        }
        String[] osArray = os.split(" ");
        if (osArray == null || osArray.length != 2 || StringUtils.isBlank(osArray[1])) {
            return StringUtils.EMPTY;
        }
        return osArray[1];
    }

    private static void setKtvStatus(KTVBookTable bookTable, int status) {
        if (!bookTable.isShowable()) {
            return;
        }
        if (CollectionUtils.isEmpty(bookTable.getKtvBookDates())) {
            return;
        }
        for (KTVBookDate ktvBookDate : bookTable.getKtvBookDates()) {
            List<KTVRoomType> ktvRoomTypes = ktvBookDate.getKtvRoomTypes();
            if (CollectionUtils.isEmpty(ktvRoomTypes)) {
                continue;
            }
            for (KTVRoomType roomType : ktvRoomTypes) {
                List<KTVBookRoom> ktvBookRooms = roomType.getKtvBookRooms();
                if (CollectionUtils.isEmpty(ktvBookRooms)) {
                    continue;
                }
                for (KTVBookRoom room : ktvBookRooms) {
                    room.setStatus(status);
                }
            }
        }
    }

    public static class Tuan {

//        /**
//         * 适用App：Android、ios
//         * 适用Version:7.2.0以前的版本
//         * BUG：当没有ktv结构化表格时，返回null、根据status=400客户端走旧逻辑，但增加“业务失败统计”；<br/>
//         * 7.2.0+的版本将根据KTVTableWidget里的Showable决定走新旧逻辑，而非跟据status=400触发旧逻辑
//         *
//         * @param version
//         * @param clientType
//         * @param ktvTable
//         * <AUTHOR> 2015/4/13
//         */
//        public static Object handle_before_7_2_0_IOS_Android_DisplayKTVTableInOldVersionBug(String version, ClientType clientType, Object ktvTable) {
//            if (ktvTable == null || version == null || !(ktvTable instanceof KTVDealTable)) {
//                return ktvTable;
//            }
//            KTVDealTable ktvTable2 = (KTVDealTable) ktvTable;
//            if (VersionUtil.compare(version, "7.2.0") < 0 && !ktvTable2.isShowable()) {
//                return SimpleMsg.NULL_MSG;
//            }
//            return ktvTable;
//        }

        /**
         * 适用App：Android、ios
         * 适用Version:点评9.0.0以前、美团7.3以前的版本兼容
         * BUG: 兼容客户端旧版本，将为空的日期移除
         *
         * <AUTHOR> 2016/8/10
         */
        public static Object handle_before_DP_9_0_MT_7_3_RemoveEmptyDateSelectItems(Object object, Map<String, String> clientContext) {
            // 数据对象及版本过滤
            if (object == null || !(object instanceof KTVDealTable)) {
                return object;
            }
            KTVDealTable ktvTable = (KTVDealTable) object;
            KTVChannel ktvChannel = KTVChannel.descOf(clientContext.get(KTVClientContext.CHANNEL));
            String version = clientContext.get(KTVClientContext.VERSION);
            boolean beforeDp9Version = ktvChannel.code == KTVChannel.DP.code && VersionUtil.compare(version, "9.0.0") < 0;
            boolean beforeMt73Version = ktvChannel.code == KTVChannel.MT.code && VersionUtil.compare(version, "7.3.0") < 0;
            if (!beforeDp9Version && !beforeMt73Version) {
                return object;
            }

            // 移除空格
            if (CollectionUtils.isEmpty(ktvTable.getKtvDates())) {
                return ktvTable;
            }
            List<KTVSelectItem> ktvDates = Lists.newArrayList();
            for (KTVSelectItem ktvSelectItem : ktvTable.getKtvDates()) {
                boolean hasContent = ktvSelectItem != null && CollectionUtils.isNotEmpty(ktvSelectItem.getChildItems());
                if (hasContent) {
                    ktvDates.add(ktvSelectItem);
                }
            }
            ktvTable.setKtvDates(ktvDates);
            ktvTable.setSelectedIndex(CollectionUtils.isEmpty(ktvDates) ? -1 : 0);

            return ktvTable;
        }

        /**
         * 适用App：Android、ios
         * 适用Version:点评<=9.0.4、美团<=7.4的版本兼容
         * BUG: 兼容客户端旧版本、合所有酒水套餐至一个表格
         *
         * <AUTHOR> 2016/8/10
         */
        public static Object handle_before_DP_9_0_4_MT_7_4_KTVDealTableMergeAllDrinkTables(Object ktvDealTable, Map<String, String> clientContext) {
            if (ktvDealTable == null || !(ktvDealTable instanceof KTVDealTable)) {
                return ktvDealTable;
            }
            KTVDealTable ktvTable = (KTVDealTable) ktvDealTable;
            if (CollectionUtils.isEmpty(ktvTable.getDrinkDeals())) {
                return ktvDealTable;
            }
            KTVChannel ktvChannel = KTVChannel.descOf(clientContext.get(KTVClientContext.CHANNEL));
            String version = clientContext.get(KTVClientContext.VERSION);
            boolean beforeDp9Version = ktvChannel.code == KTVChannel.DP.code && VersionUtil.compare(version, "9.0.4") < 0;
            boolean beforeMt73Version = ktvChannel.code == KTVChannel.MT.code && VersionUtil.compare(version, "7.4.0") < 0;
            if (!beforeDp9Version && !beforeMt73Version) {
                return ktvDealTable;
            }
            if (CollectionUtils.isEmpty(ktvTable.getDrinkDeals())) {
                return ktvDealTable;
            }
            // 将所有DrinkTables合并成一个总DrinkTable
            List<DrinkTable> drinkTables = Lists.newArrayList();
            for (DrinkTable drinkDealGroup : ktvTable.getDrinkDeals()) {
                DrinkTable drinkTable = new DrinkTable();
                drinkTable.setTitle(drinkDealGroup.getTitle());
                List<TrieCell> cells = Lists.newArrayList();
                if (CollectionUtils.isEmpty(drinkDealGroup.getDrinkDealItems())) {
                    continue;
                }
                for (DrinkDealItem drinkDealItem : drinkDealGroup.getDrinkDealItems()) {
                    if (StringUtils.isNotBlank(drinkDealItem.getTitle())) {
                        TrieCell titleCell = new TrieCell();
                        titleCell.setFirst(drinkDealItem.getTitle());
                        titleCell.setSecond("");
                        titleCell.setThird("");
                        cells.add(titleCell);
                    }
                    cells.addAll(drinkDealItem.getDrinkDeal());
                }
                drinkTable.setDrinkDeal(cells);
                drinkTable.setDrinkDealItems(null);
                drinkTables.add(drinkTable);
            }
            ktvTable.setDrinkDeals(drinkTables);

            return ktvTable;
        }

    }

}