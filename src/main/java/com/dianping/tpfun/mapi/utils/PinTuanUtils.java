package com.dianping.tpfun.mapi.utils;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * User: jason.jiang
 * Date: 2020/2/18
 * Time: 下午4:44
 */
public class PinTuanUtils {

    /**
     * 是否是泛商品拼团
     *
     * @param spuType
     * @return
     */
    public static boolean isFspPinTuan(Integer spuType) {
        List<Integer> pinRelationSpuTypes = Lion.getList("sku-aggregate-service.pinRelation.spuType", Integer.class, Lists.newArrayList(2000));
        return pinRelationSpuTypes.contains(spuType);
    }
}
