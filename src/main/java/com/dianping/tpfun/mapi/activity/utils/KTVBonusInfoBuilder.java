package com.dianping.tpfun.mapi.activity.utils;

import com.dianping.pay.promo.common.enums.UnifiedCouponGroupExpireType;
import com.dianping.tpfun.mapi.activity.dto.KTVBonusInfo;
import com.dianping.tpfun.mapi.activity.dto.KTVPrizeInfo;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class KTVBonusInfoBuilder {

    private static final org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(KTVBonusInfoBuilder.class);

    private List<UnifiedCouponGroupDTO> couponGroupDTOs;
    private String title;
    private String desc;
    private String tips;
    private static String limitFormat = "满%s元可用";
    private static String expireFormat = "%d天后过期";

    public KTVBonusInfo build() {
        boolean invalid = StringUtils.isBlank(title);
        if (invalid) {
            return null;
        }
        KTVBonusInfo ktvBonusInfo = new KTVBonusInfo();
        ktvBonusInfo.setTitle(getTitle());
        ktvBonusInfo.setDesc(getDesc());
        ktvBonusInfo.setTips(getTips());
        ktvBonusInfo.setKtvPrizeInfoList(transformToKTVPrizeInfo(getCouponGroupDTOs()));
        if (CollectionUtils.isNotEmpty(ktvBonusInfo.getKtvPrizeInfoList())) {
            ktvBonusInfo.setShowable(true);
        }
        return ktvBonusInfo;
    }

    public List<UnifiedCouponGroupDTO> getCouponGroupDTOs() {
        return couponGroupDTOs;
    }

    public KTVBonusInfoBuilder setCouponGroupDTOs(List<UnifiedCouponGroupDTO> couponGroupDTOs) {
        this.couponGroupDTOs = couponGroupDTOs;
        return this;
    }

    public String getDesc() {
        return desc;
    }

    public KTVBonusInfoBuilder setDesc(String desc) {
        this.desc = desc;
        return this;

    }

    public String getTips() {
        return tips;
    }

    public KTVBonusInfoBuilder setTips(String tips) {
        this.tips = tips;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public KTVBonusInfoBuilder setTitle(String title) {
        this.title = title;
        return this;
    }

    private List<KTVPrizeInfo> transformToKTVPrizeInfo(List<UnifiedCouponGroupDTO> couponGroupDTOs) {

        if (CollectionUtils.isEmpty(couponGroupDTOs)) {
            return Collections.emptyList();
        }
        List<KTVPrizeInfo> result = Lists.newLinkedList();
        DecimalFormat df = new DecimalFormat("#.00");

        for (UnifiedCouponGroupDTO couponGroupDTO2 : couponGroupDTOs) {

            try {
                KTVPrizeInfo ktvPrizeInfo = new KTVPrizeInfo();
                ktvPrizeInfo.setTitle(couponGroupDTO2.getDisplayTitle());
                ktvPrizeInfo.setDesc(couponGroupDTO2.getDisplayDesc());
                if (couponGroupDTO2.getPriceLimit() != null) {
                    if (couponGroupDTO2.getPriceLimit().compareTo(BigDecimal.valueOf(0.0)) == 0) {
                        ktvPrizeInfo.setLimit("无使用门槛");
                    } else {
                        ktvPrizeInfo.setLimit(String.format(limitFormat, df.format(couponGroupDTO2.getPriceLimit().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue())));
                    }
                } else {
                    ktvPrizeInfo.setLimit("");
                }

                int expireDay = 0;
                if (couponGroupDTO2.getExpireType() == UnifiedCouponGroupExpireType.FIXED.code) {
                    expireDay = DateDateUtils.daysBetween(new Date(), couponGroupDTO2.getEndTime());
                } else {
                    expireDay = couponGroupDTO2.getFloatDay();
                }

                if (expireDay > 0) {
                    ktvPrizeInfo.setExpireTime(String.format(expireFormat, expireDay));
                } else {
                    ktvPrizeInfo.setExpireTime("今日过期");
                }
                double faceValue = couponGroupDTO2.getDiscountAmount().doubleValue();
                ktvPrizeInfo.setFaceValue(String.valueOf((int) faceValue));
                result.add(ktvPrizeInfo);
            } catch (Exception e) {
                LOGGER.error("[ktv bonus] error in transformToKTVPrizeInfo",e);
            }
        }
        return result;
    }
}
