package com.dianping.tpfun.mapi.bizmanager.widget;


import com.dianping.ktv.bizmanager.api.domain.BizManagerShop;
import com.dianping.ktv.bizmanager.api.service.BizManagerRemoteService;
import com.dianping.ktv.shop.blacklist.KtvShopBlackListService;
import com.dianping.ktv.shop.blacklist.enums.BlackListType;
import com.dianping.tpfun.mapi.bizmanager.dto.KTVBizManagerSearchResultDO;
import com.dp.arts.client.SearchService;
import com.dp.arts.client.request.KeywordQuery;
import com.dp.arts.client.request.Request;
import com.dp.arts.client.request.SortItem;
import com.dp.arts.client.request.TermQuery;
import com.dp.arts.client.response.Record;
import com.dp.arts.client.response.Response;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * poi升级，已经无流量废弃了
 */
@Deprecated
@Component
public class KTVBizManagerSearchShopWidget {

    private static org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(KTVBizManagerSearchShopWidget.class);

    @Autowired
    @Qualifier("searchService")
    private SearchService searchService;

    @Resource
    private BizManagerRemoteService bizManagerRemoteService;

    @Resource
    private KtvShopBlackListService ktvShopBlackListService;

    private static final String[] SHOP_NEED_PROPERTORY = new String[]{
            "shopname", "branchname", "shopid", "avgprice", "shoppower", "crossroad", "matchtext", "address"
    };

    private static final int SEARCH_PAGE_SIZE = 20;

    public List<KTVBizManagerSearchResultDO> searchShopByName(String shopName,
                                                              int cityId,
                                                              int pageNo,
                                                              int pageSize,
                                                              int bizManagerId) {
        Request request = this.buildSearchRequest(shopName, cityId, pageNo, pageSize);
        Response response = searchService.search(request);
        if (null == response || CollectionUtils.isEmpty(response.getRecordList())) {
            return Collections.EMPTY_LIST;
        }

        List<Record> recordList = response.getRecordList();
        List<KTVBizManagerSearchResultDO> resultDOList = Lists.newArrayList();
        for (Record record : recordList) {
            resultDOList.add(this.buildSearchResult(record));
        }

        List<BizManagerShop> bizManagerShopList = bizManagerRemoteService.getBizManagerCooperateShops(bizManagerId);
        if (CollectionUtils.isNotEmpty(bizManagerShopList)) {
            Map<Integer, BizManagerShop> shopIdMap = Maps.newHashMap();
            for (BizManagerShop bizManagerShop : bizManagerShopList) {
                shopIdMap.put(bizManagerShop.getShopId(), bizManagerShop);
            }

            List<KTVBizManagerSearchResultDO> dupList = Lists.newArrayList();
            for (KTVBizManagerSearchResultDO resultDO : resultDOList) {
                if (null != shopIdMap.get(resultDO.getShopId())) {
                    dupList.add(resultDO);
                }
            }
            resultDOList.removeAll(dupList);
        }
        fillShopBindType(resultDOList);
        return resultDOList;
    }

    private KTVBizManagerSearchResultDO buildSearchResult(Record record) {
        KTVBizManagerSearchResultDO resultDO = new KTVBizManagerSearchResultDO();

        if (StringUtils.isNotBlank(record.get("shopname"))) {
            String shopName = record.get("shopname");
            if (StringUtils.isNotBlank(record.get("branchname"))) {
                shopName = String.format("%s(%s)", shopName, record.get("branchname"));
            }
            resultDO.setShopName(shopName);
        }

        if (StringUtils.isNotBlank(record.get("shopid"))) {
            resultDO.setShopId(Integer.parseInt(record.get("shopid")));
        }
        if (StringUtils.isNotBlank(record.get("avgprice"))) {
            resultDO.setConsumeAvg(record.get("avgprice"));
        }
        if (StringUtils.isNotBlank(record.get("shoppower"))) {
            resultDO.setEvaluate(record.get("shoppower"));
        }
        if (StringUtils.isNotBlank(record.get("matchtext"))) {
            String matchtext = record.get("matchtext");
            if (matchtext.indexOf(" 全部") > 0) {
                matchtext = matchtext.replace(" 全部", "");
            }
            resultDO.setBusinessArea(matchtext);
        }
        if (StringUtils.isNotBlank(record.get("address"))) {

            resultDO.setAddressDetail(record.get("address"));
        }
        return resultDO;
    }

    private Request buildSearchRequest(String shopName, int cityId, int pageNo, int pageSize) {
        Request request = new Request(Request.Platform.MAPI, "ktv-businessmanager");
        request.addQuery(new TermQuery("categoryids", Lists.newArrayList("2892")));
        request.addQuery(new TermQuery("cityid", String.valueOf(cityId)));
        if (StringUtils.isNotBlank(shopName)) {
            request.addQuery(new KeywordQuery("shopname", shopName));
        }
        request.addSortItem(new SortItem("shopid", Request.SortOrder.ASC));

        //设置需要返回的字段
        request.setOutputFieldList(Arrays.asList(SHOP_NEED_PROPERTORY));
        if (0 >= pageNo) {
            pageNo = 0;
        }
        if (0 >= pageSize) {
            pageSize = SEARCH_PAGE_SIZE;
        }
        request.setLimit(pageNo * pageSize, pageSize);

        return request;
    }

    private void fillShopBindType(List<KTVBizManagerSearchResultDO> ktvBizManagerSearchResultDOs) {
        if (CollectionUtils.isEmpty(ktvBizManagerSearchResultDOs)) {
            return;
        }
        List<Integer> shopIds = Lists.transform(ktvBizManagerSearchResultDOs, bizManagerSearchResultToShopIdTransfer);
        try {
            for (KTVBizManagerSearchResultDO ktvBizManagerSearchResultDO : ktvBizManagerSearchResultDOs) {
                boolean isInBlackList = ktvShopBlackListService.isShopInBlackList(ktvBizManagerSearchResultDO.getShopId(), BlackListType.KTV_BizManager.getType());
                if (!isInBlackList) {
                    ktvBizManagerSearchResultDO.setShopBindType(2);
                } else {
                    ktvBizManagerSearchResultDO.setShopBindType(3);
                }
            }
        } catch (Exception e) {
            logger.error("[ktv widget manager] error in set shop bind type ", e);
        }

    }

    private Function<KTVBizManagerSearchResultDO, Integer> bizManagerSearchResultToShopIdTransfer = new Function<KTVBizManagerSearchResultDO, Integer>() {
        @Override
        public Integer apply(KTVBizManagerSearchResultDO input) {
            return input.getShopId();
        }
    };
}
