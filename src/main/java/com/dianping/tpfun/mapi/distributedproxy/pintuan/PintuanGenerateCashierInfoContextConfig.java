package com.dianping.tpfun.mapi.distributedproxy.pintuan;

import com.meituan.nibscp.unity.migration.distributed.proxy.context.DistributedProxyBaseContext;
import com.meituan.nibscp.unity.migration.distributed.proxy.enums.FlowType;
import com.meituan.nibscp.unity.migration.distributed.proxy.ext.ContextConfig;
import org.springframework.stereotype.Component;


@Component
public class PintuanGenerateCashierInfoContextConfig implements ContextConfig {


    @Override
    public String migrationCode(DistributedProxyBaseContext baseContext, String proxyTarget, String method, Object[] args) {
        return "general_pintuan.normal.order.api";
    }

    @Override
    public String key(DistributedProxyBaseContext baseContext, String proxyTarget, String method, Object[] args) {
        return "generateCashierInfo";
    }

    @Override
    public FlowType flowType(DistributedProxyBaseContext baseContext, String proxyTarget, String method, Object[] args) {
        return FlowType.MASTER;
    }
}
