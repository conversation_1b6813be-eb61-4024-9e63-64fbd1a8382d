package com.dianping.tpfun.mapi.dto.promo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * ktv优惠促销信息
 */
@MobileDo(name = "KTVPromoItem")
public class KTVPromoItem implements Serializable {


    // 1-满赠 2-到店送 3-提前预定折扣
    @MobileDo.MobileField(name = "PromoType")
    private int promoType;
    @MobileDo.MobileField(name = "PromoInfo")
    private String promoInfo;
    @MobileDo.MobileField(name = "ShopName")
    private String shopName;
    @MobileDo.MobileField(name = "Price")
    private String price;
    @MobileDo.MobileField(name = "PromoIcon")
    private String promoIcon;
    @MobileDo.MobileField(name = "PromoInfoJson")
    private String promoInfoJson;


    public int getPromoType() {
        return promoType;
    }

    public void setPromoType(int promoType) {
        this.promoType = promoType;
    }

    public String getPromoInfo() {
        return promoInfo;
    }

    public void setPromoInfo(String promoInfo) {
        this.promoInfo = promoInfo;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }


    public String getPromoInfoJson() {
        return promoInfoJson;
    }

    public void setPromoInfoJson(String promoInfoJson) {
        this.promoInfoJson = promoInfoJson;
    }

    public String getPromoIcon() {
        return promoIcon;
    }

    public void setPromoIcon(String promoIcon) {
        this.promoIcon = promoIcon;
    }
}
