/**
 * 
 */
package com.dianping.tpfun.mapi.dto.shop.list;

/**
 * <AUTHOR>
 *
 */
public enum MobileShopStatusEnum {
	
	/**
	 * 正常商户
	 */
	Normal(0),
	
	/**
	 * 已关闭
	 */
	Closed(1),
	
	/**
	 * 尚未营业
	 */
	NotOpen(2),
	
	/**
	 * 暂时歇业
	 */
	Suspend(3),
	
	/**
	 * 只做手机端展示
	 */
	MobileOnly(4);
	
	
	public final int value;
	
	private MobileShopStatusEnum(int value) {
		this.value = value;
	}
	
	/**
	 * 把主站状态映射到手机状态
	 * 主站状态（0只在手机端展示 1已关 2暂时歇业 4尚未营业 3非active 5正常 10积分商户）
	 * @param power
	 * @return
	 */
	public static MobileShopStatusEnum getSatusByPower(short power) {
		switch(power){
			case 0:
				return MobileShopStatusEnum.MobileOnly;
			case 1:
				return MobileShopStatusEnum.Closed;
			case 2:
				return MobileShopStatusEnum.Suspend;
			case 3:
				return MobileShopStatusEnum.Normal;
			case 4:
				return MobileShopStatusEnum.NotOpen;
			case 5:
				return MobileShopStatusEnum.Normal;
			case 10:
				return MobileShopStatusEnum.Normal;
			default:
				return MobileShopStatusEnum.Normal;	
		}
	}
	
}
