package com.dianping.tpfun.mapi.dto.tip;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

/**
 * Created by yangquan02 on 20/2/27.
 */
@MobileDo(name = "NoticeDo")
public class NoticeDo implements Serializable {

    @FieldDoc(description = "跳转链接")
    @MobileDo.MobileField(name = "redirectUrl")
    private String redirectUrl;

    @FieldDoc(description = "提示图标")
    @MobileDo.MobileField(name = "icon")
    private String icon;


    @FieldDoc(description = "背景颜色")
    @MobileDo.MobileField(name = "bgColor")
    private String bgColor;

    @FieldDoc(description = "文字颜色")
    @MobileDo.MobileField(name = "textColor")
    private String textColor;

    @FieldDoc(description = "文案")
    @MobileDo.MobileField(name = "text")
    private String text;

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getBgColor() {
        return bgColor;
    }

    public void setBgColor(String bgColor) {
        this.bgColor = bgColor;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
