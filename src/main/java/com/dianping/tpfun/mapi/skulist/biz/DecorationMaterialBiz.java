package com.dianping.tpfun.mapi.skulist.biz;

import com.dianping.mobile.base.datatypes.utils.VersionUtil;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.dianping.tpfun.mapi.skulist.data.entities.ListContext;
import com.dianping.tpfun.mapi.skulist.data.enums.MaterialNewUrlSputypeEnum;
import com.dianping.tpfun.mapi.skulist.data.enums.SkuViewItemDo;
import com.dianping.tpfun.mapi.skulist.utils.UrlUtils;
import com.dianping.tpfun.product.api.sku.ProductService;
import com.dianping.tpfun.product.api.sku.model.ProductItem;
import com.dianping.tpfun.product.api.sku.request.GetProductItemByProductRequest;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: yinshunming
 * @email: <EMAIL>
 * @date: 2022/7/14 5:38 下午
 * @description:
 */
@Component
@Log4j2
public class DecorationMaterialBiz {
    private static final Logger LOGGER = LoggerFactory.getLogger(DecorationMaterialBiz.class);
    private static final int CERAMICTILE_TYPE = 1061;

    @Reference(timeout = 1000, url = "http://service.dianping.com/tpfunService/skuProductService_1.0.0")
    private ProductService productService;

    /*
     * 对不同版本，家装建材的价格范围进行兼容处理
     * */
    public void setDecorationMaterialPrice(List<SkuViewItemDo> skuViewItemDos, List<Integer> productIds, IMobileContext mobileContext) {
        if (CollectionUtils.isEmpty(skuViewItemDos) || CollectionUtils.isEmpty(productIds) || mobileContext == null) {
            LOGGER.error("[DecorationMaterialBiz] setDecorationMaterialPrice is error,sku is {} or productIds is {} or mobileContext is {} ", skuViewItemDos, productIds, mobileContext);
            return;
        }
        String version = mobileContext.getVersion();
        boolean isMtClient = mobileContext.isMtClient();
        GetProductItemByProductRequest request = new GetProductItemByProductRequest();
        request.setProductIds(productIds);
        request.setNeedIemAttr(true);
        Map<Integer, List<ProductItem>> itemMap = null;
        try {
            itemMap = productService.mGetItemsByProductV2(request);
        } catch (Exception e) {
            log.error("[sku list] error in productService mGetItemsByProductV2", e);
        }
        if (MapUtils.isEmpty(itemMap)) {
            log.error("[sku list] error in DecorationMaterialBiz setDecorationMaterialPrice itemMap is {}", itemMap);
            return;
        }
        for (SkuViewItemDo sku : skuViewItemDos) {
            List<ProductItem> items = itemMap.get(sku.getDecorationMaterial().getProductId());
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }
            ProductItem productItem = items.get(0);
            String priceunit = null;
            if (sku.getDecorationMaterial().getSputye() == MaterialNewUrlSputypeEnum.CERAMICTILE_SPUTYPE.getSputype()
                    || sku.getDecorationMaterial().getSputye() == MaterialNewUrlSputypeEnum.CURTAIN_SPUTYPE.getSputype()) {
                priceunit = sku.getDecorationMaterial().getCeramicTilePriceunit();
            } else {
                priceunit = productItem.getAttrFirstValue("unit");
            }
            priceunit = StringUtils.isNotBlank(priceunit) ? "/" + priceunit : "";
            double price = sku.getDecorationMaterial().getProductPrice();
            double marketPrice = sku.getDecorationMaterial().getProductMarketPrice();

            String maxPriceAttr = productItem.getAttrFirstValue("maxPrice");
            String minPriceAttr = productItem.getAttrFirstValue("minPrice");
            double minPrice = StringUtils.isBlank(minPriceAttr) ? 0 : Double.valueOf(minPriceAttr);
            double maxPrice = StringUtils.isBlank(maxPriceAttr) ? 0 : Double.valueOf(maxPriceAttr);
            if (StringUtils.isNotBlank(version) && (((VersionUtil.compare(version, "10.6") >= 0 && !isMtClient)
                    || (VersionUtil.compare(version, "9.9.8") >= 0 && isMtClient)))) {
                if (price > 0) {
                    sku.getDecorationMaterial().setProductPrice(price);
                    sku.getDecorationMaterial().setRangePrice(String.format("¥%s" + priceunit, formatDouble(price)));
                } else if (maxPrice > 0) {
                    sku.getDecorationMaterial().setRangePrice(String.format("¥%s-%s" + priceunit, formatDouble(minPrice), formatDouble(maxPrice)));
                } else if (minPrice > 0 && Double.doubleToLongBits(maxPrice) == Double.doubleToLongBits(0)) {
                    sku.getDecorationMaterial().setRangePrice(String.format("¥%s以上" + priceunit, formatDouble(minPrice)));
                } else if (marketPrice > 0 && Double.doubleToLongBits(minPrice) == Double.doubleToLongBits(0)
                        && Double.doubleToLongBits(maxPrice) == Double.doubleToLongBits(0)) {
                    sku.getDecorationMaterial().setRangePrice(String.format("¥%s" + priceunit, formatDouble(marketPrice)));
                } else {
                    sku.getDecorationMaterial().setRangePrice("暂无价格");
                }
            } else {
                if (price > 0) {
                    sku.getDecorationMaterial().setProductPrice(Double.valueOf(formatDouble(price)));
                } else if (maxPrice > 0) {
                    sku.getDecorationMaterial().setProductPrice(Double.valueOf(formatDouble(maxPrice)));
                } else if (marketPrice > 0 && Double.doubleToLongBits(minPrice) == Double.doubleToLongBits(0)
                        && Double.doubleToLongBits(maxPrice) == Double.doubleToLongBits(0)) {
                    sku.getDecorationMaterial().setProductPrice(Double.valueOf(formatDouble(marketPrice)));
                }
            }
            //原价都为0
            sku.getDecorationMaterial().setProductMarketPrice(0);
        }

    }

    /*去掉价格.0格式*/
    private String formatDouble(double price) {
        String priceStr = String.valueOf(price);
        if (priceStr.contains(".")) {
            String[] priceArray = priceStr.split("\\.");
            if (priceArray != null && priceArray.length > 0) {
                return priceArray[0];
            }
        }
        return priceStr;
    }

    /*
     * 瓷砖按照平方米转换价格
     * */
    private double transCeramicTilePrice(SkuViewItemDo sku, double price) {
        int spuType = sku.getDecorationMaterial().getSputye();
        String size = sku.getDecorationMaterial().getCeramicTileSize();
        String priceunit = sku.getDecorationMaterial().getCeramicTilePriceunit();
        if (spuType == CERAMICTILE_TYPE && Double.doubleToLongBits(price) != Double.doubleToLongBits(0)
                && StringUtils.isNotBlank(size) && StringUtils.isNotBlank(priceunit) &&
                !"平方米".equals(priceunit)) {
            double sizeM = getSize(size);
            if (Double.doubleToLongBits(sizeM) != Double.doubleToLongBits(0)) {
                return price / sizeM;
            }
        }
        return price;
    }

    public void setProductUrl(SkuViewItemDo sku, Long shopId, String cooperation, ListContext context) {
        if (sku == null || context == null) {
            LOGGER.error("[DecorationMaterialBiz] setProductUrl is error,sku is null or context is null");
            return;
        }
        sku.getDecorationMaterial().setProductUrl(
                UrlUtils.getDecorationMaterialUrl(Integer.toString(sku.getDecorationMaterial().getProductId()), shopId.toString(), context.getMobileContext(), cooperation, MaterialNewUrlSputypeEnum.isExitSputype(sku.getDecorationMaterial().getSputye())));
    }

    //校验瓷砖大小规格并返回大小，校验不通过的放回0
    private double getSize(String sizeStr) {
        if (StringUtils.isNotBlank(sizeStr)) {
            String regEx = "(\\d+)mm\\*(\\d+)mm";
            Pattern pattern = Pattern.compile(regEx);
            Matcher matcher = pattern.matcher(sizeStr);
            if (matcher.matches()) {
                String m = matcher.group(1);
                String n = matcher.group(2);
                if (StringUtils.isNotBlank(m) && StringUtils.isNotBlank(n)) {
                    return (Double.valueOf(m) * Double.valueOf(n)) / 1000000;
                }
            }
            return 0;
        } else {
            return 0;
        }
    }
}