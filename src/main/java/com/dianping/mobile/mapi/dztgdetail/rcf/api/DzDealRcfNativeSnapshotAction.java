package com.dianping.mobile.mapi.dztgdetail.rcf.api;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.annotation.Action;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.enums.ReqProtocol;
import com.dianping.mobile.mapi.dztgdetail.action.AbsAction;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.DealNativeSnapshotMainProcessor;
import com.dianping.mobile.mapi.dztgdetail.util.IdUpgradeUtils;
import com.sankuai.sig.botdefender.adapter.annotation.CryptMethod;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2024/11/16 15:06
 */
@Controller("general/platform/dztgdetail/nativedealsnapshot.bin")
@Action(url = "nativedealsnapshot.bin", httpType = "get", protocol = {ReqProtocol.MAPI, ReqProtocol.REST})
public class DzDealRcfNativeSnapshotAction extends AbsAction<DealNativeSnapshotReq> {

    @Resource
    private DealNativeSnapshotMainProcessor dealNativeSnapshotMainProcessor;

    @Override
    protected IMobileResponse validate(DealNativeSnapshotReq request, IMobileContext context) {
        IdUpgradeUtils.processProductIdForDealNativeSnapshotReq(request, "nativedealsnapshot.bin");
        if (request == null) {
            return Resps.PARAM_ERROR;
        }
        return null;
    }

    @Override
    @CryptMethod
    protected IMobileResponse execute(DealNativeSnapshotReq request, IMobileContext context) {
        EnvCtx envCtx = initEnvCtxV2(context);
        // 未登录状态检验
        if (!envCtx.isLogin()) {
            return new CommonMobileResponse(DealNativeSnapshotMainProcessor.Response.fail("NO_LOGIN"));
        }
        DealNativeSnapshotMainProcessor.Response result = dealNativeSnapshotMainProcessor.process(request, envCtx);
        Cat.logEvent("DealNativeSnapshotMainProcessor", result.getResult());
        return new CommonMobileResponse(result.getDealRcfNativeSnapshot());
    }

    @Override
    protected List<ClientInfoRule> getRule() {
        return Collections.emptyList();
    }

}
