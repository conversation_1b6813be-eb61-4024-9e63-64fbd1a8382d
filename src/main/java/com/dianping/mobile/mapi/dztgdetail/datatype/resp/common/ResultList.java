package com.dianping.mobile.mapi.dztgdetail.datatype.resp.common;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/6 16:31
 */
@MobileDo(id = 0x26c3)
public class ResultList implements Serializable {
    /**
     * 表示结果总数 unknow = -1
     */
    @MobileDo.MobileField(key = 0x177d)
    private int recordCount;

    /**
     * start from 0
     */
    @MobileDo.MobileField(key = 0xaa64)
    private int startIndex;

    /**
     * -
     */
    @MobileDo.MobileField(key = 0xf0b)
    private boolean isEnd;

    /**
     * 表示下一页请求的start
     */
    @MobileDo.MobileField(key = 0x5703)
    private int nextStartIndex;

    /**
     * 用于控制list数据为空时的客户端显示
     */
    @MobileDo.MobileField(key = 0xa465)
    private String emptyMsg;

    /**
     * 用于搜索关键字优化统计
     */
    @MobileDo.MobileField(key = 0x2d87)
    private String queryID;

    public int getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public boolean getIsEnd() {
        return isEnd;
    }

    public void setIsEnd(boolean isEnd) {
        this.isEnd = isEnd;
    }

    public int getNextStartIndex() {
        return nextStartIndex;
    }

    public void setNextStartIndex(int nextStartIndex) {
        this.nextStartIndex = nextStartIndex;
    }

    public String getEmptyMsg() {
        return emptyMsg;
    }

    public void setEmptyMsg(String emptyMsg) {
        this.emptyMsg = emptyMsg;
    }

    public String getQueryID() {
        return queryID;
    }

    public void setQueryID(String queryID) {
        this.queryID = queryID;
    }
}
