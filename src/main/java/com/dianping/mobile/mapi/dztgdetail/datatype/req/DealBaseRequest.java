package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.annotation.MobileRequest.Param;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;

import java.io.Serializable;

@TypeDoc(description = "dealbase.bin接口请求参数")
@MobileRequest
public class DealBaseRequest implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单id, int类型")
    @Param(name = "dealid")
    private Integer dealId;

    @FieldDoc(description = "团单id, string类型")
    @Param(name = "stringdealid")
    private String stringDealId;

    @FieldDoc(description = "城市id")
    @Param(name = "cityid")
    private Integer cityId;

    @FieldDoc(description = "APP希望看到的商户信息，不填写则由后端排序生成商家信息")
    @Param(name = "poiid")
    @Deprecated
    private Integer poiId;
    @Param(name = "poiIdEncrypt")
    @DecryptedField(targetFieldName = "poiId")
    private String poiIdEncrypt;

    @FieldDoc(description = "升级后POIID")
    @Param(name = "poiidstr")
    private String poiIdStr;
    @Param(name = "poiIdStrEncrypt")
    @DecryptedField(targetFieldName = "poiIdStr")
    private String poiIdStrEncrypt;

    @FieldDoc(description = "distance表示商家信息按距离排序，rating表示商家信息按照评价排序")
    @Param(name = "sort")
    private String sort;  //APP希望看到的商户信息

    @FieldDoc(description = "纬度")
    @Param(name = "lat")
    private Double lat;

    @FieldDoc(description = "经度")
    @Param(name = "lng")
    private Double lng;

    @FieldDoc(description = "系统类型")
    @Param(name = "utm_medium")
    private String utm_medium;

    @FieldDoc(description = "软件版本")
    @Param(name = "utm_term")
    private String utm_term;

    @FieldDoc(description = "Android客户端utm_term传递的是整型的版本号，但是非技术人员无法理解这个版本号，所以需要另外传递一个字符串类型的版本名参数。")
    @Param(name = "version_name")
    private String version_name;

    @FieldDoc(description = "表示推广渠道，例如appstore，tongbu等")
    @Param(name = "utm_source")
    private String utm_source;

    @FieldDoc(description = "按照数据组 stat_para_extend 的定义来填写")
    @Param(name = "utm_campaign")
    private String utm_campaign;

    @FieldDoc(description = "表示用户的deviceid")
    @Param(name = "utm_content")
    private String utm_content;

    @FieldDoc(description = "uuid")
    @Param(name = "uuid")
    private String uuid;

    public Integer getDealId() {
        return dealId == null ? 0 : dealId;
    }

    public void setDealId(Integer dealId) {
        this.dealId = dealId;
    }

    public Integer getCityId() {
        return cityId == null ? 0 : cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getPoiId() {
        return poiId == null ? 0 : poiId;
    }

    public void setPoiId(Integer poiId) {
        this.poiId = poiId;
    }

    public String getPoiIdStr() {
        return poiIdStr;
    }

    public void setPoiIdStr(String poiIdStr) {
        this.poiIdStr = poiIdStr;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public String getUtm_medium() {
        return utm_medium;
    }

    public void setUtm_medium(String utm_medium) {
        this.utm_medium = utm_medium;
    }

    public String getUtm_term() {
        return utm_term;
    }

    public void setUtm_term(String utm_term) {
        this.utm_term = utm_term;
    }

    public String getVersion_name() {
        return version_name;
    }

    public void setVersion_name(String version_name) {
        this.version_name = version_name;
    }

    public String getUtm_source() {
        return utm_source;
    }

    public void setUtm_source(String utm_source) {
        this.utm_source = utm_source;
    }

    public String getUtm_campaign() {
        return utm_campaign;
    }

    public void setUtm_campaign(String utm_campaign) {
        this.utm_campaign = utm_campaign;
    }

    public String getUtm_content() {
        return utm_content;
    }

    public void setUtm_content(String utm_content) {
        this.utm_content = utm_content;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getPoiIdEncrypt() {
        return poiIdEncrypt;
    }

    public void setPoiIdEncrypt(String poiIdEncrypt) {
        this.poiIdEncrypt = poiIdEncrypt;
    }

    public String getPoiIdStrEncrypt() {
        return poiIdStrEncrypt;
    }

    public void setPoiIdStrEncrypt(String poiIdStrEncrypt) {
        this.poiIdStrEncrypt = poiIdStrEncrypt;
    }

    public String getStringDealId() {
        return stringDealId;
    }

    public void setStringDealId(String stringDealId) {
        this.stringDealId = stringDealId;
    }
}
