package com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail;

import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.CommonModuleWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.util.CommonModuleUtil;
import com.dianping.mobile.mapi.dztgdetail.util.DealVersionUtils;
import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.GpsCoordinateTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;

import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.DP_APP;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.DP_XCX;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_APP;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_XCX;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.UNKNOWN;

@Slf4j
public class ProductDetailCommonModuleProcessor extends AbsDealProcessor {

    @Resource
    private CommonModuleWrapper commonModuleWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        List<String> moduleKeys = CommonModuleUtil.getModuleKeys(ctx.getDealGroupDTO(), ctx.getEnvCtx());
        if (DealVersionUtils.isOldMetaVersion(ctx.getDealGroupDTO(), LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG)) {
            return false;
        }
        return CollectionUtils.isNotEmpty(moduleKeys);
    }


    @Override
    public void prepare(DealCtx ctx) {
        try {
            // 构造请求体
            ProductDetailPageRequest request = buildProductDetailPageRequest(ctx);
            Future<?> future = commonModuleWrapper.preQueryCommonModule(request);
            ctx.getFutureCtx().setCommomModuleFuture(future);
        } catch (Exception e) {
            log.error("ProductDetailTradeModuleProcessor prepare error, ctx:{}", ctx, e);
        }
    }

    @Override
    public void process(DealCtx ctx) {
        if (Objects.isNull(ctx.getFutureCtx().getCommomModuleFuture())) {
            return;
        }
        String json = commonModuleWrapper.getFutureResult(ctx.getFutureCtx().getCommomModuleFuture());
        if (StringUtils.isBlank(json)) {
            return;
        }
        GenericProductDetailPageResponse response = JSONObject.parseObject(json, GenericProductDetailPageResponse.class);
        ctx.setCommonModuleResponse(response);
    }

    private ProductDetailPageRequest buildProductDetailPageRequest(DealCtx ctx) {
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        EnvCtx envCtx = ctx.getEnvCtx();
        boolean isMt = envCtx.isMt();
        // regionid、regionVersion、regionType
        request.setCityId(ctx.getCityId4P());
        request.setCityLat(ctx.getCityLatitude());
        request.setCityLng(ctx.getCityLongitude());
        ClientTypeEnum clientTypeEnum = getClientType(envCtx);
        request.setClientType(clientTypeEnum.getCode());
        request.setCx(ctx.getCx());
        request.setGpsCityId(ctx.getGpsCityId());
        request.setGpsCoordinateType(GpsCoordinateTypeEnum.GCJ02.getCode());
        request.setModuleKeys(Sets.newHashSet(CommonModuleUtil.getModuleKeys(ctx.getDealGroupDTO(), ctx.getEnvCtx())));
        // mrn version
        ShepherdGatewayParam shepherdGatewayParam = new ShepherdGatewayParam();
        shepherdGatewayParam.setMrnVersion(ctx.getMrnVersion());
        shepherdGatewayParam.setCsecversionname(ctx.getWxVersion());
        shepherdGatewayParam.setUnionid(envCtx.getUnionId());
        if (isMt) {
            shepherdGatewayParam.setDeviceId(envCtx.getUuid());
        } else {
            shepherdGatewayParam.setDeviceId(envCtx.getDpId());
        }
        shepherdGatewayParam.setMtUserId(envCtx.getMtUserId());
        shepherdGatewayParam.setDpUserId(envCtx.getDpUserId());
        shepherdGatewayParam.setMtVirtualUserId(envCtx.getMtVirtualUserId());
        shepherdGatewayParam.setDpVirtualUserId(envCtx.getDpVirtualUserId());
        shepherdGatewayParam.setAppVersion(envCtx.getVersion());
        shepherdGatewayParam.setMobileOSType("ios");
        request.setShepherdGatewayParam(shepherdGatewayParam);
        request.setProductId(ctx.getDealId4P());
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        request.setUserLat(ctx.getUserlat());
        request.setUserLng(ctx.getUserlng());
        request.setPoiId(ctx.getLongPoiId4PFromResp());
        request.setSkuId(StringUtils.isNotBlank(ctx.getSkuId()) ? Long.parseLong(ctx.getSkuId()) : 0L);
        request.setCityLat(ctx.getCityLatitude());
        request.setCityLng(ctx.getCityLongitude());
        request.setPageSource(ctx.getRequestSource());
        CustomParam customParam = new CustomParam();
        DealGroupDTO dealGroupDTO = ctx.getDealGroupDTO();
        if (Objects.nonNull(dealGroupDTO) && Objects.nonNull(dealGroupDTO.getCategory())) {
            DealGroupCategoryDTO category = dealGroupDTO.getCategory();
            if (Objects.nonNull(category.getCategoryId())) {
                customParam.addParam("productSecondCategoryId", String.valueOf(category.getCategoryId()));
            }
            if (Objects.nonNull(category.getServiceTypeId())) {
                customParam.addParam("productThirdCategoryId", String.valueOf(category.getServiceTypeId()));
            }
        }
        request.setCustomParam(customParam);
        return request;
    }

    private ClientTypeEnum getClientType(EnvCtx envCtx) {
        if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_APP) {
            return MT_APP;
        } else if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_APP) {
            return DP_APP;
        } else if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP) {
            return MT_XCX;
        } else if (envCtx.getDztgClientTypeEnum() == DztgClientTypeEnum.DIANPING_WEIXIN_MINIAPP) {
            return DP_XCX;
        }
        return UNKNOWN;
    }

}
