package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.HeaderPicExhibitShowRuleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/20
 */
@Component
@Slf4j
public abstract class AbstractHeaderPicProcessor implements HeaderPicProcessor {

    /**
     * 根据各行业配置的头图和款式的展示规则拼装dealGroupPBO
     *
     * @param ctx
     * @param dealGroupPBO
     */
    public void assembleHeaderPicAndExhibitInfo(DealCtx ctx, DealGroupPBO dealGroupPBO) {
        // 获取头图&款式展示规则配置
        HeaderPicExhibitShowRuleConfig ruleConfig = LionConfigUtils.getHeaderPicShowRule(ctx.getCategoryId());
        Boolean showHeaderPic = ruleConfig.getShowHeaderPic();
        Boolean showExhibitConfig = ruleConfig.getShowExhibit();
        // 打点
        logEvent(ruleConfig);

        // 如果配置需要展示款式信息，但是ctx中款式信息为空，则直接返回，避免款式和斗图不都展示的情况
        if (!showHeaderPic && showExhibitConfig && Objects.isNull(dealGroupPBO.getExhibitContents())) {
            log.info(String.format("assembleHeaderPicAndExhibitInfo exception, dealGroupId:{}", ctx.getDealBaseReq().getDealgroupid()));
            return;
        }

        // 不展示头图模块
        if (!showHeaderPic) {
            dealGroupPBO.setDealContents(null);
        }
        // 不展示款式模块
        if (!showExhibitConfig) {
            dealGroupPBO.setExhibitContents(null);
        }
    }

    protected void logEvent(HeaderPicExhibitShowRuleConfig ruleConfig) {
        Cat.logEvent("module.headerPic", String.format("headerPic_:%b_exhibit_%b", ruleConfig.getShowHeaderPic(), ruleConfig.getShowExhibit()));
    }

    /**
     * 是否满足展示有款式信息条件
     *
     * @param ctx
     * @return
     */
    public abstract boolean matchShowExhibit(DealCtx ctx);
}
