package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.technician.common.api.domain.Environment;
import com.dianping.technician.common.api.domain.Operator;
import com.dianping.technician.common.api.enums.OperatorRoleEnum;
import com.dianping.technician.common.api.enums.PlatformEnum;
import com.dianping.technician.common.api.enums.SourceEnum;
import com.dianping.technician.dto.shopsearch.TechCard;
import com.dianping.technician.dto.shopsearch.TechCategory;
import com.dianping.technician.dto.shopsearch.TechModuleRequest;
import com.dianping.technician.dto.shopsearch.TechModuleResult;
import com.dianping.technician.enums.TechGoodBindEnum;
import com.dianping.technician.service.eme.TechGoodsBindService;
import com.dianping.technician.service.shopsearch.TechShopSearchService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class BeautyTechGroupService {

    private static final String CAT_TYPE = BeautyTechGroupService.class.getSimpleName();
    private static final Logger LOGGER = LoggerFactory.getLogger(BeautyTechGroupService.class);

    @Autowired
    private TechGoodsBindService techGoodsBindService;
    @Autowired
    private TechShopSearchService techShopSearchService;

    public List<TechCard> getGroupTechs(DealCtx ctx) {
        long dealGroupId = ctx.getDpId();
        long dpShopIdLong = ctx.getDpLongShopId();
        try {
            List<Integer> technicianIds = techGoodsBindService.queryShowTechnicianIdByGoods((int) dealGroupId, TechGoodBindEnum.GROUP);
            if (CollectionUtils.isEmpty(technicianIds)) {
                return Lists.newArrayList();
            }
            // 查询商户下的所有手艺人
            TechModuleRequest request2 = new TechModuleRequest();
            request2.setDpShopIdLong(dpShopIdLong);
            request2.setMt(ctx.isMt());
            request2.setNeedAddBtn(false);
            request2.setEnvironment(Environment.builder().platform(ctx.isMt() ? PlatformEnum.MEI_TUAN : PlatformEnum.DIAN_PING)
                    .sourceEnum(ctx.isMt() ? SourceEnum.MT_APP : SourceEnum.DP_APP)
                    .build());
            request2.setOperator(Operator.builder().operatorRole(OperatorRoleEnum.USER.getCode())
                    .operatorId(String.valueOf(0)).build());
            TechModuleResult res = techShopSearchService.searchShopTechList(request2);
            List<TechCategory> categoryList = res.getCategoryList();
            if (CollectionUtils.isEmpty(categoryList) || CollectionUtils.isEmpty(categoryList.get(0).getTechnicians())) {
                return Lists.newArrayList();
            }
            // 过滤绑定了团购的手艺人
            return categoryList.get(0).getTechnicians().stream()
                    .filter(k -> technicianIds.contains(k.getTechnicianId()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("group-load-tech-error,groupId:{},shopId:{}", dealGroupId, dpShopIdLong, e);
            return Lists.newArrayList();
        }
    }

}
