package com.dianping.mobile.mapi.dztgdetail.button.leadsdeal;

import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.mobile.mapi.dztgdetail.button.AbstractButtonBuilder;
import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.StyleTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.entity.LeadsDealBarConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
import com.dianping.mobile.mapi.dztgdetail.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2024-08-29
 * @description: 留资型行业底部button
 */
public class LeadsDealButtonBuilder extends AbstractButtonBuilder {

    @Override
    protected void doBuild(DealCtx context, ButtonBuilderChain chain) {
        buildButton(context);
        chain.build(context);
    }

    private void buildButton(DealCtx context) {
        LeadsDealBarConfig buyBarConfig = LionConfigUtils.getLeadsDealBarConfig(context);
        if (Objects.isNull(buyBarConfig)) {
            return;
        }
        // 预约按钮构造
        buildResvBtn(context, buyBarConfig);
        // 购买按钮构造
        buildBuyBtn(context, buyBarConfig);
        // 设置styleType
        context.getBuyBar().setStyleType(StyleTypeEnum.BUY_BUTTON_REPLACE_SHARE.code);
    }

    private void buildResvBtn(DealCtx context, LeadsDealBarConfig buyBarConfig) {
        boolean isGreaterThanMinMrnVersion = isGreaterThanMinMrnVersion(context);
        // 如果团购有商家预约权益且为主App且大于最低mrn版本，则需返回预约弹窗
        if (DealUtils.hasResvBenefits(context) && context.getEnvCtx().judgeMainApp() && isGreaterThanMinMrnVersion) {
            DealBuyBtn btn = new DealBuyBtn(true, buyBarConfig.getResvBtnTitle());
            // 超值特惠团购预约按钮文案特殊处理
            setSpecialValueBtnTitle(context, btn, buyBarConfig);
            btn.setDetailBuyType(BuyBtnTypeEnum.RESV_DEAL.getCode());
            btn.setRedirectUrl(RedirectUrls.buildResvPopBtnUrl(context, buyBarConfig));
            context.addButton(btn);
            return;
        }
        List<String> phones = getPhones(context);
        // 如果团购无商家预约权益且为主App且大于最低mrn版本，且电话不为空则需返回电话
        if (CollectionUtils.isNotEmpty(phones) && context.getEnvCtx().judgeMainApp() && isGreaterThanMinMrnVersion) {
            DealBuyBtn btn = new DealBuyBtn(true, buyBarConfig.getPhoneResvBtnTitle());
            // 超值特惠团购预约按钮文案特殊处理
            setSpecialValueBtnTitle(context, btn, buyBarConfig);
            btn.setDetailBuyType(BuyBtnTypeEnum.RESV_DEAL.getCode());
            btn.setUsePhone(true);
            btn.setPhoneNos(getPhones(context));
            context.addButton(btn);
            return;
        }
        // 如果团购无商家预约权益，且商户电话信息为空，则返回不可预约按钮
        DealBuyBtn btn = new DealBuyBtn(false, buyBarConfig.getEmptyResvBtnTitle());
        btn.setDetailBuyType(BuyBtnTypeEnum.RESV_DEAL.getCode());
        context.addButton(btn);
    }

    private void setSpecialValueBtnTitle(DealCtx context, DealBuyBtn btn, LeadsDealBarConfig buyBarConfig) {
        if (btn == null || buyBarConfig == null || StringUtils.isEmpty(buyBarConfig.getSpecialValueResvBtnTitle())
                || !context.isHitSpecialValueDeal()) {
            return;
        }
        btn.setBtnTitle(buyBarConfig.getSpecialValueResvBtnTitle());
    }

    private List<String> getPhones(DealCtx ctx) {
        if (CollectionUtils.isNotEmpty(ctx.getPoiPhones())) {
            return filterPhones(ctx.getPoiPhones());
        }
        BestShopDTO bestShopDTO = ctx.getBestShopResp();
        if (Objects.isNull(bestShopDTO)) {
            return Collections.emptyList();
        }
        return filterPhones(bestShopDTO.getPhoneNos());
    }

    private List<String> filterPhones(List<String> phoneNos) {
        return phoneNos.stream()
                .filter(number -> StringUtils.isNotBlank(number))
                .collect(Collectors.toList());
    }

    private void buildBuyBtn(DealCtx context, LeadsDealBarConfig buyBarConfig) {
        DealBuyBtn canNotBuyButton = DealBuyHelper.getCanNotBuyButton(context);
        // 不可售卖状态或者当前团单为超值特惠团购或者非App，则隐藏该按钮
        if (Objects.nonNull(canNotBuyButton) || context.isHitSpecialValueDeal() || !(context.getEnvCtx().judgeMainApp())) {
            return;
        }
        // 提单页跳链不存在，则不构造购买按钮
        String buyUrl = getBuyUrl(context);
        if (StringUtils.isEmpty(buyUrl)) {
            return;
        }
        DealBuyBtn btn = new DealBuyBtn(true, buyBarConfig.getBuyBtnTitle());
        btn.setDetailBuyType(BuyBtnTypeEnum.NORMAL_DEAL.getCode());
        // 跳转链接可能会在主流程中被替换
        btn.setRedirectUrl(buyUrl);
        context.addButton(btn);
    }

    private String getBuyUrl(DealCtx context) {
        if (Objects.isNull(context.getSkuModule())
            || StringUtils.isEmpty(context.getSkuModule().getUrl())) {
            return StringUtils.EMPTY;
        }
        return context.getSkuModule().getUrl();
    }

    private boolean isGreaterThanMinMrnVersion(DealCtx context) {
        String minMrnVersion = LionConfigUtils.getLeadsDealMinMrnVersion();
        return VersionUtils.isGreatEqualThan(context.getMrnVersion(), minMrnVersion);
    }
}
