package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealBaseDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.common.constants.DealAttrKeys;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AttributeUtils;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.sankuai.beautycontent.security.displaycontrol.response.DisplayControlResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/18
 */
@Component
@Slf4j
public class BasicInfoBuilderService {

    public String getDefaultSkuId(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.BasicInfoBuilderService.getDefaultSkuId(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (dealCtx.getSkuCtx() != null && StringUtils.isNotBlank(dealCtx.getSkuCtx().getSkuId())) {
            return dealCtx.getSkuCtx().getSkuId();
        }
        return String.valueOf(getDealId(dealCtx.getDealGroupBase()));
    }

    private Integer getDealId(DealGroupBaseDTO dealGroupBaseDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.BasicInfoBuilderService.getDealId(com.dianping.deal.base.dto.DealGroupBaseDTO)");
        List<DealBaseDTO> deals = dealGroupBaseDTO.getDeals();
        if (CollectionUtils.isNotEmpty(deals)) {
            for (DealBaseDTO deal : deals) {
                if (deal.getDealStatus() == 1) {
                    return deal.getDealId();
                }
            }
        }
        return 0;
    }

    public String getTitle(final DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.BasicInfoBuilderService.getTitle(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (GreyUtils.enableQueryCenterForMainApi(dealCtx) && dealCtx.getDealGroupDTO() != null) {
            DealGroupDTO dealGroupDTO = dealCtx.getDealGroupDTO();
            String title = dealGroupDTO.getBasic().getTitle();
            String eduPrefix = getEduPrefix(dealCtx);
            if (eduPrefix != null) {
                return eduPrefix + title;
            }
            return title;
        } else {
            DealGroupBaseDTO dealGroupBaseDTO = dealCtx.getDealGroupBase();
            String title = dealGroupBaseDTO.getProductTitle();
            String eduPrefix = getEduPrefix(dealCtx);
            if (eduPrefix != null) {
                return eduPrefix + title;
            }
            return title;
        }
    }

    public String getEduPrefix(final DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.BasicInfoBuilderService.getEduPrefix(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        DisplayControlResponse displayControlResponse = dealCtx.getDisplayControlResponse();
        if (displayControlResponse == null || !displayControlResponse.canShow()) {
            return null;
        }
        List<AttributeDTO> attributeDTOS = dealCtx.getAttrs();
        String suitableAge = AttributeUtils.getAttributeValue(DealAttrKeys.EDU_SUITABLE_AGE, attributeDTOS);
        String femaleOnly = AttributeUtils.getAttributeValue(DealAttrKeys.EDU_FEMALE_ONLY, attributeDTOS);
        return matchEduPrefix(suitableAge, femaleOnly);
    }

    private String matchEduPrefix(String suitableAge, String femaleOnly) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.BasicInfoBuilderService.matchEduPrefix(java.lang.String,java.lang.String)");
        try {
            if (StringUtils.isBlank(suitableAge)) {
                return null;
            }
            Matcher betweenAgeMatcher = Pattern.compile("^(.*)岁-(.*)岁$").matcher(suitableAge);
            if (betweenAgeMatcher.matches()) {
                int maxAge = Integer.parseInt(betweenAgeMatcher.group(2));
                if (maxAge <= 7) {
                    return "【幼儿】";
                }
                if (maxAge <= 17) {
                    return "【青少】";
                }
            }

            Matcher beyondAgeMatcher = Pattern.compile("^(.*)岁及以上$").matcher(suitableAge);
            if (beyondAgeMatcher.matches()) {
                if ("是".equals(femaleOnly)) {
                    return "【女性班】";
                } else {
                    return "【成人】";
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Failed to resolve attributes of edu deals, suitableAge = {}, femaleOnly = {}", suitableAge, femaleOnly, e);
            return null;
        }
    }
}
