package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
@Data
@MobileDo(id = 0x3317)
public class ReductionPromoDetail implements Serializable {

    /**
     * 券名称
     */
    @MobileField(key = 0x7697)
    private String reduceName;

    /**
     * 券优惠金额
     */
    @MobileField(key = 0x6a3c)
    private String reducePromo;

}
