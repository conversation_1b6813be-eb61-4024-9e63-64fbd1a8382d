package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.handler;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/11/7 15:47
 */
@Service
@Slf4j
public class PhoneRepairHandler implements BaseReserveMaintenanceHandler {
    private static final String MT_KEY = "MtPhoneReserveExp";
    private static final String DP_KEY = "DpPhoneReserveExp";
    private static final List<String> PHONE_RESERVE_AB_KEYS = Lists.newArrayList(MT_KEY, DP_KEY);

    /**
     * 手机维修
     * 
     * @return
     */
    @Override
    public int getDealSecondCategory() {
        return 450;
    }

    @Override
    public String getExpName(boolean isMt) {
        return isMt ? MT_KEY : DP_KEY;
    }

    @Override
    public List<String> getAbKeys() {
        return PHONE_RESERVE_AB_KEYS;
    }
}
