package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.book.req.ShopBookDto;
import com.dianping.deal.detail.dto.DealGroupAntiFleeOrderDTO;
import com.dianping.deal.detail.dto.DealGroupDetailDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.sales.common.datatype.KeyParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.sales.common.datatype.SpuSale;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.stock.dto.ProductGroupStock;
import com.dianping.deal.style.dto.DealGroupDzxInfo;
import com.dianping.deal.style.dto.StyleResponse;
import com.dianping.gm.bonus.exposure.api.dto.CommonBonusExposureDTO;
import com.dianping.gm.marketing.member.card.api.dto.membercard.UserMemberCardInfoDTO;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.feature.model.FeatureDetailDTO;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.LEInsuranceAgreementEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtLiveSaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ShopCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.promoDetail.DealGift;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.dealstruct.PnPurchaseNoteDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.struct.DealDetailStructModuleDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.flash.vo.DealModuleDetailVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DealStyle;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgAdsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgMoreDealModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgSkuModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.MTTemplateKey;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DigestInfoDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.promo.DealPromoModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.style.BusinessStyle;
import com.dianping.mobile.mapi.dztgdetail.entity.CostEffectivePinTuan;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.PurchaseNoteStructuredConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.QueryExhibitImageParam;
import com.dianping.mobile.mapi.dztgdetail.entity.SkuSummary;
import com.dianping.mobile.mapi.dztgdetail.entity.UrlProcessorDztgClient;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.basic.SVIPMapperCacheProcessor;
import com.dianping.mobile.mapi.dztgdetail.util.FitnessCrossUtils;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.reception.service.dto.PromoMergeDTO;
import com.dianping.product.shelf.common.dto.subjectConfig.ConfigDTO;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.dianping.tgc.open.entity.QueryResponseDTO;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtDealDto;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.beautycontent.security.displaycontrol.response.DisplayControlResponse;
import com.sankuai.clr.content.core.thrift.dto.PlanDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDetail;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDistributionInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealTimeStockDTO;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import com.sankuai.mpmctcontent.query.thrift.dto.meta.BatchQueryTagValueResponseDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.MemberDiscountInfoDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.GetGrouponMemberInfoRespDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nibmktproxy.queryclient.proxy.PromotionDTOResult;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 查询团单信息的环境变量
 */
@Data
public class DealCtx {

    public DealCtx(EnvCtx ctx) {
        if (ctx != null) {
            this.envCtx = ctx;
        }
    }

    private String requestSource;
    /**
     * 这个地方传递值是json字符串，否则会无法转换导致报错
     */
    private String requestExtParam;

    private EnvCtx envCtx = new EnvCtx();
    private FutureCtx futureCtx = new FutureCtx();

    private int dpId; //点评团单ID
    private int mtId; //美团团单ID
    private int dpDealId;//点评套餐ID
    private String skuId;

    private int dpCityId;
    private int mtCityId;

    @Deprecated
    private int mtShopId;
    private long mtLongShopId;
    @Deprecated
    private int dpShopId;
    private long dpLongShopId;
    private String dpShopUuid;

    private double userlng;
    private double userlat;
    private double cityLongitude; //首页城市经度
    private double cityLatitude; //首页城市纬度
    private String regionId; //区域id，用于set化

    /**
     * 点评app环境IOS端的用户定位城市传的是美团的，注意这个是大坑，具体参考文档https://km.sankuai.com/collabpage/2289046202
     * 通过SVIPMapperCacheProcessor转换成了正确的双平台定位城市id
     *
     * @see SVIPMapperCacheProcessor
     */
    private int gpsCityId; //双平台用户定位城市，点评侧是点评id，美团侧是美团id
    private int dpGpsCityId;
    private int mtGpsCityId;

    private String position; //点位信息
    private String gpsCoordinateType;
    private String expResults;
    private ModuleAbConfig moduleAbConfig;
    private List<ModuleAbConfig> moduleAbConfigs = Lists.newArrayList();

    private DealGroupPBO result;
    private DealPromoModule dealPromoModuleResult;

    private DealGroupBaseDTO dealGroupBase;
    private DealGroupChannelDTO channelDTO;
    private CardSummaryBarDTO timesCard; //次卡信息
    private String pinPoolPromoDesc; //拼团优惠信息
    private boolean multiCard; //多次卡
    private BigDecimal pinPoolPromoAmount = BigDecimal.ZERO; //拼团优惠信息
    private UserMemberCardInfoDTO userMemberCard; //会员卡
    private List<PromoDisplayDTO> promoList;
    private List<PromoDisplayDTO> promoWithTimeList;
    private List<PromoDisplayDTO> discountPromoList;
    private Map<String, PromotionDTOResult> promotionMap;//营销代理返回
    private PromoMergeDTO promoMerge;
    private DealGroupAntiFleeOrderDTO fleeOrderDTO;
    private List<AttributeDTO> attrs;
    private SalesDisplayDTO salesDisplayDTO;
    private ProductGroupStock dealGroupStock;
    private DealActivityDTO choicestDealActivityDTO;
    private DealActivityDTO dealTitleActivityDTO;
    private List<DealActivityDTO> dealActivities;
    private BigDecimal commissionRate;
    private PromotionDTOResult promotionDTOResult;

    private StyleResponse styleResponse;
    private DealGroupDzxInfo dealGroupDzxInfo;
    private List<MtDealDto> mtDealDtoList;
    private List<MtDealDto> mtDealDtoListParall;
    private String reqChannel; // 来自request的入参
    private String lyyuserid; // 乐摇摇userid
    /**
     * 诚信参数
     */
    private String cx;
    private MTTemplateKey mtTemplateKey;
    private DealStyle dealStyle;
    private DealGroupDetailDTO dealGroupDetailDTO;
    private boolean hasStructedDetail; //是否有结构化数据
    private boolean favResult;

    private ModuleConfigsModule moduleConfigsModule;
    private DztgShareModule dztgShareModule;
    private DztgMoreDealModule dztgMoreDealModule;
    private DztgAdsModule dztgAdsModule;
    private List<Pair> structedDetails;
    private DztgSkuModule skuModule;
    private DztgHighlightsModule highlightsModule;// 亮点模块
    private ExhibitContentDTO exhibitContentDTO;
    private FeatureDetailDTO featureDetailDTO;
    private DigestInfoDTO digestInfoDTO;

    // 教育展示信息控制
    private DisplayControlResponse displayControlResponse;

    // 是否展示预约浮层
    private boolean showReserveEntrance;
    // 是否展示新预约浮层
    private boolean showNewReserveEntrance;
    // 预约浮层跳转链接
    private String reserveRedirectUrl;
    //  是否可以预约，类型为Boolean，表示是否可以进行预约操作。
    private Boolean isCanResv;

    private BestShopDTO bestShopResp;
    private List<RelatedShop> relatedShops;
    private String imUrl; //Im聊天地址
    private ShopBookDto shopBookDto;
    private boolean purchaseCanBook;
    private boolean doorToDoorPoi;

    private List<String> poiPhones;
    private DpPoiDTO dpPoiDTO;
    private MtPoiDTO mtPoiDTO;

    private PinProductBrief pinProductBrief;
    private QueryResponseDTO voucherList;
    private List<CommonBonusExposureDTO> bonusExposureDTOList;
    private Set<Integer> poiBackCategoryIds;
    private boolean isEnd;
    private boolean isCanNotBuy;
    // 是否是会员专属团单
    private boolean isMemberExclusive;
    private GetGrouponMemberInfoRespDTO memberInfoRespDTO;

    //商家会员价
    @Deprecated
    private MemberDiscountInfoDTO shopMemberDiscountInfoDTO;

    private List<String> dealExtraTypes; //商品类型，如巨划算商品等

    private PriceContext priceContext = new PriceContext();

    private DealBuyBar buyBar = new DealBuyBar(DealBuyBar.BuyType.COMMON.type, Lists.newArrayList());

    private SkuSummary skuSummary;

    //底Bar门市价是否隐藏
    private boolean marketPriceHided;

    //MarketPriceNormalButtonBuilder重置底Bar时，隐藏所有优惠信息
    private boolean marketPriceNormalButtonHide;

    //颜色是否需要转换
    private boolean convertColor;

    // 上单客户ID
    private long customerId;

    //是否是外部请求（如快手小程序）
    private boolean isExternal;

    // 预览单关联的门店数量（与正式团单完全无关）
    private int previewShopTotalNum;

    //是否是预览单
    private boolean previewDeal;

    //是否需要填充bestShop的信息
    private boolean needFillBestShop;

    // mrn版本
    private String mrnVersion;

    private ModuleExtraDTO moduleExtraDTO;

    //团单通用字段（JsonString，内容可扩展）
    private String dealParam;
    // 各场景到团详的自定义参数
    private String passParam;

    private SkuCtx skuCtx;
    // 不同业务场景下样式标识
    private BusinessStyle businessStyle;

    /**
     * 上面的mtCityId真实性不可信，乱赋值或者没有赋值
     */
    private int realMtCityId;
    /**
     * 私域直播间信息
     */
    private LiveRoomDetail privateLiveRoomInfo;

    /**
     * 客资信息，如医疗种植牙到店礼
     */
    private PlanDTO planDTO;

    /**
     * 直播间id
     */
    private long mLiveId;
    /**
     * 直播间售卖信息
     */
    private GoodsSellingInfoDTO mliveSellingInfo;
    /**
     * 直播渠道品标志
     */
    private Boolean mLiveChannel = Boolean.FALSE;

    private List<DealGift> dealGifts;

    private List<AttributeDTO> attributes;
    /**
     * 健身通数据
     */
    private FitnessCrossBO fitnessCrossBO;

    /**
     * 神券膨胀标识： 1-可膨 2-不可膨
     * https://km.sankuai.com/collabpage/2489971380
     */
    private int mmcInflate;

    /**
     * 神券可用标识： 1-可用 2-不可用
     * https://km.sankuai.com/collabpage/2489971380
     */
    private int mmcUse;

    /**
     * 券包可买标识： 1-可买 2-不可买
     * https://km.sankuai.com/collabpage/2489971380
     */
    private int mmcBuy;

    /**
     * 神券可领塞标识：1-可领塞 2-不可领塞
     * https://km.sankuai.com/collabpage/2489971380
     */
    private int mmcFree;

    /**
     * 神券包组件版本
     */
    private String mmcPkgVersion;

    /**
     * 小程序版本
     */
    private String wxVersion;

    /**
     * 销量数据
     */
    private Map<KeyParam, SpuSale> baseSaleMap;

    private String offlineCode;

    /**
     * 自助开台
     */
    private Boolean autoOpenTable = Boolean.FALSE;

    /**
     * 私域直播分享微信名称
     */
    private String wxName;

    /**
     * 个人分销参数
     */
    private String userDistributionParam;

    /**
     * 是否支持先用后付
     */
    private boolean creditPay;

    /**
     * 是否属于神券感知增强场景
     */
    private boolean hasSuperCouponScene;


    public boolean isMt() {
        return this.envCtx.isMt();
    }

    // 判断是否是 开店宝侧
    public boolean isDpMerchant() {
        return this.envCtx.isDpMerchant();
    }

    /**
     * 是否是美团美播小程序
     * @return true/false
     */
    public boolean isMtLiveMinApp() {
        return this.envCtx.isMtLiveMinApp();
    }

    // 判断是否是 阿波罗侧
    public boolean isApollo() {
        return this.envCtx.isApollo();
    }

    // 判断是否是 第三方平台 开店宝、阿波罗侧
    public boolean isThirdPArt() {
        return this.envCtx.isThirdPlatform();
    }

    //美团平台返回美团团单ID，点评平台返回点评团单ID
    public int getDealId4P() {
        return isMt() ? mtId : dpId;
    }

    public long getUserId4P() {
        return isMt() ? envCtx.getMtUserId() : envCtx.getDpUserId();//已确认判断平台后再使用
    }

    public int getCityId4P() {
        return isMt() ? mtCityId : dpCityId;
    }

    /**
     * 美团平台返回美团适用商户ID，点评平台返回点评适用商户ID
     * 注意：如果外部参数没有传shopid，也不会从其他地方去获取
     */
    @Deprecated
    public int getPoiId4PFromReq() {
        return isMt() ? mtShopId : dpShopId;
    }

    public long getLongPoiId4PFromReq() {
        long longPoiId = isMt() ? mtLongShopId : dpLongShopId;
        if (longPoiId == 0L) {
            return isMt() ? mtShopId : dpShopId;
        }
        return longPoiId;
    }

    /**
     * 美团平台返回美团适用商户ID，点评平台返回点评适用商户ID
     * 注意：如果外部参数没有传shopid，那么就会从适用商户中去获取
     */
    public long getLongPoiId4PFromResp() {
        long result = getLongPoiId4PFromReq();
        if (result == 0L && bestShopResp != null) {
            return isMt() ? bestShopResp.getMtShopId() : bestShopResp.getDpShopId();
        }
        return result;
    }

    /**
     * 美团平台返回美团适用商户UUID，点评平台返回点评适用商户UUID
     * 注意：如果外部参数没有传shopid，那么就会从适用商户中去获取
     */
    public String getShopUuidFromResp() {
        if (isMt()) {
            return null;
        }
        if (StringUtils.isNotBlank(dpShopUuid)) {
            return dpShopUuid;
        }
        long shopId = getLongPoiId4PFromReq();
        if (shopId == 0L && bestShopResp != null) {
            shopId = isMt() ? bestShopResp.getMtShopId() : bestShopResp.getDpShopId();
        }
        return ShopUuidUtils.getUuidById(shopId);
    }

    public int getCategoryId() {
        return channelDTO != null ? channelDTO.getCategoryId() : 0;
    }

    @Deprecated
    public String getChannelGroupName() {
        return channelDTO == null || channelDTO.getChannelDTO() == null
                ? null : channelDTO.getChannelDTO().getChannelGroupEn();
    }

    @Deprecated
    public void setDpShopId(int dpShopId) {
        this.dpShopId = dpShopId;
        this.dpLongShopId = dpShopId;
        this.dpShopUuid = ShopUuidUtils.getUuidById(dpShopId);
    }

    public void setDpLongShopId(long dpShopId) {
        this.dpShopId = dpShopId > Integer.MAX_VALUE ? 0 : (int) dpShopId;
        this.dpLongShopId = dpShopId;
        this.dpShopUuid = ShopUuidUtils.getUuidById(dpShopId);
    }


    public DealBuyBtn getPreButton() {
        return buyBar.getBuyBtns().isEmpty() ? null : buyBar.getBuyBtns().get(buyBar.getBuyBtns().size() - 1);
    }

    public int getButtonCount() {
        return buyBar.getBuyBtns().size();
    }

    public void addButton(DealBuyBtn buyBtn) {
        buyBar.getBuyBtns().add(buyBtn);
    }

    private boolean useQueryCenter = false;

    private boolean queryCenterHasError = false;

    private DealGroupDTO dealGroupDTO;
    private DealTimeStockDTO dealTimeStockDTO;
    private String saleStatus;

    private DealTechCtx dealTechCtx;

    private BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO;
    /**
     * 美团美播小程序上团单的售卖状态
     */
    private MtLiveSaleStatusEnum mtLiveSaleStatusEnum;

    public boolean isPreSale() {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        Optional<AttributeDTO> preSaleAttributeDTO = attrs.stream().filter(a -> isPreSaleTag(a)).findFirst();
        if (preSaleAttributeDTO.isPresent()) {
            return true;
        }
        return false;
    }

    private boolean isPreSaleTag(AttributeDTO a) {
        if (a == null || StringUtils.isEmpty(a.getName()) || CollectionUtils.isEmpty(a.getValue())) {
            return false;
        }
        return "preSaleTag".equals(a.getName()) && a.getValue().contains("true");
    }

    public boolean isMultiSku() {
        return this.getSkuCtx() != null && this.getSkuCtx().isMultiSku();
    }

    /**
     * 是否是健身通商品
     */
    public boolean isFitnessCrossDeal() {
        return FitnessCrossUtils.isFitnessCrossDeal(this);
    }

    /**
     * 是否使用card_style样式
     */
    private boolean enableCardStyle = false;

    /**
     * 是否使用card_style_v2样式
     */
    private boolean enableCardStyleV2 = false;

    /**
     * card_style实验结果
     */
    private ModuleAbConfig cardStyleAbConfig;

    /**
     * card_style_v2实验结果
     */
    private ModuleAbConfig cardStyleAbV2Config;

    /**
     * 是否为美甲多款式团单
     */
    private boolean isBeautyNailMultiStyle;

    /**
     * 价惠密文,来自于请求
     */
    private String pricecipher;

    /**
     * 平台id
     */
    private Long ptId;

    /**
     * 价保信息
     */
    private ObjectGuaranteeTagDTO priceProtectionInfo;

    /**
     * 买贵必赔信息
     */
    private ObjectGuaranteeTagDTO bestPriceGuaranteeInfo;

    /*
    * 安心医标签
    * */
    private List<Integer> safeMedicalTag;

    /**
     * 入参
     */
    private DealBaseReq dealBaseReq;
    /***
     * 加项信息
     */
    private AdditionalInfo additionalInfo;

    /**
     * 购买须知重构配置化信息
     */
    private PurchaseNoteStructuredConfig pNSConfig;

    /**
     * 购买须知信息
     */
    private PnPurchaseNoteDTO pnPurchaseNoteDTO;

    /**
     * 是否获取结构化购买须知信息
     */
    private boolean hitStructuredPurchaseNote;

    /**
     * 是否展示预约浮窗实验结果
     */
    private ModuleAbConfig showReservationAbConfig;

    /**
     * 是否免费团单
     */
    private boolean isFreeDeal;

    /**
     * 免费团单类型
     * @see FreeDealEnum
     */
    private FreeDealEnum freeDealType;

    /**
     * 免费团单展示配置
     */
    private FreeDealConfig freeDealConfig;

    /**
     * 直播数据
     */
    private MLiveInfoVo mLiveInfoVo;

    /**
     * processor粒度url和可访问端配置
     */
    private List<UrlProcessorDztgClient> urlProcessorDztgClientList;

    /**
     * 丽人纹绣新增tab
     */
    private TattooPrecautionsVO tattooPrecautionsVO;
    private TattooQAsVO tattooQAsVO;

    /**
     * 门店类目
     */
    private ShopCategoryEnum shopCategoryEnum;
    /**
     * 门店下面所有的tag标签
     */
    private Map<Long, List<DisplayTagDto>> dpShopId2TagsMap;

    /**
     * 特团拼团信息
     */
    private CostEffectivePinTuan costEffectivePinTuan;

    /**
     * 是否来自特团拼团
     */
    private boolean hitCostEffectivePinTuan;

    /**
     * LE洗涤履约保障
     */
    private LEInsuranceAgreementEnum lEInsuranceAgreementEnum;

    /**
     * 商户标签集合及标签配置
     */
    private List<FeatureDetailDTO> shopTagFeatures;

    /**
     * 款式ID
     */
    private Long infoContentId;

    /**
     * 款式标签返回值
     */
    private BatchQueryTagValueResponseDTO tagValueResponseDTO;

    /**
     * 款式请求参数
     */
    private QueryExhibitImageParam queryExhibitImageParam;

    /**
     * 眼镜款式实验结果
     */
    private ModuleAbConfig glassesExhibitAbConfig;

    /**
     * 留资信息
     */
    private LoadLeadsInfoRespDTO leadsInfo;

    /**
     * 是否为超值特惠团购
     */
    private boolean hitSpecialValueDeal;

    /**
     * 团详样式信息
     */
    private DealStyleBO dealStyleBO;
    /**
     * 定制样式团购详情
     */
    private DealModuleDetailVO dealModuleDetail;
    /**
     * 通用样式团购详情
     */
    private DealDetailStructModuleDo dealDetailStruct;
    /**
     * 安心学
     */
    private boolean isAnXinXue;

    /**
     * 安心练
     */
    private boolean isAnXinExercise;


    /**
     * 活动橱窗团单的活动属性（例如超值特惠团购）
     */
    private List<ConfigDTO> activityWindowDealConfigs;

    /**
     * VR链接
     */
    private String vrUrl;

    /**
     * 私域直播分销信息
     */
    private LiveRoomDistributionInfo liveRoomDistributionInfo;

    /**
     * 春节活动标签
     */
    private boolean isSpringFestivalTag;

    /**
     * 结构化退款购买须知表格控制开关
     */
    private boolean hasPurchaseNoteTable;
    /**
     * 预订团单保障配置
     */
    private List<FeatureDetailDTO> preOrderFeatDetails;
    /**
     * 预订团单销量
     */
    private long preOrderSales;

    /*
     * 放心种植标签
     * */
    private TagDTO safeImplantTag;

    /**
     * rcf快照-氛围条缓存数据
     */
    private Object queryExposureResourcesCache;

    /**
     * rcf快照-相似团购推荐缓存数据
     */
    private String dealFilterListCache;

    /**
     * 是否保洁自有品牌团单
     */
    private boolean cleaningSelfOwnDeal;

    /**
     * 是否无忧通团单
     */
    private boolean careFreeDeal;

    /**
     * 结婚团购商家是否有预约权益
     */
    private boolean hasBookBenefit;

    /**
     * 家政团购是否为强预订团单，默认为false
     */
    private boolean preOderDeal;

    /**
     * 通用模块数据
     */
    private GenericProductDetailPageResponse commonModuleResponse;

    /**
     * 商品详情页交易模块返回结果
     */
    private GenericProductDetailPageResponse productDetailTradeModuleResponse;
    /**
     * 是否隐藏会员卡引导
     */
    private boolean hideMemberCardGuide;
}