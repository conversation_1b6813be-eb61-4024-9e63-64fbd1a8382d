package com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.dealdetail.req.DealGroupCacheRequest;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024/11/8 16:15
 */
@Slf4j
@Component
public class DealGroupDTOCellarService extends AbstractDealDetailCacheBizService<DealGroupCacheRequest> {

    @Override
    public String buildKey(DealGroupCacheRequest request) {
        if (request.isMT()) {
            return String.format(CellarKey.MT_DEAL_ID_TO_CATEGORY.getCacheKeyTemplate(), request.getDealGroupId());
        } else {
            return String.format(CellarKey.DP_DEAL_ID_TO_CATEGORY.getCacheKeyTemplate(), request.getDealGroupId());
        }
    }

    @Override
    public DealGroupDTO parseCacheValue(String resultJson) {
        // 解析模型为 DealGroupDTO
        try {
            if (StringUtils.isBlank(resultJson)) {
                return null;
            }
            return JSON.parseObject(resultJson, DealGroupDTO.class);
        } catch (Exception e) {
            log.error("DealGroupDTOCellarService.parseResult,resultJson:{}, error:{}", resultJson, e);
            return null;
        }
    }
}
