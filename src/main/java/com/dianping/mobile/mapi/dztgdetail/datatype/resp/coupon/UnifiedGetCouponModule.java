package com.dianping.mobile.mapi.dztgdetail.datatype.resp.coupon;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/12/13.
 */
@TypeDoc(description = "领券数据")
@MobileDo(id = 0xcc8a)
@Data
public class UnifiedGetCouponModule implements Serializable {

    @FieldDoc(description = "抵用券加密id")
    @MobileField(key = 0x2488)
    private String couponGroupId;

    @FieldDoc(description = "抵用券使用跳转链接")
    @MobileField(key = 0xc56e)
    private String url;

    @FieldDoc(description = "领券返回信息")
    @MobileField(key = 0x38ec)
    private String message;

    @FieldDoc(description = "领券是否成功")
    @MobileField(key = 0xd0b6)
    private boolean success;
}
