package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.ActivityCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.pigeon.util.CollectionUtils;
import com.dianping.tpfun.product.api.sku.common.enums.FunChannel;
import com.dianping.tpfun.product.api.sku.common.enums.FunClientType;
import com.dianping.tpfun.product.api.sku.common.enums.FunPlatform;
import com.dianping.tpfun.product.api.sku.pintuan.PinFacadeService;
import com.dianping.tpfun.product.api.sku.pintuan.dto.BestPinTag;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tpfun.product.api.sku.pintuan.request.BestPinRequest;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductBriefReq;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductIdOptional;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.general.product.query.center.client.builder.model.DefaultSelectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;

/**
 * Created by zuomlin on 2018/12/17.
 */
@Slf4j
@Component
public class SkuWrapper extends AbsWrapper {

    @Autowired
    @Qualifier("pinFacadeServiceFuture")
    private PinFacadeService pinFacadeServiceFuture;

    @Resource
    @Qualifier("dealSkuServiceFuture")
    private DealSkuService dealSkuServiceFuture;

    @Resource
    @Qualifier("queryCenterDealGroupQueryService")
    public DealGroupQueryService dealGroupQueryService;


    /**
     * 获取团单的默认skuId
     * @param req 团单id
     * @param envCtx 环境信息
     * @return 默认skuId
     */
    public String getDefaultSkuId(DealBaseReq req, EnvCtx envCtx) {
        // 如果入参中带了skuId，则直接使用此skuId
        if (req.getSkuId() != null && NumberUtils.isCreatable(req.getSkuId())) {
            return req.getSkuId();
        }
        IdTypeEnum idTypeEnum = envCtx.isMt() ? IdTypeEnum.MT : IdTypeEnum.DP;
        QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(Long.valueOf(req.getDealgroupid())), idTypeEnum)
                .defaultSelect(DefaultSelectBuilder.builder().dealId()).build();
        try {
            QueryDealGroupListResponse response = dealGroupQueryService.queryByDealGroupIds(request);
            if (response == null) {
                return StringUtils.EMPTY;
            }
            DealGroupDTO dealGroupDTO = Optional.of(response)
                    .map(QueryDealGroupListResponse::getData)
                    .map(QueryDealGroupListResult::getList)
                    .orElse(new ArrayList<>()).stream()
                    .findFirst().orElse(null);
            if (Objects.isNull(dealGroupDTO)
                    || Objects.isNull(dealGroupDTO.getDefaultSelectDTO())
                    || Objects.isNull(dealGroupDTO.getDefaultSelectDTO().getDealId())) {
                log.info("SkuWrapper getDefaultSkuId is null, dealgroupId={}, isMt={}", req.getDealgroupid(), envCtx.isMt());
                return StringUtils.EMPTY;
            }
            return String.valueOf(dealGroupDTO.getDefaultSelectDTO().getDealId());
        } catch (Exception e) {
            log.error("SkuWrapper getDefaultSkuId error, dealgroupId={}, isMt={}", req.getDealgroupid(), envCtx.isMt(), e);
        }
        return StringUtils.EMPTY;
    }

    public Future preQueryDealSummary(SkuOptionBatchRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }
        try {
            dealSkuServiceFuture.batchQuerySummary(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            logger.error("SkuWrapper preQueryDealSummary error!", e);
            return null;
        }
    }

    public DealSkuSummaryDTO getSkuSummaryByDealId(long dealId, IdTypeEnum idTypeEnum) {
        SkuOptionBatchRequest request = new SkuOptionBatchRequest();
        request.setDealGroupIds(Lists.newArrayList(dealId));
        request.setDealGroupIdType(idTypeEnum.getCode());
        Future future = preQueryDealSummary(request);
        if (future == null) {
            return null;
        }
        Map<Long, DealSkuSummaryDTO> result = getFutureResult(future);
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        return result.getOrDefault(dealId, null);
    }

    private Future prepareGetPinProductIdByDealGroupIds(List<Integer> dealGroupIds) {
        if (CollectionUtils.isEmpty(dealGroupIds)) {
            return null;
        }
        try {
            pinFacadeServiceFuture.getPinProductIdByDealGroupIds(dealGroupIds, new GetPinProductIdOptional());
            return FutureFactory.getFuture();
        } catch (Exception e) {
            logger.error(String.format("pingfacadeservice_1.0.0: getPinProductIdByDealGroupIds future call failed, params: dealGroupIds=%s",
                    dealGroupIds), e);
        }
        return null;
    }

    private Map<Integer, Integer> getPinProductIdByDealGroupIds(Future future) {
        return getFutureResult(future);
    }

    public Map<Integer, Integer> getPinProductIdByDealGroupIds(List<Integer> dealGroupIds) {
        return getPinProductIdByDealGroupIds(prepareGetPinProductIdByDealGroupIds(dealGroupIds));
    }

    public Future prepareBatchGetPinProductBrief(GetPinProductBriefReq req) {
        if (req == null || CollectionUtils.isEmpty(req.getPinProductIds())) {
            return null;
        }
        try {
            pinFacadeServiceFuture.mGetPinProductBrief(req);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            logger.error("has exception prepareBatchGetPinProductBrief{}" + req.getPinProductIds());
        }
        return null;
    }

    public Future prepareBatchGetPinProductBrief(List<Integer> productIds, ActivityCtx activityCtx) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper.prepareBatchGetPinProductBrief(java.util.List,com.dianping.mobile.mapi.dztgdetail.datatype.context.ActivityCtx)");
        if (CollectionUtils.isEmpty(productIds)) {
            return null;
        }
        try {
            GetPinProductBriefReq getPinProductBriefReq = new GetPinProductBriefReq();
            getPinProductBriefReq.setPinProductIds(productIds);
            if (activityCtx.isMt()) {
                getPinProductBriefReq.setCityId(activityCtx.getMtCityId());
                getPinProductBriefReq.setFunChannel(FunChannel.MT.code);
                getPinProductBriefReq.setUserId(activityCtx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            } else {
                getPinProductBriefReq.setCityId(activityCtx.getDpCityId());
                getPinProductBriefReq.setFunChannel(FunChannel.DP.code);
                getPinProductBriefReq.setUserId(activityCtx.getEnvCtx().getDpUserId());
            }
            if (activityCtx.getEnvCtx().isMainApp()) {
                getPinProductBriefReq.setFunClientType(FunClientType.NATIVE.code);
                getPinProductBriefReq.setClientVersion(activityCtx.getEnvCtx().getVersion());
            } else if (activityCtx.getEnvCtx().isMainWX()) {
                getPinProductBriefReq.setFunClientType(FunClientType.MINIPROGRAM.code);
            } else {
                getPinProductBriefReq.setFunClientType(FunClientType.M.code);
            }
            if (activityCtx.getEnvCtx().isAndroid()) {
                getPinProductBriefReq.setFunPlatform(FunPlatform.ANDROID.code);
            } else if (activityCtx.getEnvCtx().isIos()) {
                getPinProductBriefReq.setFunPlatform(FunPlatform.IPHONE.code);
            } else {
                getPinProductBriefReq.setFunPlatform(FunPlatform.PC.code);
            }
            pinFacadeServiceFuture.mGetPinProductBrief(getPinProductBriefReq);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            logger.error(String.format("pingfacadeservice_1.0.0: mGetPinProductBrief future call failed, params: request=%s", ReflectionToStringBuilder.toString(productIds)), e);
        }
        return null;
    }

    public Map<Integer, PinProductBrief> batchGetPinProductBrief(Future future) {
        return getFutureResult(future);
    }

    public Future prepareBestPin(Integer dpDealGroupId, ActivityCtx activityCtx) {
        if (dpDealGroupId == null || dpDealGroupId <= 0) {
            return null;
        }
        try {
            BestPinRequest bestPinRequest = new BestPinRequest();
            bestPinRequest.setProductIds(Collections.singletonList(dpDealGroupId));
            bestPinRequest.setType(11);
            bestPinRequest.setLimitVersion(Boolean.TRUE);
            bestPinRequest.setProductType(100011);
            if (activityCtx.isMt()) {
                bestPinRequest.setCityId(activityCtx.getMtCityId());
                bestPinRequest.setPlatform(FunChannel.MT.code);
                bestPinRequest.setUserId(activityCtx.getEnvCtx().getMtUserId());//已确认判断平台后再使用
            } else {
                bestPinRequest.setCityId(activityCtx.getDpCityId());
                bestPinRequest.setPlatform(FunChannel.DP.code);
                bestPinRequest.setUserId(activityCtx.getEnvCtx().getDpUserId());
            }
            if (activityCtx.getEnvCtx().isMainApp()) {
                bestPinRequest.setClientType(FunClientType.NATIVE.code);
                bestPinRequest.setClientVersion(activityCtx.getEnvCtx().getVersion());
            } else if (activityCtx.getEnvCtx().isMainWX()) {
                bestPinRequest.setClientType(FunClientType.MINIPROGRAM.code);
            } else {
                bestPinRequest.setClientType(FunClientType.M.code);
            }
            if (activityCtx.getEnvCtx().isAndroid()) {
                bestPinRequest.setHardware(FunPlatform.ANDROID.code);
            } else if (activityCtx.getEnvCtx().isIos()) {
                bestPinRequest.setHardware(FunPlatform.IPHONE.code);
            } else {
                bestPinRequest.setHardware(FunPlatform.PC.code);
            }
            pinFacadeServiceFuture.getBestPinTag(bestPinRequest);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            logger.error("pingfacadeservice.getBestPinTag future call failed, params: dpId={}", dpDealGroupId, e);
        }
        return null;
    }

    public BestPinTag getBestPin(Future future) {
        Map<Integer, BestPinTag> bestPinTagMap = getFutureResult(future);
        return MapUtils.isEmpty(bestPinTagMap) ? null : bestPinTagMap.values().iterator().next();
    }
}
