package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DiscountCardWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.Future;

public class DiscountCardProcessor extends AbsDealProcessor {

    @Autowired
    private DiscountCardWrapper discountCardWrapper;

    @Override
    public boolean isEnable(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DiscountCardProcessor.isEnable(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        return ctx.getEnvCtx().isMainApp();
    }

    @Override
    public void prepare(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DiscountCardProcessor.prepare(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        long userId = ctx.isMt() ? ctx.getEnvCtx().getMtUserId() : ctx.getEnvCtx().getDpUserId();//已确认判断平台后再使用
        Future discountFuture = discountCardWrapper.
                preUserMemberCardByDealGroup(userId,ctx.getDpId(),ctx.isMt(),ctx.getDpLongShopId());
        ctx.getFutureCtx().setDiscountFuture(discountFuture);
    }

    @Override
    public void process(DealCtx ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.DiscountCardProcessor.process(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        ctx.setUserMemberCard(discountCardWrapper
                .loadUserMemberCardByDealGroup(ctx.getFutureCtx().getDiscountFuture()));
    }

}
