package com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.DealRcfCustomerProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten.DealModuleFlattenProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.service.DealDetailFlattenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/12/25 10:39
 */
@Slf4j
@Component
public class DealModuleFlattenHandler implements DealRcfCustomerProcessor {
    @Resource
    private DealDetailFlattenService dealDetailFlattenService;

    @Override
    public void customerProcess(DealNativeSnapshotReq request, DealBffResponseDTO bffResponse) {
        try{
            JSONObject dealModuleResult = bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dealmodule);
            dealDetailFlattenService.flattenDetail(dealModuleResult);
        }catch (Exception e){
            log.error("DealModuleFlattenHandler process error:{}", e);
        }
    }

    @Override
    public boolean canProcess(DealNativeSnapshotReq request, DealBffResponseDTO bffResponse) {
        // dealmodule接口返回有数据就处理
        return Objects.nonNull(bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dealmodule));
    }
}
