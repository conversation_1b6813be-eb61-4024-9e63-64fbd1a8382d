package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;

/**
 * @Author: <EMAIL>
 * @Date: 2023/7/25
 */
@Data
@TypeDoc(description = "多买多省搭售入参")
@MobileRequest
public class BuyMoreSaveMoreReq implements IMobileRequest, Serializable {

    @Deprecated
    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，int类型")
    @MobileRequest.Param(name = "dealgroupid")
    private Integer dealGroupId;

    @FieldDoc(description = "团单ID", rule = "美团平台为美团团单ID，点评平台为点评团单ID，string类型")
    @MobileRequest.Param(name = "stringdealgroupid")
    private String stringDealGroupId;

    @FieldDoc(description = "搭售信息来源，1-团详页搭售模块，2-浮层搭售模块")
    @MobileRequest.Param(name = "sourceType")
    private Integer sourceType;

    @FieldDoc(description = "渠道来源，特团渠道：cost_effective")
    @MobileRequest.Param(name = "source")
    private String source;

    @FieldDoc(description = "门店id", rule = "美团平台为美团商户ID，点评平台为点评商户ID")
    @MobileRequest.Param(name = "poiidStr")
    private String poiidStr;
    @MobileRequest.Param(name = "poiidStrEncrypt")
    @DecryptedField(targetFieldName = "poiidStr")
    private String poiidStrEncrypt;

    @FieldDoc(description = "商户ID", rule = "美团平台为美团商户UUID，点评平台为点评商户UUID")
    @MobileRequest.Param(name = "shopuuid")
    private String shopUuid;
    @MobileRequest.Param(name = "shopUuidEncrypt")
    @DecryptedField(targetFieldName = "shopUuid", assetIdType = SHOP_UUID)
    private String shopUuidEncrypt;

    @FieldDoc(description = "城市ID", rule = "美团平台为美团城市ID，点评平台为点评城市ID，优先给到首页城市ID（非用户地理位置城市）")
    @MobileRequest.Param(name = "cityid")
    private Integer cityId;

    @FieldDoc(description = "用户经度(注意app和小程序经纬度类型不同)")
    @MobileRequest.Param(name = "userlng")
    private Double userLng;

    @FieldDoc(description = "用户纬度(注意app和小程序经纬度类型不同)")
    @MobileRequest.Param(name = "userlat")
    private Double userLat;

    @Deprecated
    @FieldDoc(description = "开始位置")
    @MobileRequest.Param(name = "start")
    private Integer start;

    @Deprecated
    @FieldDoc(description = "数量")
    @MobileRequest.Param(name = "limit")
    private Integer limit;
}
