package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.mobile.mapi.dztgdetail.util.LogUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

@Data
public class ProcessHandler<T> implements Processor<T> {

    protected Logger errorLogger = LoggerFactory.getLogger(ProcessHandler.class);

    private List<Processor<T>> processorList;

    private String logName;

    @Override
    public boolean isEnable(T ctx) {
        return true;
    }

    @Override
    public boolean isEnd(T ctx) {
        return false;
    }

    @Override
    public void prepare(T ctx) {
        doPrepare(ctx, p -> p.prepare(ctx));
    }

    @Override
    public void process(T ctx) {
        doProcess(ctx, p -> p.process(ctx));
    }

    @Override
    public List<Integer> getConfigUrlDztgClient(T ctx) {
        return Collections.emptyList();
    }

    @Override
    public boolean matchDztgClient(T ctx, Processor<T> processor) {
        return true;
    }

    /**
     * 先执行prepare()再process
     *
     * @param ctx ctx
     */
    public void preThenProc(T ctx) {
        prepare(ctx);
        process(ctx);
    }

    private void doPrepare(T ctx, Consumer<Processor<T>> consumer) {
        if (CollectionUtils.isEmpty(processorList)) {
            return;
        }
        long startTime = System.currentTimeMillis();
        for (Processor<T> processor : processorList) {
            Transaction trx = Cat.newTransaction(String.format("[%s].Process.doPrepare", logName), processor.getClass().getName());
            try {
                if (processor.isEnd(ctx)) {
                    break;
                }
                if (processor.isEnable(ctx) && processor.matchDztgClient(ctx, processor)) {
                    consumer.accept(processor);
                }
                trx.setSuccessStatus();
            } catch (Exception e) {
                trx.setStatus(e);
                String msg = String.format("ProcessHandler[%s] do error", processor.getClass().getName());
                LogUtils.error(msg, e);
                errorLogger.error(msg, e);
            } finally {
                Cat.newCompletedTransactionWithDuration(String.format("[%s].ProcessChain.doPrepare", logName), processor.getClass().getName(), System.currentTimeMillis() - startTime);
                trx.complete();
            }
        }
    }

    private void doProcess(T ctx, Consumer<Processor<T>> consumer) {
        if (CollectionUtils.isEmpty(processorList)) {
            return;
        }
        long startTime = System.currentTimeMillis();
        for (Processor<T> processor : processorList) {
            Transaction trx = Cat.newTransaction(String.format("[%s].Process.doProcess", logName), processor.getClass().getName());
            try {
                if (processor.isEnd(ctx)) {
                    break;
                }
                if (processor.isEnable(ctx) && processor.matchDztgClient(ctx, processor)) {
                    consumer.accept(processor);
                }
                trx.setSuccessStatus();
            } catch (Exception e) {
                trx.setStatus(e);
                String msg = String.format("ProcessHandler[%s] do error", processor.getClass().getName());
                LogUtils.error(msg, e);
                errorLogger.error(msg, e);
            } finally {
                Cat.newCompletedTransactionWithDuration(String.format("[%s].ProcessChain.doProcess", logName), processor.getClass().getName(), System.currentTimeMillis() - startTime);
                trx.complete();
            }
        }
    }
}
