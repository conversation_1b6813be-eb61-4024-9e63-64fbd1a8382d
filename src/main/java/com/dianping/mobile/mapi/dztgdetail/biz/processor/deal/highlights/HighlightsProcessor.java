package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.Processor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.common.constants.EduConstant;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/8 17:12
 */
public class HighlightsProcessor extends AbsDealProcessor implements ApplicationListener {

    private static Map<Integer, Processor<DealCtx>> HIGHLIGHTS_PROCESSOR_MAP = new HashMap<>();
    private static Map<Integer, Processor<DealCtx>> HIGHLIGHTS_PROCESSOR_V2_MAP = new HashMap<>();
    private static final Integer MEDIC_EXAMINER_CATEGORY = 401;

    //亲子游乐团购分类
    private static final Integer FAMILY_FUN_CATEGORY = 1002;

    private static final Integer MASSAGE_CATEGORY = 303;

    private static final Integer SHOPPING_MALL_CATEGORY = 712;

    private static final Integer GLASSES_CATEGORY = 406;

    /**
     * 医疗-齿科-种植牙
     */
    private static final Integer IMPLANT_CATEGORY = 506;

    private static final Integer GYNECOLOGY_CATEGORY = 1603;

    private static final Integer OBSTETRICAL_CATEGORY = 1629;

    private static final Integer GROUP_BUILDING_CATEGORY = 324;

    //局改
    private static final Integer REASSURED_REPAIR_CATEGORY = 445;

    private static final Integer LIFE_CLEAR_CATEGORY = 409;

    //月子中心新建房型套餐
    private static final Integer CARE_CENTENR_HOUSE_STYLE= 1011;

    // 结婚-彩妆造型
    private static final Integer WEDDING_MAKEUP_STYLE = 908;

    @Autowired
    ApplicationContext applicationContext;

    @Override
    public void prepare(DealCtx ctx) {
        execute(ctx, p -> p.prepare(ctx));
    }

    @Override
    public void process(DealCtx ctx) {
        execute(ctx, p -> p.process(ctx));
    }

    private void execute(DealCtx ctx, Consumer<Processor<DealCtx>> func) {
        Processor<DealCtx> highlightsProcessor;
        if (ctx.isEnableCardStyleV2()) {
            highlightsProcessor = HIGHLIGHTS_PROCESSOR_V2_MAP.get(ctx.getCategoryId());
        } else {
            highlightsProcessor = HIGHLIGHTS_PROCESSOR_MAP.get(ctx.getCategoryId());
        }
        if (highlightsProcessor != null) {
            func.accept(highlightsProcessor);
        }
    }

    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        // 丽人
        for (Integer category : BeautyHighlightsProcessor.BEAUTY_CATEGORY) {
            HIGHLIGHTS_PROCESSOR_MAP.put(category, applicationContext.getBean(BeautyHighlightsProcessor.class));
            HIGHLIGHTS_PROCESSOR_V2_MAP.put(category, applicationContext.getBean(BeautyHighlightsV2Processor.class));
        }
        // 足疗
        HIGHLIGHTS_PROCESSOR_MAP.put(MASSAGE_CATEGORY, applicationContext.getBean(MassageHighlightsV1Processor.class));
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(MASSAGE_CATEGORY, applicationContext.getBean(MassageHighlightsV2Processor.class));
        HIGHLIGHTS_PROCESSOR_MAP.put(MEDIC_EXAMINER_CATEGORY, applicationContext.getBean(MedicExaminerHighlightsProcessor.class));
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(MEDIC_EXAMINER_CATEGORY, applicationContext.getBean(MedicExaminerHighlightsV2Processor.class));
        //亲子游乐
        HIGHLIGHTS_PROCESSOR_MAP.put(FAMILY_FUN_CATEGORY, applicationContext.getBean(FamilyFunHighlightsProcessor.class));
        // 摄影
        for (Integer category : PhotoHighlightsProcessor.PHOTO_CATEGORY) {
            HIGHLIGHTS_PROCESSOR_V2_MAP.put(category, applicationContext.getBean(PhotoHighlightsProcessor.class));
        }
        //商场
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(SHOPPING_MALL_CATEGORY, applicationContext.getBean(ShoppingMallHighlightsProcessor.class));
        HIGHLIGHTS_PROCESSOR_MAP.put(SHOPPING_MALL_CATEGORY, applicationContext.getBean(ShoppingMallHighlightsProcessor.class));

        //眼镜
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(GLASSES_CATEGORY, applicationContext.getBean(GlassesHighlightV2Processor.class));
        // 教育在线团购
        List<Integer> eduOnlineCategoryIds = getEduOnlineCategoryIds();
        for (Integer categoryId : eduOnlineCategoryIds) {
            HIGHLIGHTS_PROCESSOR_V2_MAP.put(categoryId, applicationContext.getBean(EduDealHighlightsProcessor.class));
        }
        // 种植牙
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(IMPLANT_CATEGORY, applicationContext.getBean(DentalImplantCheckProcessor.class));

        // 团建聚会（新增二级分类）
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(GROUP_BUILDING_CATEGORY, applicationContext.getBean(GroupBuildingHighlightsV2Processor.class));
        //妇产科
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(GYNECOLOGY_CATEGORY, applicationContext.getBean(GynecologyHighlightsV2Processor.class));
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(OBSTETRICAL_CATEGORY, applicationContext.getBean(GynecologyHighlightsV2Processor.class));
        //局改团详
        HIGHLIGHTS_PROCESSOR_MAP.put(REASSURED_REPAIR_CATEGORY,applicationContext.getBean(ReassuredRepairDealHighlightsProcessor.class));
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(REASSURED_REPAIR_CATEGORY,applicationContext.getBean(ReassuredRepairDealHighlightsProcessor.class));
        //保洁
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(LIFE_CLEAR_CATEGORY, applicationContext.getBean(LifeClearHighlightsProcessor.class));
        //月子中心房型套餐
        HIGHLIGHTS_PROCESSOR_MAP.put(CARE_CENTENR_HOUSE_STYLE, applicationContext.getBean(CareCenterHouseStyleHighlightsProcessor.class));
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(CARE_CENTENR_HOUSE_STYLE, applicationContext.getBean(CareCenterHouseStyleHighlightsProcessor.class));
        HIGHLIGHTS_PROCESSOR_V2_MAP.put(WEDDING_MAKEUP_STYLE, applicationContext.getBean(WeddingMakeUpStyleProcessor.class));
    }

    private List<Integer> getEduOnlineCategoryIds() {
        try {
            Map<String, List> eduOnlineCategoryId2Services = Lion.getMap(LionConstants.APP_KEY,
                    LionConstants.EDU_ONLINE_DEAL_TYPE_CONFIG, List.class, Collections.emptyMap());
            if (MapUtils.isEmpty(eduOnlineCategoryId2Services)) {
                return Lists.newArrayList();
            }
            return eduOnlineCategoryId2Services.keySet().stream()
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("获取教育在线类团购分类失败", e);
        }
        return Lists.newArrayList();
    }
}
