package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.atmosphere;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-01-03
 * @desc 团详页头图氛围
 */
@Data
@MobileDo(id = 0x5161)
public class HeadPicAtmosphere implements Serializable {

    @FieldDoc(description = "顶部图")
    @MobileDo.MobileField(key = 0x4034)
    private ImageVO topImage;

    @FieldDoc(description = "背景图")
    @MobileDo.MobileField(key = 0x3dc6)
    private ImageVO backgroundImage;
}