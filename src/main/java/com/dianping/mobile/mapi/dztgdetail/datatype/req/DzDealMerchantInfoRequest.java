package com.dianping.mobile.mapi.dztgdetail.datatype.req;

import com.dianping.mobile.framework.annotation.MobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.DecryptedField;
import lombok.Data;

import java.io.Serializable;

@TypeDoc(description = "团单SKU选择模块请求参数")
@MobileRequest
public class DzDealMerchantInfoRequest implements IMobileRequest, Serializable {
    /**
     * 门店id
     */
    @MobileRequest.Param(name = "shopid")
    private String shopid;

    /**
     * 团单id
     */
    @MobileRequest.Param(name = "dealid")
    private String dealid;


    public String getShopId() {
        return shopid;
    }

    public String getDealId() {
        return dealid;
    }

    public void setShopId(String shopid) {
        this.shopid = shopid;
    }

    public void setDealId(String dealid) {
        this.dealid = dealid;
    }
}