package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.cat.Cat;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.merchantcard.timescard.exposure.TimesCardNavigationService;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import com.sankuai.merchantcard.timescard.exposure.res.QueryDealGroupCardBarSummaryRequest;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Future;

@Component
public class TimesCardWrapper extends AbsWrapper {

    @Resource(name = "timesCardNavigationFuture")
    private TimesCardNavigationService timesCardNavigationService;

    @Deprecated
    public Future preTimesCards(int id, int shopId, int code, long userId) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.biz.wrapper.TimesCardWrapper.preTimesCards(int,int,int,long)");
        if (id <= 0) {
            return null;
        }
        try {
            QueryDealGroupCardBarSummaryRequest request = new QueryDealGroupCardBarSummaryRequest();
            request.setShopId(shopId);
            request.setPlatform(code);
            request.setDealGroupId(id);
            request.setUserId(userId);
            timesCardNavigationService.loadDealGroupCardBarSummary(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            logger.error("timesCardServiceFuture.queryTimesCards error", e);
        }
        return null;
    }

    public Future preTimesCardsV2(int id, long shopId, int code, long userId) {
        if (id <= 0) {
            return null;
        }
        try {
            QueryDealGroupCardBarSummaryRequest request = new QueryDealGroupCardBarSummaryRequest();
            request.setLongShopId(shopId);
            request.setPlatform(code);
            request.setDealGroupId(id);
            request.setUserId(userId);
            timesCardNavigationService.loadDealGroupCardBarSummary(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            logger.error("timesCardServiceFuture.queryTimesCards error", e);
        }
        return null;
    }

    public CardSummaryBarDTO queryTimesCard(Future future) {
        CardResponse<CardSummaryBarDTO> resp = getFutureResult(future);
        return resp != null && resp.isSuccess() ? resp.getData() : null;
    }
}
