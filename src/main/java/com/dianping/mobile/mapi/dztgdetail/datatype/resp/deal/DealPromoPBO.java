package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;


import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

@TypeDoc(description = "到综团单优惠数据模型")
@MobileDo(id = 0x8b45)
public class DealPromoPBO implements Serializable {

    @FieldDoc(description = "优惠描述")
    @MobileField(key = 0xcc07)
    private String promoDesc;

    @FieldDoc(description = "优惠ID")
    @MobileField(key = 0x271b)
    private String promoId;

    public String getPromoDesc() {
        return promoDesc;
    }

    public void setPromoDesc(String promoDesc) {
        this.promoDesc = promoDesc;
    }

    public String getPromoId() {
        return promoId;
    }

    public void setPromoId(String promoId) {
        this.promoId = promoId;
    }
}
