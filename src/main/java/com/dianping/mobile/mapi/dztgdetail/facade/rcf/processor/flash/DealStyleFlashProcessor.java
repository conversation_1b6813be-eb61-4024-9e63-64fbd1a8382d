package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.flash;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsFlashDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.service.cellarservice.DealStyleCacheBizService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FlashDealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealStyleBO;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @create 2024/10/17 17:08
 */
public class DealStyleFlashProcessor extends AbsFlashDealProcessor {

    @Resource
    DealStyleCacheBizService dealStyleCacheBizService;

    @Override
    public void prepare(FlashDealCtx ctx) {
        String key = dealStyleCacheBizService.buildKey(ctx.getDealflashReq());
        Future future = dealStyleCacheBizService.getCacheValueFuture(key);
        ctx.getFutureCtx().setDealStyleBPOFuture(future);
    }

    @Override
    public void process(FlashDealCtx ctx) {
        try {
            Future future = ctx.getFutureCtx().getDealStyleBPOFuture();
            if (Objects.nonNull(future)){
               DealStyleBO dealStyleBO = (DealStyleBO) dealStyleCacheBizService.getCacheValueResult(future);
               ctx.setDealStyleBO(dealStyleBO);
            }
        } catch (Exception e) {
            logger.error("DealStyleFlashProcessor.process error:", e);
        }
    }
}
