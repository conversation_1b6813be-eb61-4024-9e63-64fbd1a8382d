package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pigeon.remoting.ServiceFactory;
import com.dianping.pigeon.remoting.common.codec.json.SafeJacksonUtils;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.common.domain.GenericType;
import com.dianping.pigeon.remoting.common.service.GenericService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.pigeon.remoting.invoker.config.InvokerConfig;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2025-04-07
 * @desc
 */
@Slf4j
@Component
public class ProductDetailWrapper extends AbsWrapper implements InitializingBean {

    private GenericService service;

    @Override
    public void afterPropertiesSet() throws Exception {
        InvokerConfig<GenericService> invokerConfig = new InvokerConfig<>(
                "com.sankuai.dzshoppingguide.ProductDetailPageTradeModuleSpiService",
                GenericService.class
        );
        invokerConfig.setTimeout(10000);
        invokerConfig.setGeneric(GenericType.JSON_COMMON.getName());
        invokerConfig.setCallType(CallMethod.FUTURE.getName());
        service = ServiceFactory.getService(invokerConfig);
    }

    public Future<?> preQueryProductDetailTradeModule(ProductDetailPageRequest request) {
        if (service == null) {
            return null;
        }
        try {
            service.$invoke(
                    "query",
                    Collections.singletonList(ProductDetailPageRequest.class.getName()),
                    Collections.singletonList(SafeJacksonUtils.serialize(request))
            );
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("preQueryProductDetailTradeModule error", e);
            return null;
        }
    }
}
