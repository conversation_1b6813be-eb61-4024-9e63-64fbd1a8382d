package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import java.util.List;

public interface Processor<T> {

    boolean isEnable(T ctx);

    boolean isEnd(T ctx);

    void prepare(T ctx);

    void process(T ctx);

    /**
     * 获取接口可访问端信息
     * @param ctx
     * @return
     */
    List<Integer> getConfigUrlDztgClient(T ctx);

    /**
     * 接口是否支持端访问
     * @param ctx
     * @param processor
     * @return
     */
    boolean matchDztgClient(T ctx, Processor<T> processor);
}
