package com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/20
 */
@MobileDo(id = 0x2a18)
@Data
public class DealPopover implements Serializable {

    @MobileField(key = 0x8f0c)
    @FieldDoc(description = "浮层类型")
    private String type;

    @MobileField(key = 0x3c48)
    @FieldDoc(description = "浮层ICON")
    private String icon;

    @MobileField(key = 0x451b)
    @FieldDoc(description = "浮层文本")
    private String text;

    @MobileField(key = 0xfebf)
    @FieldDoc(description = "浮层描述")
    private String desc;

    @MobileField(key = 0xc286)
    @FieldDoc(description = "浮层上角标")
    private String superscript;
}
