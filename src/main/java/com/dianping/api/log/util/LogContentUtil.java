package com.dianping.api.log.util;

import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

/**
 * <AUTHOR>
 */
public class LogContentUtil {

    public static LogContent create(String title) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.api.log.util.LogContentUtil.create(java.lang.String)");
        return new LogContent(title);
    }

    public static <T> String printList(List<T> list) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.api.log.util.LogContentUtil.printList(java.util.List)");
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder("size:");
        sb.append(list.size()).append(";");
        for (T t : list) {
            if (t == null) {
                sb.append("null;");
            } else {
                sb.append(ToStringBuilder.reflectionToString(t)).append(";");
            }
        }
        return sb.toString();
    }

    public static class LogContent {
        private boolean commaSeparated = false;
        private boolean firstParam = true;
        private StringBuilder sb;

        public LogContent(String title) {
            sb = new StringBuilder("T:" + title);
        }

        public LogContent commaSeparated() {
            this.commaSeparated = true;
            return this;
        }

        public LogContent addParam(String key, Object value) {
            if (commaSeparated) {
                if (!firstParam) {
                    sb.append(", ");
                }
                sb.append(key);
                sb.append('=');
                sb.append(value);
            } else {
                if (!firstParam) {
                    sb.append('&');
                }
                sb.append(key);
                sb.append('=');
                sb.append(value);
            }
            firstParam = false;
            return this;
        }

        public String toString() {
            return sb.toString();
        }
    }

}
