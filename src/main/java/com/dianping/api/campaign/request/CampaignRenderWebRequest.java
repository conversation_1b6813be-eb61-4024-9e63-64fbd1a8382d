package com.dianping.api.campaign.request;


import com.dianping.api.campaign.dto.UserWebInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


@Data
public class CampaignRenderWebRequest implements Serializable {

    private UserWebInfo userInfo;

    private List<String> campaignKeys;

    private Long sessionBeginTime;

    private Long sessionEndTime;

    private String uuid;

    /**
     * 前端页面Id
     */
    private String pageId;
    /**
     * 前端组件id
     */
    private String moduleId;

    /**
     * 发券平台  美团APP 8 点评APP 64 微信小程序 128
     */
    private Integer issuePlatform;

    /**
     * 活动场景
     */
    private int campaignScene;

    /**
     * 活动对应膨后业务map，key 为活动秘钥，value为nibBiz
     * https://km.sankuai.com/collabpage/**********
     */
    private Map<String, String> campaignBizMap;

    /**
     * 神券参数
     */
    private Map<String, String> mmcParams;
}
