package com.dianping.api.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description CouponPageSourceEnum
 * <AUTHOR>
 * @Date 2024/1/26 14:45
 */
@Getter
public enum CouponPageSourceEnum {

    /**
     * 未知
     */
    UNKNOWN(0, "未知"),
    BEAUTY_POI_DETAIL(1, "丽人POI详情页"),
    MEDICAL_POI_DETAIL(2, "医疗POI详情页");

    private final Integer code;

    private final String desc;

    CouponPageSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CouponPageSourceEnum getByName(String name) {
        for (CouponPageSourceEnum value : values()) {
            if (StringUtils.equals(name, value.name())) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
