/**
 * 
 */
package com.dianping.api.common.entity;

import java.io.Serializable;

import com.dianping.api.domain.Enum.CoordType;

/**
 * <AUTHOR>
 *
 */
public class Coord implements Serializable {

	public Coord() {
		
	}
	
	public Coord(CoordType coordType, double lat, double lng, int accuracy, long time) {
		this.coordType = coordType;
		this.lat = lat;
		this.lng = lng;
		this.accuracy = accuracy;
		this.time = time;
	}
	
	public Coord(String coordStr, CoordType coordType) { // coordStrFromat = LAT,LNG@ACCURACY,TIME
		boolean illegal = true;
		if(coordStr != null && !"".equals(coordStr)) {
			String[] parts = coordStr.split("\\@");
			if(parts != null && parts.length == 2) {
				String[] before = parts[0].split("\\,");
				String[] end = parts[1].split("\\,");
				if(before != null && before.length == 2 && end != null && end.length == 2) {
					try {
						this.lat = Double.parseDouble(before[0]);
						this.lng = Double.parseDouble(before[1]);
						this.accuracy = Integer.parseInt(end[0]);
						this.time = Long.parseLong(end[1]);
						this.coordType = coordType;
						illegal = false;
					} catch(Exception e) {
					
					}
				}
			}
		}
		
		if(illegal) {
			this.coordType = CoordType.Illegal;
			this.lat = 0;
			this.lng = 0;
			this.accuracy = 0;
			this.time = 0;
		}
	}
	
	public CoordType getCoordType() {
		return coordType;
	}
	public void setCoordType(CoordType coordType) {
		this.coordType = coordType;
	}
	public double getLat() {
		return lat;
	}
	public void setLat(double lat) {
		this.lat = lat;
	}
	public double getLng() {
		return lng;
	}
	public void setLng(double lng) {
		this.lng = lng;
	}
	public int getAccuracy() {
		return accuracy;
	}
	public void setAccuracy(int accuracy) {
		this.accuracy = accuracy;
	}
	public long getTime() {
		return time;
	}
	public void setTime(long time) {
		this.time = time;
	}
	
	@Override
	public String toString() {
		return this.getLat() + "," + this.getLng() + "@" + this.getAccuracy() + "," + this.getTime();
	}

	private static final long serialVersionUID = -8750195150786202384L;
	
	private CoordType coordType;
	private double lat;
	private double lng;
	private int accuracy;
	private long time;
}
