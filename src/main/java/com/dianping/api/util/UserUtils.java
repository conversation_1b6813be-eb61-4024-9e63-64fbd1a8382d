package com.dianping.api.util;

import com.dianping.account.UserAccountService;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.account.utils.util.HttpUtils;
import com.dianping.account.validation.AccountValidationService;
import com.dianping.account.validation.dto.AccountValidationResult;
import com.dianping.api.framework.Controller;
import com.dianping.avatar.log.AvatarLogger;
import com.dianping.avatar.log.AvatarLoggerFactory;
import com.dianping.cat.Cat;
import com.dianping.pay.framework.utils.SpringLocator;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

public class UserUtils {

    private static final AvatarLogger LOG = AvatarLoggerFactory.getLogger(UserUtils.class);

    private static final String BIZ_CONTEXT_USER_ID = "BIZ_CONTEXT_USER_ID";
    private static UserAccountService userAccountService = SpringLocator.getBean(UserAccountService.class);
    private static AccountValidationService accountValidationService = SpringLocator.getBean(AccountValidationService.class); ;

    public static long parseAuthInfo(HttpServletRequest request, Controller controller, boolean loadFromCookie) {
        // 需要在解析ua之后调用
        long tokenUserId = controller.getClient().isMtClient()
                ? parseMtToken(request, controller)
                : parseToken(request, controller);
        if (tokenUserId > 0) {
            controller.setUserID(tokenUserId);
            return tokenUserId;
        } else if (loadFromCookie) {
            long cookieUserId = parseCookie(request);
            controller.setUserID(cookieUserId);
            return cookieUserId;
        } else {
            return 0;
        }
    }

    private static long parseToken(HttpServletRequest request, Controller controller) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.util.UserUtils.parseToken(javax.servlet.http.HttpServletRequest,com.dianping.api.framework.Controller)");
        String token = request.getHeader("pragma-token");
        if (isNullToken(token)) {
            token = controller.getParametersMap().getString("token");
            if (isNullToken(token)) {
                token = null;
            }
        }

        String newToken = request.getHeader("pragma-newtoken");
        if (isNullToken(newToken)) {
            newToken = controller.getParametersMap().getString("newtoken");
            if (isNullToken(newToken)) {
                newToken = null;
            }
        }

        AccountValidationResult validateResult = null;
        try {
            if(accountValidationService != null) {
                if(StringUtils.isNotBlank(newToken)) {
                    // 优先解析newtoken，新版本都有
                    validateResult = accountValidationService.validateUniDper(newToken, controller.getDpId());
                } else if(StringUtils.isNotBlank(token)) {
                    // 再尝试解析token，老版本只有token，慢慢废弃token
                    // 优先使用header中param-token，如没有则使用url中token参数
                    validateResult = accountValidationService.validateUniDper(token, controller.getDpId());
                }
            }
        } catch (Throwable t) {
            LOG.error(t.toString(), t);
        }
        if(validateResult != null) {
            if(validateResult.isValid()) {
                return validateResult.getUserId();
            } else {
                LOG.error(String.format("validate newtoken failed[%s][%s][%s][%s]", token, newToken, validateResult.getUserId(), validateResult.getCode()));
            }
        }
        return 0;
    }

    public static long parseMtToken(HttpServletRequest request, Controller controller) {
        Map<String, String> params = new HashMap<String, String>();
        params.put("visitid", HttpUtils.getVisitId(request));

        //美团token是base64
        try {
            String token = request.getHeader("pragma-token");
            if (isNullToken(token)) {
                token = controller.getParametersMap().getString("token");
                if (isNullToken(token)) {
                    token = null;
                }
            }

            VirtualBindUserInfoDTO user = userAccountService.loadUserByToken(token, controller.getUserIP(), params);
            if (user != null){
                controller.setUserID(user.getMtid());
                Cat.logEvent("AccountValidationFilter-mttoken", "Valid");
                return user.getMtid();
            } else {
                Cat.logEvent("AccountValidationFilter-mttoken", "Invalid");
            }
        } catch (Exception e) {
            Cat.logError("AccountValidationFilter-mttoken",e);
        }
        return 0;
    }

    private static long parseCookie(HttpServletRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.api.util.UserUtils.parseCookie(javax.servlet.http.HttpServletRequest)");
        String userId = (String) request.getAttribute(BIZ_CONTEXT_USER_ID);
        long returnId = NumberUtils.toLong(userId, 0);
        LOG.info("cookieUserId=" + returnId);
        return returnId;
    }

    private static boolean isNullToken(String token) {
        return StringUtils.isBlank(token) || token.equals("(null)") || token.equals("null");
    }
}
