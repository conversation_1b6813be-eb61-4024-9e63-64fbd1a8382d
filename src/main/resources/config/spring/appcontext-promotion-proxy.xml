<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="
    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

    <!-- 优惠代理服务 -->
    <bean id="promotionProxyService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.nibmktproxy.queryclient.proxy.PromotionProxyService"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mpmktproxy.query"/>
        <property name="timeout" value="300"/>
        <property name="async" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <!-- thrift连接线程池 -->
    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="promotionProxyServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="timeout" value="300"/>
        <property name="appKey" value="com.sankuai.dzu.tpbase.dztgdetailweb"/>
        <property name="remoteAppkey" value="com.sankuai.mpmktproxy.query"/>
        <property name="serviceInterface"
                  value="com.sankuai.nibmktproxy.queryclient.proxy.PromotionProxyService"/>
        <property name="remoteServerPort" value="8410"/>  <!--端口-->
        <property name="retryRequest" value="false"/>
        <property name="async" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="idGen" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.inf.leaf.thrift.IDGen"/> <!--leaf的 接口名 -->
        <property name="appKey" value="om.sankuai.dzu.tpbase.dztgdetailweb"/> <!-- 本地appkey，请自行配置 -->
        <property name="remoteAppkey" value="com.sankuai.leaf.service.common"/>  <!-- leaf Appkey  -->
        <property name="timeout" value="500"/> <!--超时时间为500ms-->
        <property name="nettyIO" value="true"/> <!--开启Netty IO-->
    </bean>

</beans>