<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="
    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
    http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">

    <bean id="dealService" class="com.dianping.pay.api.service.impl.DealServiceImpl" />
    <bean id="recommendService" class="com.dianping.service.mobile.recommend.impl.RecommendServiceImpl">
        <property name="storeUrls" value="${tuangou-api-mobile.recommend.storeUrls}"/>
    </bean>

    <bean id="lionConfigService" class="com.dianping.pay.api.service.impl.LionConfigService">
        <property name="isRebindEnabled"><value>${tuangou-api-mobile.rebind.enabled}</value></property>
        <property name="unipayStatus"><value>${tuangou-api-mobile.paychannel.unipay.status}</value></property>
        <property name="umpayStatus"><value>${tuangou-api-mobile.paychannel.umpay.status}</value></property>
        <property name="tenpayStatus"><value>${tuangou-api-mobile.paychannel.tenpay.status}</value></property>
        <property name="weixinStatus"><value>${tuangou-api-mobile.paychannel.weixin.status}</value></property>
        <property name="isHotRegionEnabled"><value>${tuangou-api-mobile.deallist.hotregion.enabled}</value></property>
        <property name="isRefundToBalanceEnabled"><value>${tuangou-api-mobile.refund.to.balance.enabled}</value></property>
        <property name="isShaiDanEnabled"><value>${tuangou-api-mobile.orderlist.shaidan.enabled}</value></property>
        <property name="creditCardPayChannel"><value>${tuangou-api-mobile.paychannel.creditcard}</value></property>
        <property name="isWebLoginEnabled"><value>${tuangou-api-mobile.login.web.enabled}</value></property>
        <property name="isBatchTicketEnabled"><value>${tuangou-api-mobile.batch.ticket.enabled}</value></property>
        <property name="fiveYuanOffer"><value>${tuangou-api-mobile.five.yuan.special.offer}</value></property>
        <!--<property name="voucherOfferBeginTime"><value>${tuangou-event.gq2013.3rd.begindate}</value></property>-->
        <!--<property name="voucherOfferEndTime"><value>${tuangou-event.gq2013.3rd.enddate}</value></property>-->
        <property name="voucherOfferCityIds"><value>${tuangou-event.gq2013.citylist}</value></property>
        <property name="weiFangCinemaOffer"><value>${tuangou-api-mobile.offer.weifang.cinema}</value></property>
        <property name="weiFangOfferDealGroupIds"><value>${tuangou-api-mobile.offer.weifang.cinema.ids}</value></property>
    </bean>

</beans>
