package com.sankuai.dzviewscene.productshelf.vu.biz.data;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DealItemData {

    private int itemId;

    private String title;

    //头图
    private PicAreaVO pic;

    private String salePrice;

    private String marketPrice;

    private DzSimpleButtonVO btn;

    /**
     * 销量
     */
    private int saleNum;

    /**
     * 适用班型
     */
    private String suitableClass;
    /**
     * 适用年龄段，如：幼儿、青少、成人
     */
    private String suitableAgeGroup;

    /**
     * 适用年龄，如：2-4岁
     */
    private String eduSuitableAge;

    private String serviceType;

    private String categoryName;

    private List<SkuItemDto> skuItemDtoList;

    private Map<String, Object> productExtraMap;

}
