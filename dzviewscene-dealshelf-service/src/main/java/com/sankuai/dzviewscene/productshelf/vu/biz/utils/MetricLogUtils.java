package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/21 5:03 下午
 */
public class MetricLogUtils {
    private static final String METRIC_LOG_PLATFORM_MT = "mt";
    private static final String METRIC_LOG_PLATFORM_DP = "dp";

    /**
     * 结果不为空Metric打点
     *
     * @param businessKey 业务指标KEY
     * @param platform 平台类型
     * @param client 客户端类型
     * @param predicate 结果是否为空判断
     */
    public static void logMetric(String businessKey, int platform, String client, ResultPredicate predicate) {
        try {
            if(!predicate.isEmpty()) {
                Cat.logMetricForCount(businessKey,buildMetricTags(platform,client));
            }
        } catch (Exception e){
            /*静默*/
        }
    }

    private static Map<String, String> buildMetricTags(int platform, String client) {
        return new HashMap<String, String>(){{
            put("channel",buildChannelTagValue(platform,client));
        }};
    }

    private static String buildChannelTagValue(int platform, String client) {
        String platform_s = platform == VCClientTypeEnum.DP_APP.getCode()?METRIC_LOG_PLATFORM_DP:METRIC_LOG_PLATFORM_MT;
        return String.format("%s_%s", platform_s, client);
    }
}
