package com.sankuai.dzviewscene.productshelf.vu.enums;

/**
 * Created by wangxinyuan02 on 2020/7/9.
 */
public enum SortRuleTypeEnum {
    XN(1, "XN", "每隔X个插一个"),
    Y_XN(2, "Y+XN", "第Y个插一个后每隔X个插一个"),
    TOP_Y_XN(3, "TopY+XN", "置顶Y个后再每隔X个插一个"),
    NO_SORT(4, "NoSort", "不排序"),
    FIXED(5, "Fixed", "指定位置插"),
    ;

    SortRuleTypeEnum(int code, String text, String desc) {
        this.code = code;
        this.text = text;
        this.desc = desc;
    }

    public int code;
    public String text;
    public String desc;
}
