package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.specialTag;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils.SpecialTagsUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils.colorFF4B10;
import static com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils.colorFFF1EC;

@Component
public class VocationalEduStrategy implements SpecialTagStrategy {

    // 免费试听课时文案key
    private static final String FREE_AUDITION_TAG_DOC = "dealEduFreeAuditionTagDocument";
    // 预约留资数文案key
    private static final String LEAD_SALES_TAG_DOC = "dealEduLeadSalesTagDocument";
    // 其他标签key
    private static final String OTHER_TAG = "attr_eduClassLocationNum";

    @Override
    public String getName() {
        return VocationalEduStrategy.class.getSimpleName();
    }

    @Override
    public String getStrategyDesc() {
        return "职业教育定制特色标签";
    }

    @Override
    public List<ShelfTagVO> build(SpecialTagBuildReq req) {
        if (Objects.isNull(req.getProductM())) {
            return null;
        }
        ProductM productM = req.getProductM();
        List<ShelfTagVO> tagVOS = Lists.newArrayList();
        // 免费试听课时的标签
        String eduFreeAuditionTag = productM.getAttr(FREE_AUDITION_TAG_DOC);
        // 预约留资数量的标签
        String eduLeadSalesTag = productM.getAttr(LEAD_SALES_TAG_DOC);
        if (StringUtils.isNotBlank(eduFreeAuditionTag) && StringUtils.isNotBlank(eduLeadSalesTag)) {
            tagVOS.add(SpecialTagsUtils.buildShelfTagVO(eduFreeAuditionTag+"·"+eduLeadSalesTag, colorFFF1EC, colorFF4B10));
        }
        // 其他标签
        String otherTag = productM.getAttr(OTHER_TAG);
        if (StringUtils.isNotBlank(otherTag)) {
            tagVOS.add(SpecialTagsUtils.buildShelfTagVO(otherTag, colorFFF1EC, colorFF4B10));
        }
        return tagVOS;
    }
}
