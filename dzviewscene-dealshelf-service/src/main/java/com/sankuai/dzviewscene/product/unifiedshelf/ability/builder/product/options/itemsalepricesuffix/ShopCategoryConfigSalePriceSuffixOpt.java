package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepricesuffix;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePriceSuffixVP;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfigUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@VPointOption(name = "根据商户类目类配置固定的价格后缀文案",
        description = "根据商户类目类配置固定的价格后缀文案",
        code = ShopCategoryConfigSalePriceSuffixOpt.CODE
)
public class ShopCategoryConfigSalePriceSuffixOpt extends UnifiedShelfSalePriceSuffixVP<ShopCategoryConfigSalePriceSuffixOpt.Config> {

    public static final String CODE = "ShopCategoryConfigSalePriceSuffixOpt";

    @Override
    public String compute(ActivityCxt context, Param param, ShopCategoryConfigSalePriceSuffixOpt.Config config) {
        String categoryConfigText = ShopCategoryConfigUtils.getHitConfig(context, config.getBackCategory2Text(), String.class);
        return StringUtils.defaultIfEmpty(categoryConfigText, config.getDefaultText());
    }

    @VPointCfg
    @Data
    public static class Config {
        private ShopCategoryConfig backCategory2Text;
        private String defaultText = "新人专享";
    }
}