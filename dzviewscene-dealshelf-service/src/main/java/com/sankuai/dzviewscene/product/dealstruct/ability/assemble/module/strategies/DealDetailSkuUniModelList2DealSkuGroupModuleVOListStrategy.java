package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/29 8:32 下午
 */
@Component("dealDetailSkuUniModelList2DealSkuGroupModuleVOListStrategy")
public class DealDetailSkuUniModelList2DealSkuGroupModuleVOListStrategy implements ModuleStrategy {

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        String abilityCode = DealDetailSkuGroupsBuilder.CODE;
        List<DealDetailSkuUniModel> dealSkusGroupsList = activityCxt.getSource(abilityCode);
        List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS = convertDealDetailSkuUniModelList2DealSkuGroupModuleVOList(dealSkusGroupsList);
        return buildDealDetailModuleVO(dealSkuGroupModuleVOS);
    }

    private List<DealSkuGroupModuleVO> convertDealDetailSkuUniModelList2DealSkuGroupModuleVOList(List<DealDetailSkuUniModel> dealSkusGroupsList) {
        if (CollectionUtils.isEmpty(dealSkusGroupsList)) {
            return null;
        }
        DealDetailSkuUniModel dealDetailSkuUniModel = CollectUtils.firstValue(dealSkusGroupsList);
        if (dealDetailSkuUniModel == null) {
            return null;
        }
        List<DealDetailSkuGroupModel> dealDetailSkuGroupModels = new ArrayList<DealDetailSkuGroupModel>() {{
            if (CollectionUtils.isNotEmpty(dealDetailSkuUniModel.getMustGroups())) {
                addAll(dealDetailSkuUniModel.getMustGroups());
            }
            if (CollectionUtils.isNotEmpty(dealDetailSkuUniModel.getOptionGroups())) {
                addAll(dealDetailSkuUniModel.getOptionGroups());
            }
        }};
        if (CollectionUtils.isEmpty(dealDetailSkuGroupModels)) {
            return null;
        }
        return dealDetailSkuGroupModels.stream().map(model -> {
            DealDetailSkuSetModel dealDetailSkuSetModel = CollectUtils.firstValue(model.getSkuSetModels());
            if (dealDetailSkuSetModel == null || CollectionUtils.isEmpty(dealDetailSkuSetModel.getSkuItems())) {
                return null;
            }
            DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
            dealSkuGroupModuleVO.setTitle(model.getTitle());
            List<DealSkuVO> dealSkuVOS = dealDetailSkuSetModel.getSkuItems().stream().map(item -> convertSkuItemModel2DealSkuVO(item)).filter(item -> item != null).collect(Collectors.toList());
            dealSkuGroupModuleVO.setDealSkuList(dealSkuVOS);
            return dealSkuGroupModuleVO;
        }).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private DealSkuVO convertSkuItemModel2DealSkuVO(SkuItemModel skuItemModel) {
        if (skuItemModel == null) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(skuItemModel.getName());
        dealSkuVO.setPrice(skuItemModel.getPrice());
        dealSkuVO.setCopies(skuItemModel.getCopies());
        if (CollectionUtils.isEmpty(skuItemModel.getAttrItems())) {
            return dealSkuVO;
        }
        List<DealSkuItemVO> items = skuItemModel.getAttrItems().stream().map(item -> convertSkuAttrModel2DealSkuItemVO(item)).filter(item -> item != null).collect(Collectors.toList());
        dealSkuVO.setItems(items);
        return dealSkuVO;
    }

    private DealSkuItemVO convertSkuAttrModel2DealSkuItemVO(SkuAttrModel skuAttrModel) {
        if (skuAttrModel == null) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(skuAttrModel.getName());
        if (skuAttrModel.getValue() != null && StringUtils.isNotEmpty(skuAttrModel.getValue().getDoc())) {
            dealSkuItemVO.setValue(skuAttrModel.getValue().getDoc());
            dealSkuItemVO.setType(0);
            return dealSkuItemVO;
        }
        List<SkuAttrAttrItemVO> valueAttrs = JsonCodec.converseList(skuAttrModel.getValueAttrsExtValue(), SkuAttrAttrItemVO.class);
        if (CollectionUtils.isNotEmpty(valueAttrs)) {
            dealSkuItemVO.setValueAttrs(valueAttrs);
            dealSkuItemVO.setType(2);
            return dealSkuItemVO;
        }
        List<PicItemVO> picItemVOS = JsonCodec.converseList(skuAttrModel.getPicItemVOSExtValue(), PicItemVO.class);
        dealSkuItemVO.setPicValues(picItemVOS);
        dealSkuItemVO.setType(1);
        return dealSkuItemVO;
    }

    private DealDetailModuleVO buildDealDetailModuleVO(List<DealSkuGroupModuleVO> skuGroupsModel1) {
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setSkuGroupsModel1(skuGroupsModel1);
        return dealDetailModuleVO;
    }
}
