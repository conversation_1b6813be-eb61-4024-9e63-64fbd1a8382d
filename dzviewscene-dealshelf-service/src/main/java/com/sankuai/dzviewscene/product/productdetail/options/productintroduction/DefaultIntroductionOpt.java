package com.sankuai.dzviewscene.product.productdetail.options.productintroduction;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.vpoints.DetailIntroductionVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import lombok.Data;

@VPointOption(name = "默认-商品详情模块选项", description = "适用于无需商品详情定制的场景", code = DefaultIntroductionOpt.CODE, isDefault = true)
public class DefaultIntroductionOpt extends DetailIntroductionVP<DefaultIntroductionOpt.Cfg> {
    public static final String CODE = "DefaultIntroductionOpt";

    @Override
    public Content compute(ActivityCxt context, Param param, Cfg cfg) {
        return null;
    }


    @Data
    @VPointCfg
    public class Cfg {
    }
}