package com.sankuai.dzviewscene.product.shelf.ability.list.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.list.ShelfProductListBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * Created by float on 2021/8/21.
 */
@VPoint(name = "商品头图", description = "商品头图, 支持配置",code = PicAreaVP.CODE, ability = ShelfProductListBuilder.CODE)
public abstract class PicAreaVP<T> extends PmfVPoint<PicAreaVO, PicAreaVP.Param, T> {

    public static final String CODE = "PicAreaVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }

}
