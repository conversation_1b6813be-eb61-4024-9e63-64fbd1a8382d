package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic;

import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.shelf.utils.UnifiedShelfPicUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPicVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

@VPointOption(
        name = "双列商卡头图",
        description = "双列商卡头图",
        code = UnifiedDoubleColumnPicOpt.CODE)
public class UnifiedDoubleColumnPicOpt extends UnifiedShelfItemPicVP<UnifiedDoubleColumnPicOpt.Config> {

    private static final int SCALE_PIC_WIDTH = 640;

    private static final int SCALE_PIC_HEIGHT = 360;

    public static final String CODE = "UnifiedDoubleColumnPicOpt";

    @Override
    public PictureModel compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (Objects.isNull(productM) || StringUtils.isEmpty(param.getProductM().getPicUrl())) {
            return null;
        }
        String picUrl = productM.getPicUrl();
        picUrl = UnifiedShelfPicUtils.toHttpsUrl(picUrl, getPicWidth(config), getPicHeight(config), PictureURLBuilders.ScaleType.Cut);
        PictureModel pictureModel = new PictureModel();
        pictureModel.setPicUrl(picUrl);
        pictureModel.setAspectRadio(getAspectRadio(config));
        return pictureModel;
    }

    private double getAspectRadio(Config config) {
        double result = (double) getPicWidth(config) / getPicHeight(config);
        return Math.round(result * 1000) / 1000.0;
    }

    private int getPicWidth(Config config){
        return config.getPicWidth() > 0 ? config.getPicWidth() : SCALE_PIC_WIDTH;
    }

    private int getPicHeight(Config config){
        return config.getPicHeight() > 0 ? config.getPicHeight() : SCALE_PIC_HEIGHT;
    }

    @VPointCfg
    @Data
    public static class Config {

        private int picWidth;

        private int picHeight;
    }
}
