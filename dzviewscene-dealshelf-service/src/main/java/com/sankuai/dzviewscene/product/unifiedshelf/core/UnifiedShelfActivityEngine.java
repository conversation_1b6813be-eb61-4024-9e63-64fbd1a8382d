package com.sankuai.dzviewscene.product.unifiedshelf.core;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.pmf.PmfActivityEngine;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivityCtxBuilder;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.product.shelf.activity.product.ProductShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.product.ProductShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.shelf.framework.*;
import com.sankuai.dzviewscene.shelf.framework.monitor.TraceElement;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
public class UnifiedShelfActivityEngine {

    private static final Logger LOGGER = LoggerFactory.getLogger(UnifiedShelfActivityEngine.class);
    private static final String CAT_TYPE = UnifiedShelfActivityEngine.class.getSimpleName();

    @Resource
    private PmfActivityEngine pmfActivityEngine;

    private Map<String, String> space2ActivityCode = buildSpace2ActivityCode();

    private Map<String, String> buildSpace2ActivityCode() {
        Map<String, String> result = Maps.newHashMap();
        result.put(DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY, DealShelfActivity.CODE);
        result.put(UnifiedShelfActivityCtxBuilder.UNIFIED_SHELF_SPACE_KEY, UnifiedShelfActivity.CODE);
        result.put(ProductShelfActivityCtxBuilder.PRODUCT_SHELF_SPACE_KEY, ProductShelfActivity.CODE);
        result.put(DealDetailActivityCtxBuilder.DEAL_DETAIL_SPACE_KEY, DealDetailActivity.CODE);
        result.put(DealDetailActivityCtxBuilder.DEAL_DETAIL_STANDARD_SERVICE_SPACE_KEY, DealDetailActivity.CODE);
        result.put(DealDetailActivityCtxBuilder.HEALTH_EXAMINATION_DEAL_DETAIL_ITEM_DETAIL_SPACE_KEY, DealDetailActivity.CODE);
        result.put(DealShelfActivityCtxBuilder.IM_DEAL_SHELF_SPACE_KEY,DealShelfActivity.CODE);
        result.put(DealShelfActivityCtxBuilder.SEARCH_DEAL_SHELF,DealShelfActivity.CODE);
        return result;
    }

    public <T> ActivityResponse<T> execute(ActivityRequest activityRequest) {
        if (activityRequest == null) {
            throw new IllegalArgumentException("activityRequest 不能为空");
        }
        com.sankuai.athena.viewscene.framework.ActivityRequest request = toPMFRequest(activityRequest);
        com.sankuai.athena.viewscene.framework.ActivityResponse<T> execute = pmfActivityEngine.execute(request);
        ActivityResponse<T> activityResponse = new ActivityResponse<>();
        activityResponse.setResult(execute.getResult());
        activityResponse.setTraceElements(convertTraceElements(execute.getTraceElements()));
        activityResponse.setExecuteError(execute.getExecuteError());
        if (StringUtils.isEmpty(execute.getSceneCode())) {
            Cat.logEvent(CAT_TYPE,"MissSceneCode");
            LOGGER.error("请求未命中SceneCode,req:{}", JsonCodec.encode(activityRequest));
        }
        return activityResponse;
    }

    private com.sankuai.athena.viewscene.framework.ActivityRequest toPMFRequest(ActivityRequest activityRequest){
        com.sankuai.athena.viewscene.framework.ActivityRequest request = new com.sankuai.athena.viewscene.framework.ActivityRequest();
        String spaceKey = (String)activityRequest.getParams().get("spaceKey");
        String activityCode = space2ActivityCode.get(spaceKey);
        Cat.logEvent("PmfEngine", activityCode);
        request.setActivityCode(activityCode);
        request.setParams(activityRequest.getParams());
        request.setStartTime(activityRequest.getStartTime());
        return request;
    }

    private List<TraceElement> convertTraceElements(List elements) {
        List<TraceElement> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(elements)) {
            return result;
        }
        for (Object element : elements) {
            if (element instanceof TraceElement) {
                result.add((TraceElement) element);
            }
            if (element instanceof com.sankuai.athena.viewscene.framework.monitor.TraceElement) {
                com.sankuai.athena.viewscene.framework.monitor.TraceElement item = (com.sankuai.athena.viewscene.framework.monitor.TraceElement) element;
                TraceElement traceElement = new TraceElement();
                traceElement.setType(item.getType());
                traceElement.setName(item.getName());
                traceElement.setParam(item.getParam());
                traceElement.setResult(item.getResult());
                traceElement.setLatency(item.getLatency());
                traceElement.setThrowable(item.getThrowable());
                result.add(traceElement);
            }

        }
        return result;
    }
}
