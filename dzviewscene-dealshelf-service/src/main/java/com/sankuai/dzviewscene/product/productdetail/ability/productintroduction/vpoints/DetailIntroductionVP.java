package com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.DetailIntroductionBuilder;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "详情页-商品介绍变化点", description = "用于详情页商品介绍需要定制的场景", code = DetailIntroductionVP.CODE, ability = DetailIntroductionBuilder.CODE)
public abstract class DetailIntroductionVP <T> extends PmfVPoint<Content, DetailIntroductionVP.Param, T> {
    public static final String CODE = "DetailIntroduction";


    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<ProductM> productMs;
    }
}
