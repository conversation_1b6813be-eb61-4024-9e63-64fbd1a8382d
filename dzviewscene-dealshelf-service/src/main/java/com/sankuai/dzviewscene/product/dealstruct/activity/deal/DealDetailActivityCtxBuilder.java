package com.sankuai.dzviewscene.product.dealstruct.activity.deal;

import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.ActivityRequest;
import com.sankuai.athena.viewscene.framework.annotation.ContextBuilder;
import com.sankuai.athena.viewscene.framework.core.IContextBuilder;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/22 5:29 下午
 */
@ContextBuilder(activityCode = DealDetailActivity.CODE, name = "团购详情上下文构造器")
public class DealDetailActivityCtxBuilder implements IContextBuilder {

    public static final String SPACE_KEY = "spaceKey";

    public static final String DEAL_DETAIL_SPACE_KEY = "deal_detail_modular_component";

    public static final String DEAL_DETAIL_STANDARD_SERVICE_SPACE_KEY = "deal_detail_standard_service";

    public static final String HEALTH_EXAMINATION_DEAL_DETAIL_ITEM_DETAIL_SPACE_KEY = "health_examination_deal_detail_item_detail";
    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public ActivityCxt build(ActivityRequest activityRequest) {
        ActivityContext activityContext = new ActivityContext();
        addSpaceKeyIfNeed(activityRequest, activityContext);
        activityContext.setParameters(activityRequest.getParams());
        addPlatformParam(activityContext);

        addLaunchParam(activityContext);
        return ActivityCtxtUtils.toActivityCtx(activityContext);
    }

    private void addSpaceKeyIfNeed(ActivityRequest activityRequest, ActivityContext activityContext) {
        String spaceKey = (String)activityRequest.getParams().get(SPACE_KEY);
        //如果是标准服务模块 已有spaceKey 不处理
        if(StringUtils.isNotEmpty(spaceKey) && (DEAL_DETAIL_STANDARD_SERVICE_SPACE_KEY.equals(spaceKey) || HEALTH_EXAMINATION_DEAL_DETAIL_ITEM_DETAIL_SPACE_KEY.equals(spaceKey))) {
           return;
        }
        activityContext.getParameters().put(SPACE_KEY, DEAL_DETAIL_SPACE_KEY);
    }

    private void addLaunchParam(ActivityContext activityContext) {
        int dealCategoryId = activityContext.getParam(ProductDetailActivityConstants.Params.dealCategoryId);
        activityContext.addParam("dealCategory", dealCategoryId);
    }

    private void addPlatformParam(ActivityContext activityContext) {
        // 点评平台
        if (!PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.platform))) {
            activityContext.addParam(ProductDetailActivityConstants.Params.dpCityId, ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.dpCityId));
            int dealId = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.productId);
            activityContext.addParam(ProductDetailActivityConstants.Params.dealCategoryId, Optional.ofNullable(getDpDealCategoryId(dealId)).orElse(0));
            return ;
        }
        // 美团平台
        CompletableFuture<Integer> dpCityIdFuture = compositeAtomService.getDpCityIdByMt(ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.mtCityId));
        dpCityIdFuture.thenAccept(dpCityId -> {
            activityContext.addParam(ProductDetailActivityConstants.Params.mtCityId, ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.mtCityId));
            activityContext.addParam(ProductDetailActivityConstants.Params.dpCityId, dpCityId == null ? 0 : dpCityId);
            int dealId = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.productId);
            activityContext.addParam(ProductDetailActivityConstants.Params.dealCategoryId, Optional.ofNullable(getMtDealCategoryId(dealId)).orElse(0));
        }).join();
    }

    private Integer getDpDealCategoryId(int dealId) {
        if(dealId == 0) {
            return null;
        }
        CompletableFuture<Map<Integer, Integer>> dealIdCategoryIdMapFuture = compositeAtomService.batchGetDealIdCategoryIdMap(Lists.newArrayList(dealId));
        return dealIdCategoryIdMapFuture.thenApply(dealIdCategoryIdMap -> {
            if(MapUtils.isEmpty(dealIdCategoryIdMap)) {
                return null;
            }
            return dealIdCategoryIdMap.get(dealId);
        }).join();
    }

    private Integer getMtDealCategoryId(int dealId) {
        if(dealId == 0) {
            return null;
        }
        CompletableFuture<List<IdMapper>> idMappersCompletableFuture = compositeAtomService.batchGetDealIdByMtId(Lists.newArrayList(dealId));
        Map<Integer, Integer> dealId2CategoryIdMap = idMappersCompletableFuture.thenCompose( idMappers -> {
            if(CollectionUtils.isEmpty(idMappers)) {
                return CompletableFuture.completedFuture(null);
            }
            List<Integer> dpDealIds = idMappers.stream().map(idMapper -> idMapper.getDpDealGroupID()).collect(Collectors.toList());
            return compositeAtomService.batchGetDealIdCategoryIdMap(dpDealIds);
        }).join();
        return CollectUtils.firstValue(dealId2CategoryIdMap);
    }
}
