package com.sankuai.dzviewscene.product.shelf.options.builder.floors.defaultshownum;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemAreaDefaultShowNumVP;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@VPointOption(name = "商品区-ktv默认展示数量",
        description = "支持配置控制展示默认数量的逻辑，也可根据城市选择展示数量",
        code = "KtvDefaultShowNumOpt")
public class KtvDefaultShowNumOpt extends ItemAreaDefaultShowNumVP<KtvDefaultShowNumOpt.Config> {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.joy.ktv.dp.HighLineCity")
    private List<Integer> dpHighLineCity;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.joy.ktv.mt.HighLineCity")
    private List<Integer> mtHighLineCity;

    private static final Integer DP = 1;
    private static final Integer MT = 2;

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        // 增加团购次卡的显示个数展示
        if (containsTimesDeal(param.getProducts()) && config.getTimesDefaultShowNum() != 0) {
            return config.getTimesDefaultShowNum();
        }
        // 1. 获取平台类型
        Integer platform = context.getParam("platform");
        if (platform == null) {
            return config.floorDefaultShowNum;
        }
        // 2. 根据城市类型选择默认展示数量
        if (DP.equals(platform)) {
            Integer dpCityId = context.getParam("dpCityId");
            if (CollectionUtils.isNotEmpty(dpHighLineCity) && dpHighLineCity.contains(dpCityId)) {
                return config.highLineCityShowNum;
            } else {
                return config.floorDefaultShowNum;
            }
        } else if (MT.equals(platform)) {
            Integer mtCityId = context.getParam("mtCityId");
            if (CollectionUtils.isNotEmpty(mtHighLineCity) && mtHighLineCity.contains(mtCityId)) {
                return config.highLineCityShowNum;
            } else {
                return config.floorDefaultShowNum;
            }
        }
        return config.floorDefaultShowNum;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 货架楼层默认展示商品数
         */
        private int floorDefaultShowNum = 4;

        private int highLineCityShowNum = 2;

        /**
         * 含团购次卡时 默认展示数
         */
        private int timesDefaultShowNum = 5;
    }
}
