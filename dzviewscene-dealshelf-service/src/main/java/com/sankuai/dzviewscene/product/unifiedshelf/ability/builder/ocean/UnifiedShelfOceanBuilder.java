package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.data.Converters;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfOceanEntryVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfOceanVO;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler;
import com.sankuai.dzviewscene.product.shelf.ability.builder.ocean.ShelfOceanFieldEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.bean.UnifiedShelfOceanConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.vp.UnifiedShelfOceanLabsVP;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Ability(code = UnifiedShelfOceanBuilder.CODE,
        name = "VO-Ocean 打点构造",
        description = "Ocean 打点构造",
        activities = {UnifiedShelfActivity.CODE},
        dependency = {ShelfMainDataAssembler.CODE}
)
public class UnifiedShelfOceanBuilder extends PmfAbility<ShelfOceanVO, UnifiedShelfOceanBuilder.Request, UnifiedShelfOceanBuilder.Config> {

    public static final String CODE = "UnifiedShelfOceanBuilder";

    @Resource
    private UnifiedShelfOceanConfig unifiedShelfOceanConfig;

    @Override
    public CompletableFuture<ShelfOceanVO> build(ActivityCxt ctx, Request request, Config config) {
        return CompletableFuture.completedFuture(getNewEngineRes(ctx, request, config));
    }

    private ShelfOceanVO getNewEngineRes(ActivityCxt ctx, Request request, Config config) {
        ShelfGroupM shelfGroupM = ctx.getSource(ShelfMainDataAssembler.CODE);
        if (shelfGroupM == null) {
            return null;
        }
        //1. 查找打点配置对象
        ShelfOceanVO shelfOceanVO = getRawShelfOceanVO(config, request.getPlatform());
        if (shelfOceanVO == null) {
            return null;
        }
        //2. 填充 AB
        paddingAbTests(shelfOceanVO, shelfGroupM.getDouHus());
        //3. 填充labs字段
        paddingLabs(shelfOceanVO, ctx, request);
        return shelfOceanVO;
    }

    private ShelfOceanVO getRawShelfOceanVO(Config config, int platform) {
        UnifiedShelfOceanConfig.ShelfOceanConfigBean configBean = new UnifiedShelfOceanConfig.ShelfOceanConfigBean();
        configBean.setMtOcean(config.getMtOcean());
        configBean.setDpOcean(config.getDpOcean());
        return unifiedShelfOceanConfig.getOcean(configBean, platform);
    }

    /**************** ABTest 填充 ****************/

    private void paddingAbTests(ShelfOceanVO shelfOcean, List<DouHuM> multiDouHu) {
        // 填充多个ab字段
        if (CollectionUtils.isNotEmpty(multiDouHu)) {
            List<String> abTestList = Converters.newPropertyExtractorConverter("abtest").convert(multiDouHu);
            paddingAbTest(JsonCodec.encode(abTestList), shelfOcean);
        }
    }

    private void paddingAbTest(String abTest, ShelfOceanVO shelfOceanVO) {
        paddingAbTest2Entry(abTest, shelfOceanVO.getChildrenFilterBar());
        paddingAbTest2Entry(abTest, shelfOceanVO.getFilterBar());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getFilterPicker());
        paddingAbTest2Entry(abTest, shelfOceanVO.getMore());
        paddingAbTest2Entry(abTest, shelfOceanVO.getProductItem());
        paddingAbTest2Entry(abTest, shelfOceanVO.getProductItemTag());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getProductBottomTag());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getSearchIcon());
        paddingAbTest2Entry(abTest, shelfOceanVO.getWholeShelf());
        paddingAbTest2Entry(abTest, shelfOceanVO.getBuyBtn());
        paddingAbTest2Entry(abTest, shelfOceanVO.getChildrenCollapse());
        paddingAbTest2Entry(abTest, shelfOceanVO.getSpuItem());
        paddingAbTest2Entry(abTest, shelfOceanVO.getSkuItem());
        paddingAbTest2Entry(abTest, shelfOceanVO.getChildrenMore());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getBookingBtn());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getSecondBookingBtn());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getJoyCard());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getSecondProductItem());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getMoreFilter());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getSecondMore());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getProductArea());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getSecondProductArea());
        paddingAbTest2Entry(abTest, shelfOceanVO.getPromoPopView());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getPromoPopViewButton());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getPromoPopViewClose());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getPinSession());
        paddingAbTest2Entry(abTest, shelfOceanVO.getActivityPromoPopView());
        paddingAbTest2Entry(abTest, shelfOceanVO.getActivityPromoPopViewButton());
        paddingAbTest2Entry(abTest, shelfOceanVO.getActivityPromoPopViewClose());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getProductItemPromo());
//        paddingAbTest2Entry(abTest, shelfOceanVO.getProductAreaTips());
        paddingAbTest2Entry(abTest, shelfOceanVO.getPageView());
        paddingAbTest2Entry(abTest, shelfOceanVO.getReserveMindBar());
        paddingAbTest2Entry(abTest, shelfOceanVO.getReserveMindSupernatant());
    }

    private void paddingAbTest2Entry(String abTest, ShelfOceanEntryVO entryVO) {
        if (entryVO == null) {
            return;
        }
        entryVO.setAbtest(abTest);
    }

    /**************** labs 填充 ****************/

    private void paddingLabs(ShelfOceanVO shelfOceanVO, ActivityCxt cxt, Request request) {
        Map<String, String> fieldLabsMap = getLabsMap(cxt, request);
        paddingLabs2ShelfOcean(shelfOceanVO, fieldLabsMap);
    }

    private Map<String, String> getLabsMap(ActivityCxt activityCxt, Request request) {
        try {
            UnifiedShelfOceanLabsVP<?> oceanLabsVP = findVPoint(activityCxt, UnifiedShelfOceanLabsVP.CODE);
            return oceanLabsVP.execute(activityCxt,
                    UnifiedShelfOceanLabsVP.Param.builder()
                            .dpPoiId(PoiIdUtil.getDpPoiIdL(request)).mtPoiId(PoiIdUtil.getMtPoiIdL(request))
                            .shop(request.getCtxShop()).platform(request.getPlatform())
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return new HashMap<>();
        }
    }

    private void paddingLabs2ShelfOcean(ShelfOceanVO shelfOceanVO, Map<String, String> labs) {
        if (MapUtils.isEmpty(labs)) {
            return;
        }
        paddingLabs2Entry(shelfOceanVO.getActivityPromoPopView(), ShelfOceanFieldEnum.ActivityPromoPopView, labs);
        paddingLabs2Entry(shelfOceanVO.getActivityPromoPopViewButton(), ShelfOceanFieldEnum.ActivityPromoPopViewButton, labs);
        paddingLabs2Entry(shelfOceanVO.getActivityPromoPopViewClose(), ShelfOceanFieldEnum.ActivityPromoPopViewClose, labs);
//        paddingLabs2Entry(shelfOceanVO.getPromoPopViewButton(), ShelfOceanFieldEnum.PromoPopViewButton, labs);
        paddingLabs2Entry(shelfOceanVO.getPromoPopView(), ShelfOceanFieldEnum.PromoPopView, labs);
//        paddingLabs2Entry(shelfOceanVO.getSecondBookingBtn(), ShelfOceanFieldEnum.SecondBookingBtn, labs);
//        paddingLabs2Entry(shelfOceanVO.getProductArea(), ShelfOceanFieldEnum.ProductArea, labs);
//        paddingLabs2Entry(shelfOceanVO.getSecondProductArea(), ShelfOceanFieldEnum.SecondProductArea, labs);
        paddingLabs2Entry(shelfOceanVO.getChildrenFilterBar(), ShelfOceanFieldEnum.ChildrenFilterBar, labs);
        paddingLabs2Entry(shelfOceanVO.getFilterBar(), ShelfOceanFieldEnum.FilterBar, labs);
//        paddingLabs2Entry(shelfOceanVO.getFilterPicker(), ShelfOceanFieldEnum.FilterPicker, labs);
        paddingLabs2Entry(shelfOceanVO.getMore(), ShelfOceanFieldEnum.More, labs);
        paddingLabs2Entry(shelfOceanVO.getProductItem(), ShelfOceanFieldEnum.ProductItem, labs);
        paddingLabs2Entry(shelfOceanVO.getProductItemTag(), ShelfOceanFieldEnum.ProductItemTag, labs);
//        paddingLabs2Entry(shelfOceanVO.getSearchIcon(), ShelfOceanFieldEnum.SearchIcon, labs);
        paddingLabs2Entry(shelfOceanVO.getWholeShelf(), ShelfOceanFieldEnum.WholeShelf, labs);
        paddingLabs2Entry(shelfOceanVO.getBuyBtn(), ShelfOceanFieldEnum.BuyBtn, labs);
        paddingLabs2Entry(shelfOceanVO.getChildrenCollapse(), ShelfOceanFieldEnum.ChildrenCollapse, labs);
        paddingLabs2Entry(shelfOceanVO.getChildrenMore(), ShelfOceanFieldEnum.ChildrenMore, labs);
        paddingLabs2Entry(shelfOceanVO.getSpuItem(), ShelfOceanFieldEnum.SpuItem, labs);
        paddingLabs2Entry(shelfOceanVO.getSkuItem(), ShelfOceanFieldEnum.SkuItem, labs);
//        paddingLabs2Entry(shelfOceanVO.getBookingBtn(), ShelfOceanFieldEnum.BookingBtn, labs);
//        paddingLabs2Entry(shelfOceanVO.getJoyCard(), ShelfOceanFieldEnum.JoyCard, labs);
//        paddingLabs2Entry(shelfOceanVO.getSecondProductItem(), ShelfOceanFieldEnum.SecondProductItem, labs);
//        paddingLabs2Entry(shelfOceanVO.getMoreFilter(), ShelfOceanFieldEnum.MoreFilter, labs);
//        paddingLabs2Entry(shelfOceanVO.getPinSession(), ShelfOceanFieldEnum.PinSession, labs);
//        paddingLabs2Entry(shelfOceanVO.getSecondMore(), ShelfOceanFieldEnum.SecondMore, labs);
//        paddingLabs2Entry(shelfOceanVO.getPromoPopViewClose(), ShelfOceanFieldEnum.PromoPopViewClose, labs);
//        paddingLabs2Entry(shelfOceanVO.getProductItemPromo(), ShelfOceanFieldEnum.ProductItemPromo, labs);
//        paddingLabs2Entry(shelfOceanVO.getProductAreaTips(), ShelfOceanFieldEnum.ProductAreaTips, labs);
        paddingLabs2Entry(shelfOceanVO.getPageView(), ShelfOceanFieldEnum.PageView, labs);
//        paddingLabs2Entry(shelfOceanVO.getExplainLayer(), ShelfOceanFieldEnum.ExplainLayer, labs);
        paddingLabs2Entry(shelfOceanVO.getReserveMindBar(), ShelfOceanFieldEnum.ReserveMindBar, labs);
        paddingLabs2Entry(shelfOceanVO.getReserveMindSupernatant(),ShelfOceanFieldEnum.ReserveMindSupernatant, labs);
    }

    private void paddingLabs2Entry(ShelfOceanEntryVO entry, ShelfOceanFieldEnum fieldEnum, Map<String, String> labsMap) {
        String labs = labsMap.get(fieldEnum.getCode());
        if (StringUtils.isEmpty(labs)) {
            return;
        }
        entry.setLabs(labs);
    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 点评侧打点数据
         */
        private ShelfOceanVO dpOcean;

        /**
         * 美团侧打点数据
         */
        private ShelfOceanVO mtOcean;
    }

    @AbilityRequest
    @Data
    public static class Request {
        /**
         * {@link ShelfActivityConstants.Params#dpPoiId}
         */
        private int dpPoiId;
        private long dpPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#mtPoiId}
         */
        private int mtPoiId;
        private long mtPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#platform}
         */
        private int platform;

        /**
         * {@link ShelfActivityConstants.Ctx#ctxShop}
         */
        private ShopM ctxShop;
    }
}

