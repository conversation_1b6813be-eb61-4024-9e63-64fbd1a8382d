package com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/9 9:15 下午
 */
@MobileDo(id = 0x798b)
public class HealthExaminationItemsGroupVO implements Serializable {
    /**
     * 组名
     */
    @MobileDo.MobileField(key = 0x2a32)
    private String groupName;

    /**
     * 一级检查项列表
     */
    @MobileDo.MobileField(key = 0x1ce9)
    private List<PrimaryExaminationItemVO> primaryExaminationItemList;

    /**
     * 描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public List<PrimaryExaminationItemVO> getPrimaryExaminationItemList() {
        return primaryExaminationItemList;
    }

    public void setPrimaryExaminationItemList(
            List<PrimaryExaminationItemVO> primaryExaminationItemList) {
        this.primaryExaminationItemList = primaryExaminationItemList;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
