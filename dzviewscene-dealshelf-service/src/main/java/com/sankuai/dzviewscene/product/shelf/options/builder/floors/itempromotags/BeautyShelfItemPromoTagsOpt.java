package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/14
 */
@VPointOption(name = "丽人-优惠标签",
        description = "包括次卡和折扣标签",
        code = "BeautyShelfItemPromoTagsOpt")
public class BeautyShelfItemPromoTagsOpt extends ItemPromoTagsVP<BeautyShelfItemPromoTagsOpt.Config> {

    private static final BigDecimal TOP_LIMIT = new BigDecimal("9.9");

    private static final BigDecimal BOTTOM_LIMIT = new BigDecimal("0.1");

    private static final String TIMES_CARD_TIMES = "times_card_times";

    @Override
    public List<DzPromoVO> compute(ActivityCxt context, Param param, Config config) {
        List<DzPromoVO> allPromoVos = Lists.newArrayList();
        DzPromoVO cardTimes = getCardTimes(param);
        if(cardTimes != null){
            allPromoVos.add(cardTimes);
        }
        DzPromoVO discount = getDiscountTag(param, config);
        if(discount != null){
            allPromoVos.add(discount);
        }
        return allPromoVos;
    }

    private DzPromoVO getCardTimes(Param param){
        String timesStr = ProductMAttrUtils.getAttrValue(param.getProductM(),TIMES_CARD_TIMES);
        if(StringUtils.isEmpty(timesStr)){
            return null;
        }
        int times = NumberUtils.toInt(timesStr, 0);
        if(times <= 0){
            return null;
        }
        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName(String.format("共%s次", times));
        dzPromoVO.setShowType(0);
        dzPromoVO.setTextColor("#FF6000");
        //售卖价后
        dzPromoVO.setPosition(1);
        return dzPromoVO;
    }

    private DzPromoVO getDiscountTag(Param param, Config config){
        BigDecimal salePrice = getSalePrice(param);
        BigDecimal marketOrBasicPrice = getMarketOrBasicPrice(param, config);
        if (salePrice == null || marketOrBasicPrice == null || Objects.equals(salePrice, BigDecimal.ZERO)
                || Objects.equals(marketOrBasicPrice, BigDecimal.ZERO)) {
            return null;
        }
        BigDecimal discountVal = getDiscountVal(salePrice, marketOrBasicPrice, config);
        if (discountVal.compareTo(getTopLimit(config)) > 0) {
            return null;
        }
        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName(String.format("%s折", discountVal));
        dzPromoVO.setShowType(1);
        //售卖价后，市场价前
        dzPromoVO.setPosition(1);
        addDiscountTag(param.getProductM(), dzPromoVO.getName());
        return dzPromoVO;
    }

    private BigDecimal getSalePrice(Param param) {
        if (StringUtils.isEmpty(param.getSalePrice())) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(param.getSalePrice());
    }

    private BigDecimal getMarketOrBasicPrice(Param param, Config config) {
        //次卡返回市场价
        if(param.getProductM().getProductType()== ProductTypeEnum.TIME_CARD.getType()){
            return StringUtils.isNotEmpty(param.getProductM().getMarketPrice()) ?
                    NumberUtils.toScaledBigDecimal(param.getProductM().getMarketPrice()) : BigDecimal.ZERO;
        }
        if (config != null && config.useBasicPrice) {
            return param.getProductM().getBasePrice();
        }
        if (StringUtils.isEmpty(param.getProductM().getMarketPrice())) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(param.getProductM().getMarketPrice());
    }

    private BigDecimal getTopLimit(Config config) {
        if (config == null || BigDecimal.valueOf(config.getMaxDiscount()).compareTo(BigDecimal.ZERO) <= 0) {
            return TOP_LIMIT;
        }
        return BigDecimal.valueOf(config.getMaxDiscount());
    }

    private BigDecimal getDiscountVal(BigDecimal salePrice, BigDecimal marketOrBasicPrice, Config config) {
        //原始值
        BigDecimal discountVal = salePrice.divide(marketOrBasicPrice, 3, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(10))
                .setScale(1, RoundingMode.HALF_UP);
        BigDecimal finalDiscountVal = discountVal.compareTo(BOTTOM_LIMIT) > 0 ? discountVal : BOTTOM_LIMIT;
        if (config != null && Boolean.TRUE.equals(config.needRounding)) {
            finalDiscountVal = finalDiscountVal.stripTrailingZeros();
        }
        return finalDiscountVal;
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 最大折扣
         */
        private double maxDiscount;

        /**
         * 是否使用基础价格做折扣计算，默认false：使用门市价，true:使用基础价格（没有减任何优惠）
         */
        private boolean useBasicPrice;

        /**
         * 是否整数取整，例如7.0折取整为7折
         */
        private boolean needRounding;

    }
}

