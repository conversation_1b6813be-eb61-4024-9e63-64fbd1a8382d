package com.sankuai.dzviewscene.product.filterlist.utils;

import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

public class LifeCleanUtils {

    //禁用自营商品标签->非自营
    public static final String DISABLE_SELF_OWN_PRODUCT = "否";

    public static final String SELF_OWN_PRODUCT_VALUE = "self_own_product";

    public static boolean isLifeClearSelf(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return false;
        }
        String standardProductAttrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, SELF_OWN_PRODUCT_VALUE);
        if (StringUtils.isEmpty(standardProductAttrValue) || DISABLE_SELF_OWN_PRODUCT.equals(standardProductAttrValue)) {
            return false;
        }
        return true;
    }
}
