package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;

public class SpecialTagsUtils {

    public static ShelfTagVO buildSimpleTagVO(String tagName) {
        ShelfTagVO dzTagVO = new ShelfTagVO();
        RichLabelModel richLabelModel = new RichLabelModel();
        richLabelModel.setText(tagName);
        richLabelModel.setMultiText(Lists.newArrayList(tagName));
        dzTagVO.setText(richLabelModel);
        return dzTagVO;
    }

    public static ShelfTagVO buildCommonTagVO(String tagName) {
        ShelfTagVO dzTagVO = new ShelfTagVO();
        RichLabelModel richLabelModel = new RichLabelModel();
        richLabelModel.setText(tagName);
        richLabelModel.setMultiText(Lists.newArrayList(tagName));
        richLabelModel.setTextColor(ColorUtils.color555555);
        richLabelModel.setBackgroundColor(ColorUtils.colorF4F4F4);
        dzTagVO.setText(richLabelModel);
        return dzTagVO;
    }

    public static ShelfTagVO buildShelfTagVO(String tagName, String textColor) {
        RichLabelModel richLabelText = new RichLabelModel();
        richLabelText.setText(tagName);
        richLabelText.setMultiText(Lists.newArrayList(tagName));
        richLabelText.setTextColor(textColor);
        richLabelText.setStyle(RichLabelStyleEnum.BUBBLE.getType());
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setText(richLabelText);
        return shelfTagVO;
    }

    public static ShelfTagVO buildShelfTagVO(String tagName, String background, String textColor) {
        RichLabelModel richLabelText = new RichLabelModel();
        richLabelText.setText(tagName);
        richLabelText.setMultiText(Lists.newArrayList(tagName));
        richLabelText.setTextColor(textColor);
        richLabelText.setBackgroundColor(background);
        richLabelText.setStyle(RichLabelStyleEnum.BUBBLE.getType());
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setText(richLabelText);
        return shelfTagVO;
    }

    public static ShelfTagVO buildShelfTagVO(String tagName, String background, String textColor, int style) {
        RichLabelModel richLabelText = new RichLabelModel();
        richLabelText.setText(tagName);
        richLabelText.setMultiText(Lists.newArrayList(tagName));
        richLabelText.setTextColor(textColor);
        richLabelText.setBackgroundColor(background);
        richLabelText.setStyle(style);
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setText(richLabelText);
        return shelfTagVO;
    }
}
