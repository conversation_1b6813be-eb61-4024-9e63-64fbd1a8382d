package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListGroupTitleVP;
import lombok.Data;
import org.codehaus.plexus.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "配镜sku列表组标题默认变化点", description = "配镜sku列表组标题默认变化点",code = GlassesSkuListGroupTitleOpt.CODE, isDefault = false)
public class GlassesSkuListGroupTitleOpt extends SkuListGroupTitleVP<GlassesSkuListGroupTitleOpt.Config> {

    public static final String CODE = "GlassesSkuListGroupTitleOpt";

    private static final String DEFAULT_FORMAT = "（%s选%s）";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param.isMustGroup()) {
            return "";
        }
        String format = StringUtils.isEmpty(config.getFormat()) ? DEFAULT_FORMAT : config.getFormat();
        return String.format(format, param.getTotalNum(), param.getOptionalNum());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String format;
    }
}
