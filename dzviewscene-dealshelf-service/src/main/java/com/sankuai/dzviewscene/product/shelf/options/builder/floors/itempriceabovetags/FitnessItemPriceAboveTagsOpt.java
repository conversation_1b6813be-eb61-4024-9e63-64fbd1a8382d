package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempriceabovetags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceAboveTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-08-08 15:54
 */
@VPointOption(name = "榜单标签",
        description = "榜单标签",
        code = "FitnessItemPriceAboveTagsOpt")
public class FitnessItemPriceAboveTagsOpt extends ItemPriceAboveTagsVP<FitnessItemPriceAboveTagsOpt.Config> {

    @Override
    public List<DzTagVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        ProductM productM = param.getProductM();
        String rankLabelName = Optional.ofNullable(productM).map(ProductM::getResourceRank).map(ResourceRankM::getRankName).orElse(null);
        if (StringUtils.isEmpty(rankLabelName) || isHideRankTag(param, config)) {
            return null;
        }
        return buildTags(productM.getResourceRank());
    }

    private boolean isHideRankTag(Param param, Config config) {
        // 命中实验后，不展示榜单标签
        if (Objects.nonNull(config) && CollectionUtils.isNotEmpty(config.getEnableHideRankLabelSkList()) && CollectionUtils.isNotEmpty(param.getDouHuList())) {
            List<String> hitSkList = param.getDouHuList().stream().map(DouHuM::getSk).collect(Collectors.toList());
            hitSkList.retainAll(config.getEnableHideRankLabelSkList());
            return CollectionUtils.isNotEmpty(hitSkList);
        }
        return false;
    }

    private List<DzTagVO> buildTags(ResourceRankM resourceRank) {
        List<DzTagVO> tags = Lists.newArrayList();
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setText(resourceRank.getRankName());
        dzTagVO.setBorderRadius(0.0D);
        dzTagVO.setTextColor(ColorUtils.color8E3C12);
        dzTagVO.setHasBorder(false);
        dzTagVO.setName("");
        dzTagVO.setBackground(ColorUtils.colorFFEDDE);
        tags.add(dzTagVO);
        return tags;
    }


    @VPointCfg
    @Data
    public static class Config {
        // 展示榜单标签实验组配置
        private List<String> enableHideRankLabelSkList;
    }
}
