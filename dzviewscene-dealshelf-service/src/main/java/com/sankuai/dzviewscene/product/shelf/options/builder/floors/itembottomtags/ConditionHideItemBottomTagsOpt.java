package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itembottomtags;

import com.dianping.cat.Cat;
import com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.utils.ItemPriceTagUtil;
import com.sankuai.dzviewscene.product.utils.HugeSaveMoneyProductMActivityUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
@VPointOption(name = "条件隐藏拼团次卡标签",
        description = "指定条件下不返回标签",
        code = "ConditionHideItemBottomTagsOpt")
public class ConditionHideItemBottomTagsOpt extends ItemBottomTagsVP<ConditionHideItemBottomTagsOpt.Config> {

    private static final int FONT_SIZE_10 = 10;
    private static final String FONT_COLOR_FF6633 = "#FF6633";

    @Override
    public List<RichLabelVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        if (config.onlyLowestPriceTag) {
            ProductActivityM productActivityM = HugeSaveMoneyProductMActivityUtils.getFirstActivityByHugeSaveMoneyTypeAndExpSks(param.getProductM(), param.getDouHuList());
            if (productActivityM != null) {
                return buildLowestPriceTag(productActivityM.getPriceStrengthTime());
            }
        }
        if (matchBeautyHideCondition(config, param.getProductM())) {
            return null;
        }
        int platform = Optional.ofNullable((Integer) activityCxt.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        return filterEmptyTags(getCardAndPinTags(param.getProductM(), config.showDiscountByNum, platform));
    }

    private List<RichLabelVO> buildLowestPriceTag(int priceStrengthTime) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.shelf.options.builder.floors.itembottomtags.ConditionHideItemBottomTagsOpt.buildLowestPriceTag(int)");
        PriceStrengthTimeEnum priceStrengthTimeEnum = PriceStrengthTimeEnum.findByCode(priceStrengthTime);
        if (priceStrengthTimeEnum == null) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setTextSize(FONT_SIZE_10);
        richLabelVO.setText(String.format("近%s最低", priceStrengthTimeEnum.getDesc()));
        richLabelVO.setBorderColor(FONT_COLOR_FF6633);
        richLabelVO.setTextColor(FONT_COLOR_FF6633);
        richLabelVO.setHasBorder(true);
        return Lists.newArrayList(richLabelVO);
    }

    /**
     * 是否满足丽人的自动隐藏逻辑
     *
     * @return true 满足
     */
    private boolean matchBeautyHideCondition(Config config, ProductM productM) {
        if (!config.isBeautyAutoHide()) {
            return false;
        }
        //1. 为标准单
        if (!ProductMAttrUtils.isStandardDealGroup(productM)) {
            return false;
        }
        //2. 有工作日特惠
        return ItemPriceTagUtil.isShowIdlePromo(productM);
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 丽人隐藏逻辑：标准单 且有工作日特惠
         */
        private boolean beautyAutoHide;

        /**
         * 只展示最低价标签条件，true：满足条件的时候展示最低价标签，false:不做相关逻辑判断
         */
        private boolean onlyLowestPriceTag;

        /**
         * 是否展示多买多减
         */
        private boolean showDiscountByNum;
    }
}
