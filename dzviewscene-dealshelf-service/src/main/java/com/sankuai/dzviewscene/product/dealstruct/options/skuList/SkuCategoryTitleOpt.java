package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "取sku类别作为sku标题的变化点", description = "取sku类别作为sku标题的变化点",code = SkuCategoryTitleOpt.CODE, isDefault = false)
public class SkuCategoryTitleOpt extends SkuTitleVP<SkuCategoryTitleOpt.Config> {

    public static final String CODE = "SkuCategoryTitleOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null) {
            return null;
        }
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        String skuCategory = getSkuCategory(skuItemDto, productCategories);
        if (CollectionUtils.isNotEmpty(config.skuCategoryShowingSkuName) && config.getSkuCategoryShowingSkuName().contains(skuCategory)) {
            return skuItemDto.getName();
        }
        return skuCategory;
    }

    private String getSkuCategory(SkuItemDto skuItemDto, List<ProductSkuCategoryModel> productCategories) {
        if (skuItemDto == null || CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel productSkuCategoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() != null && skuItemDto.getProductCategory() == category.getProductCategoryId()).findFirst().orElse(null);
        if (productSkuCategoryModel == null) {
            return null;
        }
        return productSkuCategoryModel.getCnName();
    }

    @Data
    @VPointCfg
    public static class Config {
        //取sku名称作为sku标题展示的sku类（有些类别的sku不宜以sku类别作为标题进行展示，例如自定义类别，此时一般取sku name作为标题）
        private List<String> skuCategoryShowingSkuName;
    }
}
