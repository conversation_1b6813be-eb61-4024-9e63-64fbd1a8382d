package com.sankuai.dzviewscene.product.shelf.ability.shelf;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.shelf.ability.list.ShelfProductListBuilder;
import com.sankuai.dzviewscene.product.shelf.ability.maintitle.ShelfProductMainTitleBuilder;
import com.sankuai.dzviewscene.product.shelf.ability.ocean.ProductShelfOceanBuilder;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.product.ProductShelfActivity;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.ShelfCommonOcean;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float on 2021/8/21.
 */
@Ability(
        code = ShelfResponseBuilder.CODE,
        name = "货架展示模型组装",
        description = "货架展示模型组装能力, 将加工好的子模型组装成货架整体展示模型。主要依赖参数为构造的货架、货架主标题、打点数据，输出的结果为最终展示模型。",
        activities = {
                DealShelfActivity.CODE,
                ProductShelfActivity.CODE
        },
        dependency = {
                ShelfProductListBuilder.CODE, // 货架
                ShelfProductMainTitleBuilder.CODE, // 货架主标题
                ProductShelfOceanBuilder.CODE //打点数据
        }
)
public class ShelfResponseBuilder extends PmfAbility<DzShelfResponseVO, ShelfResponseParam, ShelfResponseCfg> {

    public static final String CODE = "shelfResponseBuilder";


    @Override
    public CompletableFuture<DzShelfResponseVO> build(ActivityCxt activityCxt, ShelfResponseParam shelfResponseParam, ShelfResponseCfg shelfResponseCfg) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.shelf.ability.shelf.ShelfResponseBuilder.build(ActivityCxt,ShelfResponseParam,ShelfResponseCfg)");
        //1.转换上下文
        ActivityContext activityContext = ActivityCtxtUtils.toActivityContext(activityCxt);
        //2.获取上游节点执行结果
        MainTitleComponentVO mainTitleComponentVO = activityCxt.getSource(ShelfProductMainTitleBuilder.CODE);
        List<FilterBtnIdAndProAreasVO> filterBtnIdAndProAreasVOs = activityCxt.getSource(ShelfProductListBuilder.CODE);
        AntiCrawlerUtils.hideProductKeyInfo(filterBtnIdAndProAreasVOs, activityContext);
        ShelfOceanVO shelfOceanVO = activityCxt.getSource(ProductShelfOceanBuilder.CODE);

        //3.组装货架展示模型
        DzShelfResponseVO dzShelfResponseVO = new DzShelfResponseVO();
        dzShelfResponseVO.setShelfComponent(buildDzShelfComponentVO(activityContext, findFirstFilterComponent(null), new DzShelfCardBarVO(), mainTitleComponentVO, filterBtnIdAndProAreasVOs, shelfResponseCfg));
        dzShelfResponseVO.setOcean(ShelfCommonOcean.paddingCommonOcean(shelfOceanVO, dzShelfResponseVO.getShelfComponent(), activityContext));
        return CompletableFuture.completedFuture(dzShelfResponseVO);
    }

    ////////////////////////////////////////////////复用原货架活动逻辑, 没改////////////////////////////////////////////////////////////

    private DzShelfComponentVO buildDzShelfComponentVO(ActivityContext activityContext,
                                                       FilterComponentVO filterComponentVO,
                                                       DzShelfCardBarVO cardBarVO,
                                                       MainTitleComponentVO mainTitleComponentVO,
                                                       List<FilterBtnIdAndProAreasVO> shelfFloors,
                                                       ShelfResponseCfg shelfResponseCfg) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.shelf.ability.shelf.ShelfResponseBuilder.buildDzShelfComponentVO(ActivityContext,FilterComponentVO,DzShelfCardBarVO,MainTitleComponentVO,List,ShelfResponseCfg)");
        DzShelfComponentVO dzShelfComponentVO = new DzShelfComponentVO();
        removeFilterStyle(filterComponentVO);
        dzShelfComponentVO.setFilter(filterComponentVO);
        dzShelfComponentVO.setCardBar(cardBarVO);
        dzShelfComponentVO.setMainTitle(mainTitleComponentVO);
        dzShelfComponentVO.setFilterIdAndProductAreas(shelfFloors);
        dzShelfComponentVO.setShowType(shelfResponseCfg.getShowType());
        dzShelfComponentVO.setCategoryId(getShopCategoryId(activityContext));
        dzShelfComponentVO.setShelfInteractType(ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Style.interactType));
        dzShelfComponentVO.setSceneCode(getSceneCode(activityContext));
        return dzShelfComponentVO;
    }

    private String getSceneCode(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.shelf.ability.shelf.ShelfResponseBuilder.getSceneCode(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        if (activityContext == null) {
            return null;
        }
        return activityContext.getSceneCode();
    }


    private void removeFilterStyle(FilterComponentVO filterComponentVO) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.shelf.ability.shelf.ShelfResponseBuilder.removeFilterStyle(com.sankuai.dzviewscene.productshelf.vu.vo.FilterComponentVO)");
        if (filterComponentVO == null || CollectionUtils.isEmpty(filterComponentVO.getFilterBtns())) {
            return;
        }
        removeFilterStyle(filterComponentVO.getFilterBtns());
    }

    private int getShopCategoryId(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.shelf.ability.shelf.ShelfResponseBuilder.getShopCategoryId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        ShopM shopM = ctx.getParam(ProductDetailActivityConstants.Ctx.ctxShop);
        if (shopM == null) {
            return 0;
        }
        return shopM.getCategory();
    }

    private void removeFilterStyle(List<FilterButtonVO> filterBtns) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.shelf.ability.shelf.ShelfResponseBuilder.removeFilterStyle(java.util.List)");
        if (CollectionUtils.isEmpty(filterBtns)) {
            return;
        }
        filterBtns.forEach(filter -> {
            if (filter == null) {
                return;
            }
            removeRichLabelVOStyle(filter.getTitle());
            removeRichLabelVOStyle(filter.getSelectedTitle());
            removeFilterStyle(filter.getChildren());
        });
    }

    private void removeRichLabelVOStyle(RichLabelVO richLabelVO) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.shelf.ability.shelf.ShelfResponseBuilder.removeRichLabelVOStyle(com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO)");
        if (richLabelVO == null) {
            return;
        }
        if (StringUtils.isEmpty(richLabelVO.getText()) || richLabelVO.getText().equals("玩美季")) {
            return;
        }
        richLabelVO.setTextSize(0);
        richLabelVO.setTextColor(StringUtils.EMPTY);
        richLabelVO.setFontWeight(StringUtils.EMPTY);
    }

    private FilterComponentVO findFirstFilterComponent(Map<String, FilterComponentVO> groupFilterComponents) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.shelf.ability.shelf.ShelfResponseBuilder.findFirstFilterComponent(java.util.Map)");
        if (MapUtils.isEmpty(groupFilterComponents)) {
            return null;
        }
        return groupFilterComponents.values().stream().findFirst().orElse(null);
    }
}
