package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.specialTag;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils.SpecialTagsUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils.*;

@Component
public class RepairOnsiteQuoteStrategy implements SpecialTagStrategy {

    private static final String ATTR_COMPUTER_PROJECT_TYPE = "computer_project_type";

    private static final String ATTR_REPAIR_PROJECT_TYPE = "repair_project_type";

    private static final String MATCH_VALUE_SERVICE_TYPE = "上门费";

    private static final String ATTR_PAY_METHOD = "pay_method";

    private static final String MATCH_VALUE_PAY_METHOD = "2";

    private static final String TAG_NAME = "维修费待师傅上门后报价";

    @Override
    public String getName() {
        return RepairOnsiteQuoteStrategy.class.getSimpleName();
    }

    @Override
    public String getStrategyDesc() {
        return "维修费待师傅上门后报价";
    }

    @Override
    public List<ShelfTagVO> build(SpecialTagBuildReq req) {
        if (Objects.isNull(req.getProductM())) {
            return null;
        }
        if (isMatchPayMethod(req.getProductM()) && isMatchServiceType(req.getProductM())) {
            return Lists.newArrayList(SpecialTagsUtils.buildCommonTagVO(TAG_NAME));
        }
        return null;
    }

    private boolean isMatchServiceType(ProductM productM) {
        String computerProjectType = productM.getAttr(ATTR_COMPUTER_PROJECT_TYPE);
        String repairProjectType = productM.getAttr(ATTR_REPAIR_PROJECT_TYPE);
        return StringUtils.equals(computerProjectType, MATCH_VALUE_SERVICE_TYPE) || StringUtils.equals(repairProjectType, MATCH_VALUE_SERVICE_TYPE);
    }

    private boolean isMatchPayMethod(ProductM productM) {
        String payMethod = productM.getAttr(ATTR_PAY_METHOD);
        return StringUtils.equals(payMethod, MATCH_VALUE_PAY_METHOD);
    }
}
