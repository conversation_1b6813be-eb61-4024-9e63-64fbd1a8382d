package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/6
 */
@VPoint(name = "商品-购买信息", description = "如最近购买、剩余库存等", code = ItemPurchaseVP.CODE, ability = FloorsBuilder.CODE)
public abstract class ItemPurchaseVP<T> extends PmfVPoint<RichLabelVO, ItemPurchaseVP.Param, T> {
    public static final String CODE = "ItemPurchaseVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }
}
