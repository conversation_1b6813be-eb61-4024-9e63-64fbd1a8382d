package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/8/9 01:21
 */
public class OverNightModuleUtils {

    private static final String OVER_NIGHT_ICON = "https://p0.meituan.net/ingee/a26ddbf7ac627d7b2d2761ec53760e691727.png";

    private static final String NEW_OVER_NIGHT_ICON = "https://p0.meituan.net/ingee/39345524d0f1ead891dccaefdef798b86694.png";

    /**
     * 构建免费过夜服务模块
     */
    public static DealSkuVO parseFreeOverNightSkuModule(List<AttrM> dealAttrs, boolean hitNewIcon) {
        try {
            String dealOverNightRule = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "dealOverNightRule");
            if (StringUtils.isEmpty(dealOverNightRule)) {
                return null;
            }
            Map<String, String> overNightRuleMap = JSON.parseObject(dealOverNightRule, new TypeReference<HashMap<String, String>>() {
            });
            if (!overNightRuleMap.containsKey("identityKey") || !"overNightService".equals(overNightRuleMap.get("identityKey"))) {
                return null;
            }
            if (!overNightRuleMap.containsKey("amount") || !overNightRuleMap.containsKey("serviceTitle") || !overNightRuleMap.containsKey("originalPrice")
                    || !overNightRuleMap.containsKey("free") || !overNightRuleMap.containsKey("rule")) {
                return null;
            }
            if (!Boolean.parseBoolean(overNightRuleMap.get("free"))) {
                return null;
            }
            return buildFreeDealSkuVO(overNightRuleMap, hitNewIcon);
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    /**
     * 构建付费过夜服务模块
     */
    public static DealSkuVO parsePayOverNightSkuModule(List<AttrM> dealAttrs, boolean hitNewIcon) {
        try {
            String dealOverNightRule = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "dealOverNightRule");
            if (StringUtils.isEmpty(dealOverNightRule)) {
                return null;
            }
            Map<String, String> overNightRuleMap = JSON.parseObject(dealOverNightRule, new TypeReference<HashMap<String, String>>() {
            });
            if (!overNightRuleMap.containsKey("identityKey") || !"overNightService".equals(overNightRuleMap.get("identityKey"))) {
                return null;
            }
            if (!overNightRuleMap.containsKey("amount") || !overNightRuleMap.containsKey("serviceTitle") || !overNightRuleMap.containsKey("originalPrice")
                    || !overNightRuleMap.containsKey("free") || !overNightRuleMap.containsKey("rule")) {
                return null;
            }
            if (Boolean.parseBoolean(overNightRuleMap.get("free"))) {
                return null;
            }
            return buildPayDealSkuVO(overNightRuleMap, hitNewIcon);
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    private static DealSkuVO buildFreeDealSkuVO(Map<String, String> overNightRuleMap, boolean hitNewIcon) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle("过夜服务");
        dealSkuVO.setSubTitle(getOriginalPrice(overNightRuleMap.get("originalPrice")));
        if (StringUtils.isNotBlank(overNightRuleMap.get("rule"))) {
            DealSkuItemVO skuItemVO = new DealSkuItemVO();
            skuItemVO.setType(0);
            skuItemVO.setName(overNightRuleMap.get("rule"));
            skuItemVO.setIcon(hitNewIcon ? NEW_OVER_NIGHT_ICON : OVER_NIGHT_ICON);
            dealSkuVO.setItems(Lists.newArrayList(skuItemVO));
        }
        return dealSkuVO;
    }

    private static DealSkuVO buildPayDealSkuVO(Map<String, String> overNightRuleMap, boolean hitNewIcon) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle("过夜服务");
        dealSkuVO.setIcon(hitNewIcon ? NEW_OVER_NIGHT_ICON : OVER_NIGHT_ICON);
        dealSkuVO.setPrice(getPrice(overNightRuleMap.get("amount")));
        dealSkuVO.setDesc(overNightRuleMap.get("rule"));
        return dealSkuVO;
    }

    private static String getOriginalPrice(String originalPrice) {
        if (StringUtils.isBlank(originalPrice)) {
            return null;
        }
        return String.format("价值¥%s", originalPrice);
    }

    private static String getPrice(String price) {
        if (StringUtils.isBlank(price)) {
            return null;
        }
        return String.format("+¥%s", price);
    }

}
