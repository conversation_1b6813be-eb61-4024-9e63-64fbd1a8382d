package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module;

import lombok.Data;

import java.util.List;

/**
 * created by zhangzhiyuan04 in 2021/12/14
 */
@Data
public class ModuleItem {

    private String strategyName;

    private String type;

    private String moduleName;

    private String config;

    private List<String> douHuSkList;

    /**
     * 足疗团详特殊场景。餐食为新数据时，需要使用新样式
     */
    private String newType;
}
