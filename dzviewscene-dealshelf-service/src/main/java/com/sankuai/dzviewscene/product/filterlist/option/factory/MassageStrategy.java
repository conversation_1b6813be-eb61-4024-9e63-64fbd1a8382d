package com.sankuai.dzviewscene.product.filterlist.option.factory;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/9 14:27
 */
public interface MassageStrategy {

    /**
     * 时长
     */
    String SERVICE_DURATION_INT = "serviceDurationInt";

    /**
     * 服务部位
     */
    String SERVICE_BODY_RANGE = "serviceBodyRange";

    String BODY_REGION = "bodyRegion";


    /**
     * 服务手法
     */
    String SERVICE_TECHNIQUE = "serviceTechnique";

    /**
     * 特色灸法
     */
    String MOXIBUSTION_METHOD = "moxibustionMethod";


    String getFilterListTitle(SkuItemDto skuItemDto, String serviceType);

    List<Long> getProductCategorys();

    @Getter
    @AllArgsConstructor
    enum ProductCategoryEnum {

        /**
         * 足疗单品
         */
        FOOT_MASSAGE(2104542L),

        /**
         * 推拿/按摩（单品）
         */
        MASSAGE(2104612L),

        /**
         * 精油SPA（单品）
         */
        ESSENTIAL_OIL_SPA(2104613L),

        /**
         * 推拿正骨（单品）
         */
        ULNA(2104614L),

        /**
         * 采耳（单品）
         */
        EAR_PICKING(2104615L),

        /**
         * 艾灸（单品）
         */
        MOXIBUSTION(2104616L),

        /**
         * 拔罐（单品）
         */
        CUP(2104617L),

        /**
         * 刮痧（单品）
         */
        SCRAPING(2104618L),

        /**
         * 头疗（单品）
         */
        HEAD(2105875L),

        /**
         * 足疗（组合套餐）
         */
        COMBINATION_FOOT_MASSAGE(2104656L),

        /**
         * 推拿/按摩（组合套餐）
         */
        COMBINATION_MASSAGE(2104657L),

        /**
         * 精油SPA（组合套餐）
         */
        COMBINATION_ESSENTIAL_OIL_SPA(2104658L),

        /**
         * 推拿正骨（组合套餐）
         */
        COMBINATION_ULNA(2104659L),

        /**
         * 采耳（组合套餐）
         */
        COMBINATION_EAR_PICKING(2104660L),

        /**
         * 艾灸（组合套餐）
         */
        COMBINATION_MOXIBUSTION(2104661L),

        /**
         * 拔罐（组合套餐）
         */
        COMBINATION_CUP(2104662L),

        /**
         * 刮痧（组合套餐）
         */
        COMBINATION_SCRAPING(2104663L),
        ;

        /**
         * 服务项目id
         */
        private final Long productCategoryId;

    }
}
