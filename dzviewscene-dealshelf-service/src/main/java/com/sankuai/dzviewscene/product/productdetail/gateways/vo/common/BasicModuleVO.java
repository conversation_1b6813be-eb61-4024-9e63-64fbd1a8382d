package com.sankuai.dzviewscene.product.productdetail.gateways.vo.common;

import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class BasicModuleVO extends Content {

    /**
     * 标题
     */
    private String title;

    /**
     * 标题前标签
     */
    private String preTitleTag;

    /**
     * 描述
     */
    private String desc;

    /**
     * 头图
     */
    private String headPic;

    /**
     * 图片/视频列表
     */
    private List<ImgVO> imgList;

    /**
     * 销量
     */
    private String sales;

    /**
     * 商品标签
     */
    private List<String> productTags;

    /**
     * 价格信息
     */
    private PriceVO price;

    /**
     * 用于特殊场景扩展
     */
    private Map<String, String> extras;

}
