package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;


@VPointOption(name = "在线教育团单的配套服务AttrVO列表变化点", description = "在线教育团单的配套服务AttrVO列表变化点",code = EduOnlineDealAddServiceVOListOpt.CODE)
@Slf4j
public class EduOnlineDealAddServiceVOListOpt extends DealAttrVOListVP<EduOnlineDealAddServiceVOListOpt.Config> {

    public static final String CODE = "EduOnlineDealAddServiceVOListOpt";

    public static final String ATTR_ADDITIONAL_SERVICE = "additional_service";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = buildDealDetailStructAttrModuleVO(context, param, config);
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOS)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel groupModule = new DealDetailStructAttrModuleGroupModel();
        groupModule.setGroupName(config.getTitle());
        groupModule.setGroupSubtitle(config.getSubtitle());
        groupModule.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        return Lists.newArrayList(groupModule);
    }

    private List<DealDetailStructAttrModuleVO> buildDealDetailStructAttrModuleVO(ActivityCxt context, Param param, Config config) {
        List<String> services = getPhysicalGiftList(param);
        if (CollectionUtils.isEmpty(services)) {
            return null;
        }
        return services.stream()
                .map(service -> {
                    DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
                    dealDetailStructAttrModuleVO.setAttrName(service);
                    dealDetailStructAttrModuleVO.setIcon(config.getIconUrl());
                    return dealDetailStructAttrModuleVO;
                }).collect(Collectors.toList());
    }

    private List<String> getPhysicalGiftList(Param param) {
        String json = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), ATTR_ADDITIONAL_SERVICE);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonCodec.decode(json, new TypeReference<List<String>>() {});
    }

    @Data
    @VPointCfg
    public static class Config {

        private String title = "配套服务";

        private String subtitle;

        private String iconUrl = "https://p0.meituan.net/travelcube/fa6ada0e2bdaa52c6c0bd667a122319f694.png";
    }

}
