package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemjumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemJumpUrlVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@VPointOption(name = "APP端商家小程序跳链",
        description = "APP端商家小程序跳链",
        code = "MiniprogramJumpUrlOpt")
public class MiniprogramJumpUrlOpt extends ItemJumpUrlVP<MiniprogramJumpUrlOpt.Config> {

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (StringUtils.isNotBlank(productM.getAttr(config.getMiniprogramAttrName()))) {
            return productM.getAttr(config.getMiniprogramAttrName());
        } else {
            return productM.getJumpUrl();
        }
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 团购主题透传小程序跳链属性名
         */
        private String miniprogramAttrName = "meidiMiniprogramLink";
    }
}
