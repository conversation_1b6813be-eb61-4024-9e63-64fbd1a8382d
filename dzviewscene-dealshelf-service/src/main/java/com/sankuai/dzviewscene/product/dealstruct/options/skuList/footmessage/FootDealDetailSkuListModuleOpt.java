package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.DealSkuListModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.MassageFreeFoodUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/3 8:24 下午
 */
@VPointOption(name = "足疗团详sku列表组变化点", description = "足疗团详sku列表组变化点", code = FootDealDetailSkuListModuleOpt.CODE)
public class FootDealDetailSkuListModuleOpt extends AbstractFootMessageModuleOpt<FootDealDetailSkuListModuleOpt.Config> {

    public static final String CODE = "FootDealDetailSkuListModuleOpt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        //获取第一个必选组的第一个货数据
        SkuItemDto skuItemDto = DealDetailUtils.extractFirstMustSkuFromDealDetailInfoModel(dealDetailInfoModel);
        List<DealDetailSkuListModuleGroupModel> dealDetailSkuListModuleGroupModels = new ArrayList<>();
        //构造服务流程货列表组
        DealDetailSkuListModuleGroupModel serviceFlowSkuListModule = buildServiceFlowSkuListModule(skuItemDto, dealDetailInfoModel.getDealAttrs());
        dealDetailSkuListModuleGroupModels.add(serviceFlowSkuListModule);
        // 构建过夜模块列表组
        dealDetailSkuListModuleGroupModels.add(DealSkuListModuleUtils.buildOverNightSkuListModule(dealDetailInfoModel.getDealAttrs()));
        //构造免费餐食货列表组
        DealDetailSkuListModuleGroupModel freeChargeMealSkuListModule = buildFreeChargeMealSkuListModule(skuItemDto, config);
        dealDetailSkuListModuleGroupModels.add(freeChargeMealSkuListModule);
        return dealDetailSkuListModuleGroupModels.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 构造服务流程货列表组
     */
    private DealDetailSkuListModuleGroupModel buildServiceFlowSkuListModule(SkuItemDto skuItemDto, List<AttrM> dealAttrs) {
        if (skuItemDto == null) {
            return null;
        }
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "service_type");

        //货名称
        String title = getServiceFlowSkuName(skuItemDto.getAttrItems(), serviceType);
        //货价格
        String price = skuItemDto.getMarketPrice() == null ? null : skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString() + "元";
        //货属性列表
        List<DealSkuItemVO> dealSkuItemList = getServiceItems(skuItemDto.getAttrItems(), skuItemDto.getProductCategory());
        return buildDealDetailSkuListModuleGroupModel("服务流程模块", null, Lists.newArrayList(buildDealSkuVO(title, price, null, dealSkuItemList, null)));
    }

    /**
     * 构造免费餐食货列表组
     */
    private DealDetailSkuListModuleGroupModel buildFreeChargeMealSkuListModule(SkuItemDto skuItemDto, Config config) {
        if (skuItemDto == null) {
            return null;
        }
        // 餐食数据为新数据时 团详始终展示新样式
        if (MassageFreeFoodUtils.FOOD_NEW_DATA.contains(DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "freeFood"))) {
            DealSkuVO dealSkuVO = MassageFreeFoodUtils.parseFreeFood(skuItemDto, false);
            if (dealSkuVO == null) {
                return null;
            }
            return buildDealDetailSkuListModuleGroupModel("免费餐食", null, Lists.newArrayList(dealSkuVO));
        }
        List<FreeMealParseModel> freeMealParseModels = getFreeMealParseModels(skuItemDto.getAttrItems());
        if (CollectionUtils.isEmpty(freeMealParseModels)) {
            return null;
        }
        List<DealSkuVO> dealSkuVOList = freeMealParseModels.stream().map(meal -> convertFreeMeal2DealSkuVO(meal, config.getSkuName2IconMap())).filter(Objects::nonNull).collect(Collectors.toList());
        return buildDealDetailSkuListModuleGroupModel("免费餐食", null, sortFreeChargeMeal(dealSkuVOList, config.getSortedFreeChargeMealSkus()));
    }

    /**
     * 对餐食信息进行排序
     */
    private List<DealSkuVO> sortFreeChargeMeal(List<DealSkuVO> dealSkuVOList, List<String> orderedFreeChargeMealSkus) {
        if (CollectionUtils.isEmpty(orderedFreeChargeMealSkus) || CollectionUtils.isEmpty(dealSkuVOList)) {
            return dealSkuVOList;
        }
        dealSkuVOList.sort(Comparator.comparingInt(o -> orderedFreeChargeMealSkus.indexOf(o.getTitle())));
        return dealSkuVOList;
    }

    private DealSkuVO convertFreeMeal2DealSkuVO(FreeMealParseModel meal, Map<String, String> skuName2IconMap) {
        if (meal == null) {
            return null;
        }
        //货标题
        String title = meal.getFoodType();
        //货份数
        String copies = meal.getUsageNumber();
        if (CollectionUtils.isEmpty(meal.getFoodName())) {
            return buildDealSkuVO(title, null, copies, null, skuName2IconMap.get(title));
        }
        //货属性
        String skuAttr = meal.getFoodName().size() == 1 ? CollectUtils.firstValue(meal.getFoodName()) : String.format("以下%s：\n%s", meal.getUsageRules(), StringUtils.join(meal.getFoodName(), "、"));
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(skuAttr);
        //构造DealSkuVO
        return buildDealSkuVO(title, copies, null, Lists.newArrayList(dealSkuItemVO), skuName2IconMap.get(title));
    }

    /**
     * 从服务项目属性中解析出免费餐食列表
     */
    private List<FreeMealParseModel> getFreeMealParseModels(List<SkuAttrItemDto> skuAttrs) {
        boolean isProvideFreeChargeMeal = ObjectUtils.equals(DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "freeFood"), "提供免费餐食");
        if (!isProvideFreeChargeMeal) {
            return null;
        }
        String freeMealsJsonStr = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "foodContentArray");
        return JsonCodec.converseList(freeMealsJsonStr, FreeMealParseModel.class);
    }


    private DealSkuVO buildDealSkuVO(String title, String price, String copies, List<DealSkuItemVO> dealSkuItemVO, String icon) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setPrice(price);
        dealSkuVO.setCopies(copies);
        dealSkuVO.setIcon(icon);
        dealSkuVO.setItems(dealSkuItemVO);
        return dealSkuVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        private Map<String, String> skuName2IconMap = new HashMap<>();

        private List<String> sortedFreeChargeMealSkus = new ArrayList<>();
    }

    @Data
    private static class FreeMealParseModel {
        private List<String> foodName;
        private String foodType;
        private String usageRules;
        private String usageNumber;
    }
}
