package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuSequenceVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemPriorityVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:45 下午
 */
@VPointOption(name = "Sku元素优先级默认变化点", description = "Sku元素优先级默认变化点",code = DefaultSkuPriorityVPO.CODE, isDefault = true)
public class DefaultSkuPriorityVPO extends SkuItemPriorityVP<DefaultSkuPriorityVPO.Config> {

    public static final String CODE = "DefaultSkuPriorityVPO";

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        return 0;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
