package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo;

import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
public class MassageMemberPromoTagBuildStrategy extends AbstractPriceBottomPromoTagBuildStrategy {

    private static final String MEM_AFTER_PIC = "https://p0.meituan.net/travelcube/b5ef0237b2cb19d166956ce499b6f0bb495.png";

    private static final String ICON_TAG = "会员价";

    @Override
    public String getName() {
        return "足疗会员";
    }

    @Override
    public DzTagVO buildTag(PriceBottomTagBuildReq req) {
        // 会员
        ProductPromoPriceM memberPromoM = ProductMPromoInfoUtils.getMemberPromoPriceM(req.getProductM(), req.getCardM());
        if (Objects.isNull(memberPromoM)) {
            return null;
        }
        // 非会员标签
        ProductPromoPriceM noMemberPromoM = getNoMemberPromoM(req.getProductM().getPromoPrices());
        if (Objects.isNull(noMemberPromoM) || memberPromoM.getPromoPrice().compareTo(noMemberPromoM.getPromoPrice()) < 0) {
            // 会员比其他标签便宜，返回会员标签
            DzTagVO dzTagVO = buildMemberTag(req, memberPromoM);
            promoSimplify(dzTagVO, req, ICON_TAG, true);
            return dzTagVO;
        }
        return null;
    }

    private DzTagVO buildMemberTag(PriceBottomTagBuildReq req, ProductPromoPriceM memberPromoM) {
        DzTagVO memberDzTagVO = buildPromoTagVo(req.getPlatform(), memberPromoM);
        memberDzTagVO.setPrePic(new DzPictureComponentVO(memberPromoM.getIcon(), getPicRadio(req.getPlatform(), req.getCfg())));
        memberDzTagVO.setAfterPic(new DzPictureComponentVO(MEM_AFTER_PIC, 0.875));
        memberDzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(memberPromoM, req.getPopType()));
        memberDzTagVO.setBorderRadius(PlatformUtil.isMT(req.getPlatform()) ? 3 : 1);
        memberDzTagVO.setText(memberDzTagVO.getName());
        return memberDzTagVO;
    }

    private DzTagVO buildPromoTagVo(int platform, ProductPromoPriceM productPromoPriceM) {
        return DzPromoUtils.buildBasicDzTagVOWithColor(platform, productPromoPriceM.getPromoTag(),
                ColorUtils.color8C5819, null, null, "#8E3C12", "#FFEDDE");
    }

    private ProductPromoPriceM getNoMemberPromoM(List<ProductPromoPriceM> promoPrices) {
        List<ProductPromoPriceM> noMemberPriceM = promoPrices.stream()
                .filter(a -> Objects.nonNull(a.getPromoTagType())
                        // 过滤掉会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                        // 过滤掉没有标签
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode()))
                // 过滤掉 使用了会员优惠的Promo
                .filter(a -> !CardPromoUtils.CARD_PROMOS.contains(a.getPromoType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noMemberPriceM)) {
            return null;
        }
        return noMemberPriceM.get(0);
    }
}
