package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemmarketprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemMarketPriceVP;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;

/**
 * @author: created by hang.yu on 2024/6/14 17:38
 */
@VPointOption(name = "多次卡团单的门市价",
        description = "多次卡团单的门市价",
        code = "TimesDealMarketPriceOpt")
public class TimesDealMarketPriceOpt extends ItemMarketPriceVP<Void> {

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        if (!param.getProductM().isTimesDeal()) {
            return param.getProductM().getMarketPrice();
        }
        if (TimesDealUtil.hitTimesStyle1(param.getProductM(), param.getTimesDealSk())) {
            return param.getProductM().getMarketPrice();
        }
        return FloorsBuilderExtAdapter.EMPTY_VALUE;
    }

}
