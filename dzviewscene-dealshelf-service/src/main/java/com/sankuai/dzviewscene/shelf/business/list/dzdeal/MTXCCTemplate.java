package com.sankuai.dzviewscene.shelf.business.list.dzdeal;

import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.list.dzdeal.builder.MTXCCProductListBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.list.ProductListActivity;
import com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExt;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by float.lu on 2020/9/8.
 */
@ActivityTemplate(activityCode = ProductListActivity.ACTIVITY_PRODUCT_LIST_CODE, sceneCode = "dz_apphome_recommend_products", name = "美团小程序到踪商品列表")
public class MTXCCTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.list.dzdeal.MTXCCTemplate.flow()");
        return null;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.list.dzdeal.MTXCCTemplate.extParams(java.util.Map)");
        // 1. 设置召回参数, 代表只召回一组商品, 商品是化妆品
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList("product"));

        // 2. 设置第一组商品参数,
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put("product", new HashMap<String, Object>() {{
                // 2.1 配置召回策略
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.paramGeneralProductsQuery.name());
                // 2.2 配置填充策略
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
                // 2.3 设置第一组商品填充参数
                put(PaddingFetcher.Params.planId, "10000020");
            }});
        }});
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.list.dzdeal.MTXCCTemplate.extContexts(java.util.List)");

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.list.dzdeal.MTXCCTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.list.dzdeal.MTXCCTemplate.extAbilities(java.util.Map)");

    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.list.dzdeal.MTXCCTemplate.extPoints(java.util.Map)");
        // 1. 配置路由扩展实现
        extPoints.put(ProductListBuilderExt.EXT_POINT_PRODUCT_LIST_BUILDER_CODE, MTXCCProductListBuilderExt.class);
    }
}
