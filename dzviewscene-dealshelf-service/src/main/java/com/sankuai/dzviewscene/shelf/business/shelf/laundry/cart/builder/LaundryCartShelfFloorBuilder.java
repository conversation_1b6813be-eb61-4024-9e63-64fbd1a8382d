package com.sankuai.dzviewscene.shelf.business.shelf.laundry.cart.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.shelf.business.utils.ProductTitleHighLightUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import jodd.util.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtPointInstance(name = "洗衣预定货架楼层构造扩展点")
public class LaundryCartShelfFloorBuilder extends FloorsBuilderExtAdapter {

    @Override
    public String itemComponentRichLabelsTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId){
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.laundry.cart.builder.LaundryCartShelfFloorBuilder.itemComponentRichLabelsTitle(ActivityContext,String,ProductM,long)");
        return ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(activityContext, productM.getTitle(), ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD);
    }

    @Override
    public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.laundry.cart.builder.LaundryCartShelfFloorBuilder.itemComponentLabs(ActivityContext,String,ProductM,int)");
        Map<String, Integer> oceanMap = new HashMap<>();
        oceanMap.put("poi_id", getShopId(activityContext));
        oceanMap.put("index", index);
        return JsonCodec.encode(oceanMap);
    }

    @Override
    public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.laundry.cart.builder.LaundryCartShelfFloorBuilder.itemComponentPic(ActivityContext,String,ProductM)");
        String headPic = productM.getPicUrl();
        if (StringUtil.isBlank(headPic)) {
            return null;
        }
        PicAreaVO picAreaVO = new PicAreaVO();
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO();
        dzPictureComponentVO.setPicUrl(headPic);
        dzPictureComponentVO.setAspectRadio(1);
        picAreaVO.setPic(dzPictureComponentVO);
        return picAreaVO;
    }

    private Integer getShopId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.laundry.cart.builder.LaundryCartShelfFloorBuilder.getShopId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.mtPoiId) + "", 0);
        }
        return NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.dpPoiId) + "", 0);
    }
}
