package com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzTagVO;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@ExtPointInstance(name = "剧本库列表-实景剧本扩展实现")
public class ActiveRolePlayProductBuilderExt extends ProductBuilderVoExtAdapter {

    private static final int HIGH_LIGHT = 1;

    private static final int NORMAL = 0;

    @Override
    public String picUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayProductBuilderExt.picUrl(ActivityContext,ProductM)");
        return productM.getPicUrl();
    }

    @Override
    public String sale(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayProductBuilderExt.sale(ActivityContext,ProductM)");
        ProductSaleM productSaleM = productM.getSale();
        if (productSaleM == null || productSaleM.getSale() == 0){
            return null;
        }
        return productSaleM.getSale() + "人订过" ;
    }

    @Override
    public String salePriceDesc(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayProductBuilderExt.salePriceDesc(ActivityContext,ProductM)");
        String desc = productM.getAttr(GeneralProductAttrEnum.ATTR_DIFFERENT_PRICE_DESC.getKey());
        return StringUtils.isEmpty(desc) ? "/人" : String.format("%s/人", desc);
    }

    @Override
    public List<String> productTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayProductBuilderExt.productTags(ActivityContext,ProductM)");
        String adviceNum = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_ADVICENUM.getKey());
        String duration = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_PURE_DURATION.getKey());
        return Lists.newArrayList(adviceNum, duration).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<DzTagVO> aidDecisionTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayProductBuilderExt.aidDecisionTags(ActivityContext,ProductM)");
        List<DzTagVO> result = Lists.newArrayList();
        //排行榜需要高亮
        String rank = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_RANK.getKey());
        String style = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_STYLE.getKey());
        List<String> backroomTags = JsonCodec.decode(productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_TAGS.getKey()), new TypeReference<List<String>>(){});
        result.add(buildRankTag(rank));
        result.addAll(buildNormalTag(style, backroomTags));
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> extAttrs(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayProductBuilderExt.extAttrs(ActivityContext,ProductM)");
        String difficulty = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_DIFFICULTLEVEL.getKey());
        Map<String, Object> attrs = Maps.newHashMap();
        attrs.put("difficulty", NumberUtils.objToInt(difficulty));
        return attrs;
    }

    private DzTagVO buildRankTag(String rank) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayProductBuilderExt.buildRankTag(java.lang.String)");
        if (StringUtils.isEmpty(rank)) {
            return null;
        }
        return new DzTagVO(rank, HIGH_LIGHT);
    }

    private List<DzTagVO> buildNormalTag(String style, List<String> backroomTags) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.ActiveRolePlayProductBuilderExt.buildNormalTag(java.lang.String,java.util.List)");
        List<String> tags = Lists.newArrayList();
        tags.add(style);
        tags.addAll(Optional.ofNullable(backroomTags).orElse(Collections.emptyList()));
        return tags.stream().filter(StringUtils::isNotEmpty).map(tag -> new DzTagVO(tag.contains("实景推理")?"实景":tag, NORMAL)).collect(Collectors.toList());
    }

}
