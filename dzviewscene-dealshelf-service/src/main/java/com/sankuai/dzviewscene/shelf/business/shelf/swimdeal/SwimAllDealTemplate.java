package com.sankuai.dzviewscene.shelf.business.shelf.swimdeal;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.DouHuExtContext;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.JoyCardShelfContext;
import com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealMainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealOceanBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.fetcher.SwimDealQueryFetcher;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.cardbar.CardBarBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.cardbar.ShelfCardBarBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.OnlyProductsShelfFlow;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 游泳_POI_页_团单货架
 * @auther: liweilong06
 * @date: 2020/10/10 12:25 下午
 */
@ActivityTemplate(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, sceneCode = "joy_poi_swim_deal_shelf", name = "游泳_POI_页_团单货架")
public class SwimAllDealTemplate implements IActivityTemplate {

    /**
     * 指定场景流程
     *
     * @return
     */
    @Override
    public Class<? extends IActivityFlow> flow() {
        return OnlyProductsShelfFlow.class;
    }

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.swim.all.deal.template.user.agent.directPromoScene.map.config", defaultValue = "{}")
    public Map<Integer, Integer> userAgent2directPromoSceneMap;

    /**
     * 扩展参数, 指定场景特有的参数
     *
     * @param exParams
     * @return
     */
    @Override
    public void extParams(Map<String, Object> exParams) {
        // 1. 设置召回参数
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(SwimContext.SWIM_DEAL_FLOOR));
        // 2. 设置第一组商品参数,
        List<String> attrKeys = buildAttrKeys();
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(SwimContext.SWIM_DEAL_FLOOR, new HashMap<String, Object>() {{
                // 设置第一组商品填充参数
                put(PaddingFetcher.Params.planId, SwimContext.DEFAULT_DEAL_THEME_PLAN_ID);
                put("attributeKeys", attrKeys);
                put("promoTemplateId", SwimContext.PROMO_TEMPLATE_ID);
                put("tcMergePromoTemplateId", SwimContext.TC_MERGE_PROMO_TEMPLATE_ID);
                put("scene", SwimContext.JOY_CARD_SCENE_ID);
                put("directPromoScene", 400200);
                setDirectPromoSceneAccordingToUserAgent(exParams);
                put("enablePreSalePromoTag", true);
                put("priceDescType", 4);
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
            }});
        }});
        // 显示showType
        exParams.put(ShelfActivityConstants.Style.showType, 3);
        // 排序（美团侧单独排序）
        addSortId(exParams);
        exParams.put(DouHuFetcher.Params.DP_EXP_ID, DouHuUtils.getDPDouHuExpId("joy_poi_swim_deal_shelf", "sortProduct"));
        exParams.put(DouHuFetcher.Params.MT_EXP_ID, DouHuUtils.getMTDouHuExpId("joy_poi_swim_deal_shelf", "sortProduct"));
    }

    /**
     * 部分端，例如微信小程序还未全链路支持优惠券，因此要配置directPromoScene
     *@param
     *@return
     */
    private void setDirectPromoSceneAccordingToUserAgent(Map<String, Object> exParams) {
        int userAgent = ParamsUtil.getIntSafely(exParams, "userAgent");
        if (MapUtils.isNotEmpty(userAgent2directPromoSceneMap) && userAgent2directPromoSceneMap.containsKey(userAgent)) {
            exParams.put("directPromoScene", userAgent2directPromoSceneMap.get(userAgent));
        }
    }

    private void addSortId(Map<String, Object> exParams) {
        int platform = Optional.ofNullable((Integer) exParams.get(ShelfActivityConstants.Params.platform)).orElse(1);
        if (PlatformUtil.isMT(platform)) {
            // 排序（美团侧单独排序）
            exParams.put(PaddingFetcher.QueryParam.sortId, PaddingFetcher.QueryValue.hasDirectPromoAndSaleDescWithJuHuaSuan);
        }
    }

    private List<String> buildAttrKeys() {
        return Lists.newArrayList("swimming_suitable_person", "service_type", "reservation_is_needed_or_not", "swimming_deal_time", "preSaleTag");
    }

    /**
     * 上下文扩展实现, 按场景隔离
     *
     * @param extContexts
     */
    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        // 玩乐卡
        extContexts.add(JoyCardShelfContext.class);
        extContexts.add(DouHuExtContext.class);
    }

    /**
     * 复写此方法, 提供扩展校验器实例, 活动校验器 = 平台校验器 + 场景校验器
     *
     * @param validators
     * @return
     */
    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {

    }

    /**
     * 复写此方法, 提供扩展能力实例, 场景能力优先级 > 平台能力
     *
     * @param abilities
     * @return
     */
    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        // 1. 商品召回能力
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, SwimDealQueryFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        // 3. 斗斛实验能力
        abilities.put(DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE, PlatformDouHuFetcher.class);
    }

    /**
     * 复写此方法, 提供扩展点实例, 场景扩展点 > 平台扩展点
     *
     * @param extPoints
     * @return
     */
    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        // 1. 打点数据能力扩展点
        extPoints.put(OceanBuilderExt.SHELF_OCEAN_EXT_CODE, SwimDealOceanBuilderExt.class);
        // 2. 商品楼层构造器扩展点
        extPoints.put(FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, SwimDealFloorsBuilderExt.class);
        // 3. 玩乐卡Bar构造器扩展点
        extPoints.put(CardBarBuilderExt.EXT_POINT_CARD_BAR_CODE, ShelfCardBarBuilderExt.class);
        // 4. 标题扩展点
        extPoints.put(MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE, SwimDealMainTitleBuilderExt.class);
    }
}
