package com.sankuai.dzviewscene.shelf.gateways.api;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.gateway.client.debug.DEBUG;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.athena.viewscene.framework.ActivityRequest;
import com.sankuai.athena.viewscene.framework.ActivityResponse;
import com.sankuai.athena.viewscene.framework.pmf.PmfActivityEngine;
import com.sankuai.dzviewscene.mq.MqService;
import com.sankuai.dzviewscene.mq.dto.ContextParams;
import com.sankuai.dzviewscene.mq.dto.DataBusContext;
import com.sankuai.dzviewscene.mq.dto.ResponseDataDTO;
import com.sankuai.dzviewscene.mq.dto.request.RequestDTO;
import com.sankuai.dzviewscene.mq.dto.response.ResponseDTO;
import com.sankuai.dzviewscene.mq.request.MqRequest;
import com.sankuai.dzviewscene.product.enums.PositionTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils2;
import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
import com.sankuai.dzviewscene.shelf.gateways.utils.AppContextUtil;
import com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ContextHashMap;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.nibpt.unionlogger.UnionLoggerContext;
import com.sankuai.nibpt.unionlogger.constant.BusinessTypeEnum;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

import static org.apache.commons.lang.CharEncoding.UTF_8;

/**
 * 团单筛选列表-首次加载
 *
 * <AUTHOR>
 * @date 2022/4/29
 */
@Slf4j
@URL(url = "/api/dzviewscene/dealshelf/dealfilterlist")
public class DealFilterListApi implements API, AppContextAware, UserIdAware, MTUserIdAware {
    /**
     * 城市Id
     */
    @Setter
    private int cityid;
    /**
     * 平台
     * 100： dpapp
     * 101： m
     * 200： mtapp
     * 201： i
     */
    @Setter
    private int platform;
    /**
     * 客户端类型：ios | android | 空字符串
     */
    @Setter
    private String clienttype = "android";

    /**
     * 货架场景识别码，由后端统一分配。
     */
    @Setter
    private String scenecode;
    /**
     * 页面场景(前端有加工,语义可能有改变)
     */
    @Setter
    private String pagesource;

    /**
     * 页面来源,前端直接透传,不加工
     */
    @Setter
    private String pagesourcenew;
    /**
     * 展位识别码，由后端统一分配。
     */
    @Setter
    private String spacekey;
    /**
     * 商户Id
     */
    @Setter
    private long shopid;
    /**
     * 统一商户ID
     */
    @Setter
    private String shopuuid;
    /**
     * 选中的排序Id(暂未使用，保留字段)
     */
    @Setter
    private int sortid;
    /**
     * 定位的关键字
     */
    @Setter
    private String searchkeyword;
    /**
     * 纬度
     */
    @Setter
    private Double lat;
    /**
     * 经度
     */
    @Setter
    private Double lng;
    /**
     * 分页大小
     */
    @Setter
    private int pagesize = 20;

    @Setter
    private int pageindex = 1;
    /**
     * 附加参数，Map的Json
     */
    @Setter
    private String extra;
    /**
     * 商品ID列表
     */
    @Setter
    private String productidlist;
    /**
     * 业务实体ID
     */
    @Setter
    private String entityid;
    /**
     * tab模式下，叶子节点的tabid，标识选中的tabid。
     */
    @Setter
    private long filterid;
    /**
     * 设备ID，dpId or uuid
     */
    @Setter
    private String deviceid;
    /**
     * 统一设备ID
     */
    @Setter
    private String unionid;
    /**
     * 用户搜索的关键词
     */
    @Setter
    private String searchterm;

    /**
     * regionId，到家set化用
     */
    @Setter
    private String wtt_region_id;

    /**
     * 用户定位城市id
     */
    @Setter
    private Integer locationcityid;

    /**
     * 点评用户ID
     */
    private long dpUserid;

    /**
     * 美团用户ID
     */
    private long mtUserid;
    /**
     * 版本号
     */
    private String version;

    /**
     * 反爬风控参数
     */
    @Setter
    private String mtgsig;


    /*********************** 入参 End ***********************/
    @Resource
    private PmfActivityEngine activityEngine;
    
    @Resource
    private MqService mqService;

    @Override
    public void setAppContext(AppContext appContext) {
        clienttype = appContext.getClient();
        deviceid = AppContextUtil.readDeviceId(appContext, platform, deviceid);
        version = appContext.getVersion();
        unionid = getUnionId(appContext.getUnionid());
        platform = getPlatform(appContext.getPlatform());
    }

    private String getUnionId(String currentUnionId) {
        if (StringUtils.isEmpty(unionid)) {
            return currentUnionId;
        }
        return unionid;
    }

    private int getPlatform(String currentPlatform) {
        if (platform > 0) {
            return platform;
        }
        if (StringUtils.isEmpty(currentPlatform) || currentPlatform.equals("dp")) {
            return VCClientTypeEnum.DP_APP.getCode();
        }
        return VCClientTypeEnum.MT_APP.getCode();
    }

    @Override
    public void setMTUserId(long userId) {
        if (userId != 0L) {
            this.mtUserid = userId;
        }
    }

    @Override
    public void setUserId(long userId) {
        if (userId != 0L) {
            this.dpUserid = userId;
        }
    }

    @Override
    public Object execute() {
        try {
            ActivityRequest activityRequest = buildActivityRequest();
            LogUtils.recordKeyMsg(getUserId(),"dealfilterlistEntrance",activityRequest);
            UnionLoggerContext.logUserId(BusinessTypeEnum.GENERAL_POI_SHELF, PlatformUtil.isMT(platform) ? String.valueOf(mtUserid) : String.valueOf(dpUserid));
            ActivityResponse activityResponse = activityEngine.execute(activityRequest);
            return getResult(activityResponse, activityRequest);
        } finally {
            UnionLoggerContext.clear();
        }
    }

    private ActivityRequest buildActivityRequest() {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setParams(new ContextHashMap<>(64));
        if (PlatformUtil.isMT(platform)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, cityid);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, Long.valueOf(shopid).intValue());
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiIdL, shopid);
            activityRequest.addParam(ShelfActivityConstants.Params.mtUserId, mtUserid);
            activityRequest.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.MT.getType());
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, cityid);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, Long.valueOf(shopid).intValue());
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiIdL, shopid);
            activityRequest.addParam(ShelfActivityConstants.Params.dpUserId, dpUserid);
            activityRequest.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.DP.getType());
        }
        activityRequest.addParam(ShelfActivityConstants.Params.locationCityId, locationcityid);
        activityRequest.addParam(ShelfActivityConstants.Params.wttRegionId, wtt_region_id);
        if (DealFilterListActivity.SpaceKey.SHOP_DEAL_SHELF_LANDING.equals(spacekey)) {
            activityRequest.addParam(ShelfActivityConstants.Params.position, MagicMemberUtil.getPosition(platform, PositionTypeEnum.SHELF_PAGE.getCode()));
        }
        activityRequest.addParam(ShelfActivityConstants.Params.keyword, searchkeyword);
        activityRequest.addParam(ShelfActivityConstants.Params.shopUuid, shopuuid);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, platform);
        activityRequest.addParam(ShelfActivityConstants.Params.unionId, unionid);
        activityRequest.addParam(ShelfActivityConstants.Params.searchterm, searchterm);
        activityRequest.addParam(ShelfActivityConstants.Params.deviceId, deviceid);
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, clienttype);
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, version);
        activityRequest.addParam(ShelfActivityConstants.Params.lat, lat);
        activityRequest.addParam(ShelfActivityConstants.Params.lng, lng);
        activityRequest.addParam(ShelfActivityConstants.Params.pageSize, pagesize);
        activityRequest.addParam(ShelfActivityConstants.Params.pageNo, pageindex);
        activityRequest.addParam(ShelfActivityConstants.Params.selectedFilterId, filterid);
        activityRequest.addParam(ShelfActivityConstants.Params.extra, extra);
        activityRequest.addParam(ShelfActivityConstants.Params.mtgsig, mtgsig);
        activityRequest.addParam(ShelfActivityConstants.Params.entityId, entityid);
        activityRequest.addParam(ShelfActivityConstants.Params.productIdList, productidlist);
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealFilterList);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, scenecode);
        if (StringUtils.isNotBlank(pagesource)) {
            String pageSourceDecode = "";
            try {
                pageSourceDecode = URLDecoder.decode(pagesource, UTF_8);
            } catch (Exception e) {
                Cat.logError("pagesourceDecodeFail", e);
            }
            activityRequest.addParam(ShelfActivityConstants.Params.pageSource, pageSourceDecode);
        }
        if (StringUtils.isNotBlank(pagesourcenew)) {
            String pageSourceNewDecode = "";
            try {
                pageSourceNewDecode = URLDecoder.decode(pagesourcenew, UTF_8);
            } catch (Exception e) {
                log.error("pagesourceNewDecodeFail", e);
                Cat.logError("pagesourceNewDecodeFail", e);
            }
            activityRequest.addParam(ShelfActivityConstants.Params.pageSourceNew, pageSourceNewDecode);
        }
        activityRequest.addParam(ShelfActivityConstants.Params.spaceKey, getSpaceKeyParam());
        activityRequest.setActivityCode(DealFilterListActivity.CODE);
        activityRequest.setStartTime(System.currentTimeMillis());
        return activityRequest;
    }

    private String getSpaceKeyParam() {
        return StringUtils.isNotEmpty(spacekey) ? spacekey : DealFilterListActivity.SpaceKey.DEFAULT_DEAL_FILTER_LIST;
    }

    private Object getResult(ActivityResponse activityResponse, ActivityRequest request) {
        if (activityResponse == null || activityResponse.getResult() == null || activityResponse.getResult().join() == null) {
            return null;
        }
        DEBUG.log("traces", activityResponse.getTraceElements());
        Object join = activityResponse.getResult().join();
        AntiCrawlerUtils2.antiCrawlerPrice4List(join, request);
        LogUtils.recordKeyMsg(getUserId(),"dealfilterlistEnd",join);
        sendMqMsg(join);
        return join;
    }

    private long getUserId() {
        if (PlatformUtil.isMT(platform)) {
            return mtUserid;
        }
        return dpUserid;
    }
    
    public void sendMqMsg(Object result){
        try{
            MqRequest mqRequest = buildMqRequest(result);
            mqService.sendMsgData(mqRequest);
        }catch (Exception e){
            log.error("mqService.sendMsgData fail", e);
        }
    }
    public MqRequest buildMqRequest(Object result){
        MqRequest mqRequest = new MqRequest();
        mqRequest.setDatabusContext(buildDatabusContext());
        mqRequest.setResponseData(buildResponseData(result));
        return mqRequest;
    }

    public DataBusContext buildDatabusContext(){
        DataBusContext dataBusContext = new DataBusContext();
        ContextParams contextParams = new ContextParams();
        JSONObject scriptInfo = new JSONObject();
        scriptInfo.put("name", "rn_gc_gcdealmrnmodules&gcdealdetailvc");
        scriptInfo.put("version", "2.0.0");
        contextParams.setScriptInfo(scriptInfo);
        dataBusContext.setContextParams(contextParams);

        if (PlatformUtil.isMT(platform)) {
            dataBusContext.addParam("cityId", cityid);
            // 映射 RcfDealBffClientTypeEnum，美团clientType 取1
            dataBusContext.addParam("clientType", 1);

        } else {
            dataBusContext.addParam("cityId", cityid);
        }
        dataBusContext.addParam("shopId", shopid);
        dataBusContext.addParam(ShelfActivityConstants.Params.locationCityId, locationcityid);
        dataBusContext.addParam("appVersion", version);
        dataBusContext.addParam("userlat", lat);
        dataBusContext.addParam("userlng", lng);
        dataBusContext.addParam(ShelfActivityConstants.Params.extra, extra);
        dataBusContext.addParam(ShelfActivityConstants.Params.mtgsig, mtgsig);
        dataBusContext.addParam("dealgroupid", entityid);
        dataBusContext.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealFilterList);
        dataBusContext.addParam(ShelfActivityConstants.Params.sceneCode, scenecode);
        if (StringUtils.isNotBlank(pagesource)) {
            String pageSourceDecode = "";
            try {
                pageSourceDecode = URLDecoder.decode(pagesource, UTF_8);
            } catch (Exception e) {
                log.error("pagesourceDecodeFail", e);
            }
            dataBusContext.addParam("pageSource", pageSourceDecode);
        }
        dataBusContext.addParam(ShelfActivityConstants.Params.spaceKey, getSpaceKeyParam());
        return dataBusContext;
    }

    public List<Object> buildResponseData(Object result){
        List<Object> list = new ArrayList<>();
        ResponseDataDTO responseDataDTO = new ResponseDataDTO();
        RequestDTO request = new RequestDTO();

        if (PlatformUtil.isMT(platform)) {
            request.addParam(ShelfActivityConstants.Params.mtCityId, cityid);
            request.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.MT.getType());
        } else {
            request.addParam(ShelfActivityConstants.Params.dpCityId, cityid);
            request.addParam(ShelfActivityConstants.Params.platform, VCPlatformEnum.DP.getType());
        }
        request.addParam("shopId", shopid);
        request.addParam(ShelfActivityConstants.Params.locationCityId, locationcityid);
        request.addParam(ShelfActivityConstants.Params.clientType, clienttype);
        request.addParam(ShelfActivityConstants.Params.appVersion, version);
        request.addParam("userlat", lat);
        request.addParam("userlng", lng);
        request.addParam(ShelfActivityConstants.Params.extra, extra);
        request.addParam(ShelfActivityConstants.Params.mtgsig, mtgsig);
        request.addParam("dealgroupid", entityid);
        request.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealFilterList);
        request.addParam(ShelfActivityConstants.Params.sceneCode, scenecode);
        if (StringUtils.isNotBlank(pagesource)) {
            String pageSourceDecode = "";
            try {
                pageSourceDecode = URLDecoder.decode(pagesource, UTF_8);
            } catch (Exception e) {
                log.error("pagesourceDecodeFail", e);
            }
            request.addParam(ShelfActivityConstants.Params.pageSource, pageSourceDecode);
        }
        request.addParam(ShelfActivityConstants.Params.spaceKey, getSpaceKeyParam());

        request.setUrl("/api/dzviewscene/dealshelf/dealfilterlist.bin");
        responseDataDTO.setRequest(request);

        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setCode(200);
        responseDTO.setMsg("success");
        responseDTO.setData(result);
        responseDataDTO.setResponse(responseDTO);
        list.add(responseDataDTO);
        return list;
    }
}
