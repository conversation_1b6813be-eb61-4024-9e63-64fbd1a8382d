package com.sankuai.dzviewscene.shelf.platform.shelf.flow;


import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.MultiDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@ActivityFlow(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, flowCode = MergeQueryOnlyProductsMultiABShelfFlow.ACTIVITY_FLOW_MERGE_QUERY_ONLY_QUERY_PRODUCTS_MULTI_AB_CODE, name = "融合查询版只查询商品列表")
public class MergeQueryOnlyProductsMultiABShelfFlow implements IActivityFlow<ShelfGroupM> {
    public static final String ACTIVITY_FLOW_MERGE_QUERY_ONLY_QUERY_PRODUCTS_MULTI_AB_CODE = "MergeQueryOnlyProductsMultiABShelfFlow";

    @Override
    public CompletableFuture<ShelfGroupM> execute(AbstractActivity<?> activity, ActivityContext ctx) {
        // 1. 查询斗斛(可选)
        CompletableFuture<List<DouHuM>> douHuMCompletableFutureList = activity.findAbility(ctx, MultiDouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE).build(ctx);

        //2.融合查询
        CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture = activity.findAbility(ctx, MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE).build(ctx);

        return productGroupsCompletableFuture.thenApply(productGroupMMap -> {
            ShelfGroupM shelfGroupM = new ShelfGroupM();
            shelfGroupM.setFilterMs(null);
            shelfGroupM.setProductGroupMs(productGroupMMap);
            shelfGroupM.setDouHus(douHuMCompletableFutureList.join());
            return shelfGroupM;
        });
    }
}
