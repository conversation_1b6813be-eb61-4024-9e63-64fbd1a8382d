package com.sankuai.dzviewscene.shelf.business.detail.edu.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 课程信息
 * @auther: liweilong06
 * @date: 2020/9/28 3:02 上午
 */
@Data
@Builder
public class DzEduCourseDetailVO implements Serializable {

    /**
     * 课程Id
     */
    private long courseId;

    /**
     * 课程标题
     */
    private String title;

    /**
     * 图片信息
     */
    private List<DzImgVO> pics;

    /**
     * 视频信息
     */
    private DzVideoVO video;

    /**
     * 随时退
     */
    private boolean autoRefund;

    /**
     * 过期退
     */
    private boolean overdueAutoRefund;

    /**
     * 预约信息
     */
    private String reservation;

    /**
     * 最低价
     */
    private String minPrice;

    /**
     * 最高价
     */
    private String maxPrice;

    /**
     * 售价
     */
    private String salePrice;

    /**
     * 市场原价
     */
    private String marketPrice;

    /**
     * 优惠信息列表
     */
    private List<String> promo;

    /**
     * 是否可以购买
     */
    private boolean canBuy;

    /**
     * 新品
     */
    private boolean newProduct;

    /**
     * 特色标签，如课程实录
     */
    private String featureTag;

    /**
     * 销量
     */
    private String saleStr;

    /**
     * 课程类型
     */
    private String courseType;

    /**
     * 课程结构化信息
     */
    private List<DzEduAttrDataVO> courseAttrs;

    /**
     * 课程亮点
     */
    private List<String> courseHighlights;

    /**
     * 购买须知
     */
    private List<DzEduAttrDataVO> buyDescription;

    /**
     * 课程特色
     */
    private List<DzEduClassFeatureVO> classFeatures;

}
