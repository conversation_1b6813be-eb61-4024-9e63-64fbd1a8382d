package com.sankuai.dzviewscene.shelf.platform.filterlist.flow;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.model.FilterProductsM;
import org.apache.commons.collections.MapUtils;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@ActivityFlow(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE, flowCode = FilterListOnlyProductsFlow.ACTIVITY_FLOW_ONLY_PRODUCTS_CODE, name = "融合先筛选后获取商品能力的流程")
public class FilterListOnlyProductsFlow implements IActivityFlow<FilterProductsM> {

    public static final String ACTIVITY_FLOW_ONLY_PRODUCTS_CODE = "FilterListOnlyProductsFlow";

    @Override
    public CompletableFuture<FilterProductsM> execute(AbstractActivity<?> activity, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.flow.FilterListOnlyProductsFlow.execute(AbstractActivity,ActivityContext)");
        // 1. 召回: 商品组名->商品列表
        CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture =
                activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE).build(ctx).thenCompose(productGroups -> {
                    // 2 暂存召回结果, 能力实现自行填充
                    ctx.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
                    return activity.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE).build(ctx);
                });

        return productGroupsCompletableFuture.thenApply(productGroupMMap -> {
            FilterProductsM filterProductsM = new FilterProductsM();
            Map<String, ProductGroupM> productGroupMap = productGroupsCompletableFuture.join();
            if (MapUtils.isNotEmpty(productGroupMap)) {
                filterProductsM.setProductGroup(productGroupMap.entrySet().stream().findFirst().map(Map.Entry::getValue).orElse(null));
            }
            return filterProductsM;
        });
    }
}
