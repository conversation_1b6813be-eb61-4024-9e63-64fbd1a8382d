package com.sankuai.dzviewscene.shelf.faulttolerance.detail;

import com.dianping.cat.Cat;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceConfiguration;
import com.sankuai.athena.stability.faulttolerance.core.Executor;
import com.sankuai.athena.stability.faulttolerance.mirror.MirrorConfiguration;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by zhangsuping on 2021/9/23.
 */
@Service
public class ProductDetailFTConfiguration extends FaultToleranceConfiguration<ActivityContextRequest, Object> {

    @Resource
    private ProductDetailExecutor productDetailExecutor;

    @Resource
    private ProductDetailMirrorReportCleaner productDetailMirrorReportCleaner;

    @Override
    public String getFtName() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.faulttolerance.detail.ProductDetailFTConfiguration.getFtName()");
        return "dzproductdetail";
    }

    @Override
    public Executor<ActivityContextRequest, Object> getMainExecutor() {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.faulttolerance.detail.ProductDetailFTConfiguration.getMainExecutor()");
        return productDetailExecutor;
    }

    @Override
    public Executor<ActivityContextRequest, Object> getFallback(String fallback) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.faulttolerance.detail.ProductDetailFTConfiguration.getFallback(java.lang.String)");
        return null;
    }

    @Override
    public MirrorConfiguration getMirrorConfiguration() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.faulttolerance.detail.ProductDetailFTConfiguration.getMirrorConfiguration()");
        return productDetailMirrorReportCleaner;
    }
}
