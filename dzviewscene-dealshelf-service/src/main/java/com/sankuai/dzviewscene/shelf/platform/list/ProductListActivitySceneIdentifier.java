package com.sankuai.dzviewscene.shelf.platform.list;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.AbstractSceneIdentifier;
import com.sankuai.dzviewscene.shelf.framework.annotation.SceneIdentifier;
import com.sankuai.dzviewscene.shelf.framework.core.IScenePredicate;

import java.util.Map;

/**
 * Created by float.lu on 2020/9/8.
 */
@SceneIdentifier(activityCode = ProductListActivity.ACTIVITY_PRODUCT_LIST_CODE, name = "商品列表场景识别器")
public class ProductListActivitySceneIdentifier extends AbstractSceneIdentifier {

    @Override
    protected void fillPredicates(Map<String, IScenePredicate> scenePredicates) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.list.ProductListActivitySceneIdentifier.fillPredicates(java.util.Map)");
        //1. 美团小程序首页推荐商品列表
        scenePredicates.put("dz_apphome_recommend_products", activityContext -> false);
        //2. 医美频道页商品列表场景
        scenePredicates.put("cosmetology_channel_prepaylist", activityContext -> false);
        //3. 生活服务美团猜喜卡商品列表
        scenePredicates.put("life_apphome_recommend_products", activityContext -> false);
        // 亲子-展览演出-列表
        scenePredicates.put("baby_exhibition_show_feed_productlist",activityContext -> false);
        //医美-整形外科频道-预付推荐商品列表
        scenePredicates.put("cosmetology_channel_plastic_surgery_prepaylist", activityContext -> false);
        //结婚-案例详情-预付商品列表
        scenePredicates.put("wedding_case_detail_prepaylist", activityContext -> false);
    }
}
