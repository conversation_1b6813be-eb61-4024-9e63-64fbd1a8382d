package com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl;

import com.dianping.cat.Cat;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.rule.api.dto.Response;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.GroupPaddingHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/12/2 4:41 下午
 */
@Component
public class DealPromoPaddingHandler implements GroupPaddingHandler {

    private static final int PADDING_DEAL_LIMIT = 20;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityContext activityContext, ProductGroupM productGroupM, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.padding(ActivityContext,ProductGroupM,Map)");
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        int cityId = PlatformUtil.isMT(platform) ? activityContext.getParam(ShelfActivityConstants.Params.mtCityId) :
                activityContext.getParam(ShelfActivityConstants.Params.dpCityId);
        long userId = PlatformUtil.isMT(platform) ? activityContext.getParam(ShelfActivityConstants.Params.mtUserId) :
                activityContext.getParam(ShelfActivityConstants.Params.dpUserId);
        String deviceId = activityContext.getParam(ShelfActivityConstants.Params.deviceId);
        Map<Integer, ProductM> productId2Product = buildProductId2Product(productGroupM);
        Integer promoTemplateId = (Integer) params.get(PaddingFetcher.Params.promoTemplateId);

        List<CompletableFuture<Map<Product, List<PromoDisplayDTO>>>> promoFutures =
                getPromoFutures(platform, cityId, userId, deviceId, promoTemplateId, productId2Product);
        return collectProduct2PromoPriceMs(promoFutures, params, productId2Product).thenApply(productId2ProductPromoPrices -> {
            paddingProductGroupM(productGroupM, productId2ProductPromoPrices);
            return productGroupM;
        });
    }

    private void paddingProductGroupM(ProductGroupM productGroupM, Map<Integer, List<ProductPromoPriceM>> productId2PromoPrices) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.paddingProductGroupM(ProductGroupM,Map)");
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts()) || MapUtils.isEmpty(productId2PromoPrices)) {
            return;
        }
        productGroupM.getProducts().forEach(productM -> paddingProductM(productM, productId2PromoPrices.get(productM.getProductId())));
    }

    private void paddingProductM(ProductM productM, List<ProductPromoPriceM> productPromoPriceMs) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.paddingProductM(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM,java.util.List)");
        if (CollectionUtils.isEmpty(productPromoPriceMs)) {
            // miss
            return;
        }
        List<ProductPromoPriceM> promoPrices = CollectionUtils.isEmpty(productM.getPromoPrices()) ? Lists.newArrayList() : productM.getPromoPrices();
        productPromoPriceMs.forEach(productPromoPriceM -> promoPrices.removeIf(promoPrice -> promoPrice.getPromoType() == productPromoPriceM.getPromoType()));
        promoPrices.addAll(productPromoPriceMs);
        productM.setPromoPrices(promoPrices);
    }

    private List<CompletableFuture<Map<Product, List<PromoDisplayDTO>>>> getPromoFutures(int platform, int cityId, long userId, String deviceId,
                                                                                                   int templateId, Map<Integer, ProductM> productId2Product) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.getPromoFutures(int,int,long,java.lang.String,int,java.util.Map)");
        List<CompletableFuture<Map<Product, List<PromoDisplayDTO>>>> futures = new ArrayList<>();
        List<Integer> requestProductIds = new ArrayList<>(productId2Product.keySet());
        List<List<Integer>> requestProductIdsList = ListUtils.partition(requestProductIds, PADDING_DEAL_LIMIT);
        for (List<Integer> productIds : requestProductIdsList) {
            BatchQueryPromoDisplayRequest batchQueryPromoDisplayRequest = buildBatchQueryPromoDisplayRequest(platform,
                    cityId, userId, deviceId, templateId, productIds, productId2Product);
            futures.add(compositeAtomService.batchQueryPromoDisplay(batchQueryPromoDisplayRequest));
        }
        return futures;
    }

    private CompletableFuture<Map<Integer, List<ProductPromoPriceM>>> collectProduct2PromoPriceMs(List<CompletableFuture<Map<Product, List<PromoDisplayDTO>>>> promoFutures,
                                                                                                  Map<String, Object> params, Map<Integer, ProductM> productId2Product) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.collectProduct2PromoPriceMs(java.util.List,java.util.Map,java.util.Map)");
        if (CollectionUtils.isEmpty(promoFutures)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        return assemble(promoFutures).thenCompose(responses -> {
                    if (CollectionUtils.isEmpty(responses)) {
                        return CompletableFuture.completedFuture(null);
                    }
                    return CompletableFuture.completedFuture(convertResponse2ProductPromoPriceList(responses, params, productId2Product));
                }
        );
    }

    private Map<Integer, List<ProductPromoPriceM>> convertResponse2ProductPromoPriceList(List<Map<Product, List<PromoDisplayDTO>>> responses,
                                                                                         Map<String, Object> params, Map<Integer, ProductM> productId2Product) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.convertResponse2ProductPromoPriceList(java.util.List,java.util.Map,java.util.Map)");
        Map<Integer, List<ProductPromoPriceM>> productId2PromoPriceList = Maps.newHashMap();
        responses.forEach(response -> {
            if (MapUtils.isNotEmpty(response)) {
                productId2PromoPriceList.putAll(buildSingleResponseProductPromoPrices(response, params, productId2Product));
            }
        });
        return productId2PromoPriceList;
    }

    private Map<Integer, List<ProductPromoPriceM>> buildSingleResponseProductPromoPrices(Map<Product, List<PromoDisplayDTO>> product2PromoDisplay,
                                                                                         Map<String, Object> params, Map<Integer, ProductM> productId2Product) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.buildSingleResponseProductPromoPrices(java.util.Map,java.util.Map,java.util.Map)");
        Map<Integer, List<ProductPromoPriceM>> singleResponseResult = Maps.newHashMap();
        product2PromoDisplay.forEach(((product, promoDisplays) -> {
            if (CollectionUtils.isEmpty(promoDisplays)) {
                return;
            }
            PromoDisplayDTO showPromo = null;
            for (PromoDisplayDTO promoDisplayDTO : promoDisplays) {
                showPromo = getMaxPromo(showPromo, promoDisplayDTO);
            }
            ProductM productM = productId2Product.get(product.getProductId());
            if (showPromo == null || productM == null) {
                return;
            }
            singleResponseResult.put(product.getProductId(), buildProductPromoPriceMs(params, showPromo, productM));
        }));
        return singleResponseResult;
    }

    private List<ProductPromoPriceM> buildProductPromoPriceMs(Map<String, Object> params, PromoDisplayDTO showPromo, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.buildProductPromoPriceMs(Map,PromoDisplayDTO,ProductM)");
        ProductPromoPriceM productPromoPriceM = buildProductPromoPriceM(params, showPromo, productM);
        return Lists.newArrayList(productPromoPriceM);
    }

    private ProductPromoPriceM buildProductPromoPriceM(Map<String, Object> params, PromoDisplayDTO showPromo, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.buildProductPromoPriceM(Map,PromoDisplayDTO,ProductM)");
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setPromoType(PromoTypeEnum.DIRECT_PROMO.getType());
        productPromoPriceM.setPromoPrice(buildPromoPrice(productM, showPromo.getPromoAmount()));
        productPromoPriceM.setPromoTag(showPromo.getTag());
        productPromoPriceM.setPromoPriceTag(buildPromoPriceTag(productPromoPriceM.getPromoPrice()));
        productPromoPriceM.setDiscount(buildPromoDiscount(params, productPromoPriceM.getPromoPrice(), productM.getBasePrice(),
                BigDecimal.valueOf(NumberUtils.toDouble(productM.getMarketPrice()))));
        return productPromoPriceM;
    }

    private String buildPromoPriceTag(BigDecimal promoPrice) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.buildPromoPriceTag(java.math.BigDecimal)");
        if (promoPrice == null || promoPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return StringUtils.EMPTY;
        }
        return String.format("%s元", promoPrice.stripTrailingZeros().toPlainString());
    }

    private BigDecimal buildPromoDiscount(Map<String, Object> params, BigDecimal promoPrice, BigDecimal basePrice, BigDecimal marketPrice) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.buildPromoDiscount(java.util.Map,java.math.BigDecimal,java.math.BigDecimal,java.math.BigDecimal)");
        if (promoPrice == null || promoPrice.compareTo(BigDecimal.ZERO) <= 0 ||
                basePrice == null || basePrice.compareTo(BigDecimal.ZERO) <= 0 ||
                marketPrice == null || marketPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.TEN;
        }
        List<String> discountBasePricePlanIds = Lists.newArrayList("10000041");
        String refreshPlanId = (String) params.get(PaddingFetcher.Params.refreshPlanId);
        if (discountBasePricePlanIds.contains(refreshPlanId)) {
            // 1. 折扣计算规则定制化：方案ID在配置内的，用原价计算折扣
            return promoPrice.divide(basePrice, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.TEN);
        } else {
            // 2. 其他通用场景，用市场价计算折扣
            return promoPrice.divide(marketPrice, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.TEN);
        }
    }

    private BigDecimal buildPromoPrice(ProductM productM, BigDecimal promoAmount) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.buildPromoPrice(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM,java.math.BigDecimal)");
        if (promoAmount == null || productM.getBasePrice() == null) {
            return productM.getBasePrice();
        }
        BigDecimal salePrice = productM.getBasePrice().subtract(promoAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (salePrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        return salePrice.stripTrailingZeros();
    }


    private PromoDisplayDTO getMaxPromo(PromoDisplayDTO showPromo, PromoDisplayDTO promoDisplayDTO) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.getMaxPromo(PromoDisplayDTO,PromoDisplayDTO)");
        if (showPromo == null) {
            return promoDisplayDTO;
        }
        if (showPromo.getPromoAmount().compareTo(promoDisplayDTO.getPromoAmount()) < 0) {
            return promoDisplayDTO;
        }
        return showPromo;
    }

    private <T> CompletableFuture<List<T>> assemble(List<CompletableFuture<T>> futures) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.assemble(java.util.List)");
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));
        return allDoneFuture.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }

    private BatchQueryPromoDisplayRequest buildBatchQueryPromoDisplayRequest(int platform, int cityId, long userId, String deviceId, int templateId,
                                                                             List<Integer> productIds, Map<Integer, ProductM> productId2Product) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.buildBatchQueryPromoDisplayRequest(int,int,long,java.lang.String,int,java.util.List,java.util.Map)");
        BatchQueryPromoDisplayRequest promoReq = new BatchQueryPromoDisplayRequest();
        promoReq.setPlatform(getPhonePlatform(platform));
        promoReq.setCityId(cityId);
        promoReq.setProductType(getProductType(platform));
        promoReq.setProductList(buildProducts(productIds, productId2Product));
        promoReq.setTemplateID(templateId);
        promoReq.setUserId(userId);
        promoReq.setDpId(deviceId);
        promoReq.setReturnControl(null);
        return promoReq;
    }

    private List<Product> buildProducts(List<Integer> productIds, Map<Integer, ProductM> productId2Product) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.buildProducts(java.util.List,java.util.Map)");
        return productIds.stream().filter(productId -> productId2Product.get(productId) != null).collect(ArrayList::new,
                (list, productId) -> {
                    ProductM productM = productId2Product.get(productId);
                    Product product = new Product();
                    product.setProductId(productId);
                    product.setShopId(CollectionUtils.isEmpty(productM.getShopIds()) ? 0 : productM.getShopIds().get(0));
                    product.setPrice(productM.getBasePrice());
                    list.add(product);
                }, ArrayList::addAll);
    }

    private int getPhonePlatform(int platform) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.getPhonePlatform(int)");
        if (!PlatformUtil.isMT(platform)) {
            return PayPlatform.dp_iphone_native.getCode();
        } else {
            return PayPlatform.mt_iphone_native.getCode();
        }
    }

    private int getProductType(int platform) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.getProductType(int)");
        if (PlatformUtil.isMT(platform)) {
            return ProductType.mt_tuangou.value;
        }
        return ProductType.tuangou.value;
    }

    private Map<Integer, ProductM> buildProductId2Product(ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealPromoPaddingHandler.buildProductId2Product(com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM)");
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return Maps.newHashMap();
        }
        return productGroupM.getProducts().stream().filter(product -> product.getProductId() != 0 &&
                CollectionUtils.isNotEmpty(product.getShopIds()))
                .collect(HashMap::new, (map, product) -> map.put(product.getProductId(), product), HashMap::putAll);
    }

}
