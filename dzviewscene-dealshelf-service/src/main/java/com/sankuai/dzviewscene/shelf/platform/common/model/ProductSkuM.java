package com.sankuai.dzviewscene.shelf.platform.common.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ProductSkuM {

    /**
     * 商品id
     */
    private long productId;

    /**
     * skuId
    */
    private long skuId;

    /**
     * sku标题
     */
    private String skuName;

    /**
     * 状态，1：有效，0：无效
     */
    private int status;

    /**
     * 售价
     */
    private String salePrice;

    /**
     * 市场价
     */
    private String marketPrice;

    /**
     * 优惠后价格
     */
    private String promoPrice;

    /**
     * 融合优惠价格
     */
    private List<ProductPromoPriceM> promoPrices;

    /**
     * 扩展属性
     */
    private List<AttrM> attrs;

    /**
     * 跳转链接
     */
    private String jumpUrl;
}
