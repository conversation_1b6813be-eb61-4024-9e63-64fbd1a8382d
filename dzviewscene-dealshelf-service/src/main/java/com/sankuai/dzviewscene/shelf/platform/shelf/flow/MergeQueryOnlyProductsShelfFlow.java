package com.sankuai.dzviewscene.shelf.platform.shelf.flow;

import com.sankuai.dzviewscene.shelf.business.shelf.common.context.DouHuExtContext;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/11/19.
 */
@ActivityFlow(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, flowCode = MergeQueryOnlyProductsShelfFlow.ACTIVITY_FLOW_MERGE_QUERY_ONLY_QUERY_PRODUCTS_CODE, name = "融合查询版只查询商品列表")
public class MergeQueryOnlyProductsShelfFlow implements IActivityFlow<ShelfGroupM> {

        public static final String ACTIVITY_FLOW_MERGE_QUERY_ONLY_QUERY_PRODUCTS_CODE = "MergeOnlyProductsShelfFlow";

        @Override
        public CompletableFuture<ShelfGroupM> execute(AbstractActivity<?> activity, ActivityContext ctx) {

            // 融合查询
            CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture = activity.findAbility(ctx, MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE).build(ctx);

            return productGroupsCompletableFuture.thenApply(productGroupMMap -> {
                ShelfGroupM shelfGroupM = new ShelfGroupM();
                shelfGroupM.setFilterMs(null);
                shelfGroupM.setProductGroupMs(productGroupMMap);
                setDouHuInNeed(shelfGroupM, ctx);
                return shelfGroupM;
            });
        }

        private void setDouHuInNeed(ShelfGroupM shelfGroupM, ActivityContext ctx) {
            CompletableFuture<DouHuM> douHuMFuture = ctx.getExtContext(DouHuExtContext.CONTEXT_KEY);
            if (douHuMFuture != null) {
                shelfGroupM.setDouHu(douHuMFuture.join());
            }else {
                shelfGroupM.setDouHu(null);
            }
        }
    }
