package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter;

import com.sankuai.dzviewscene.shelf.framework.AbstractAbility;
import com.sankuai.dzviewscene.shelf.framework.annotation.Ability;
import com.sankuai.dzviewscene.shelf.framework.core.IExtPoint;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;

import java.util.List;

/**
 * 多筛选生成能力
 *
 * Created by float.lu on 2020/9/22.
 */
@Ability(code = ShowFilterFetcher.ABILITY_MULTI_FILTER_FETCHER, name = "批量筛选")
public abstract class ShowFilterFetcher extends AbstractAbility<List<FilterM>, IExtPoint> {

    public static final String ABILITY_MULTI_FILTER_FETCHER = "MultiFilterFetcher";

}
