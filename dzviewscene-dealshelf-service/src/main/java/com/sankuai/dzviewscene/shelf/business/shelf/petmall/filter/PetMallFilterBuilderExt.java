package com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;

import java.util.List;

/**
 * <AUTHOR>
 */

@ExtPointInstance(name = "宠物电商筛选列表构造扩展点")
public class PetMallFilterBuilderExt extends FilterBuilderExtAdapter {
    @Override
    public int showType(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterBuilderExt.showType(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 2;
    }

    @Override
    public int minShowNum(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterBuilderExt.minShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 2;
    }

    @Override
    public int childrenMinShowNum(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterBuilderExt.childrenMinShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 0;
    }

    @Override
    public String labs(ActivityContext activityContext, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterBuilderExt.labs(ActivityContext,FilterBtnM)");
        return null;
    }

    @Override
    public RichLabelVO titleButton(ActivityContext activityContext, String productType, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterBuilderExt.titleButton(ActivityContext,String,FilterBtnM)");
        return new RichLabelVO(13,"#FF6633",filterBtnM.getTitle());
    }

    @Override
    public RichLabelVO subTitle(ActivityContext activityContext, String productType, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterBuilderExt.subTitle(ActivityContext,String,FilterBtnM)");
        return null;
    }

    @Override
    public String extra(ActivityContext activityContext, String productType, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterBuilderExt.extra(ActivityContext,String,FilterBtnM)");
        return null;
    }

    @Override
    public List<IconRichLabelVO> preFixedBtns(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterBuilderExt.preFixedBtns(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return null;
    }

    @Override
    public boolean showWithNoProducts(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterBuilderExt.showWithNoProducts(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return false;
    }
}
