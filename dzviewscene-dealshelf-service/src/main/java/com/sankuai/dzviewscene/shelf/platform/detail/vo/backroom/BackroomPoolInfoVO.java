package com.sankuai.dzviewscene.shelf.platform.detail.vo.backroom;

import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.shelf.backroom.vo.PoolRuleDescVO;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzPoolUserVO;
import lombok.Data;

import java.util.List;

/**
 * Title: BackroomPinInfoVO
 * Description: 密室详情页拼场模块
 *
 * <AUTHOR>
 * @date 2021-01-05
 */
@Data
public class BackroomPoolInfoVO {

    /**
     * 拼场简单规则
     */
    private List<String> poolSimpleRule;

    /**
     * 拼场详细规则
     */
    private PoolRuleDescVO poolDetailRule;

    /**
     * 发起人
     */
    private DzPoolUserVO promoter;

    /**
     * 已加入信息
     */
    private String joinMessage;

    /**
     * 邀请信息
     */
    private String inviteMessage;

    /**
     * 当前进度描述
     */
    private List<RichLabelVO> currentProgressDesc;

    /**
     * 场次用户信息
     */
    private List<DzPoolUserVO> poolUserList;

    /**
     * 拼场宣言
     */
    private String poolDeclaration;

    /**
     * skuId
     */
    private int productItemId;

    /**
     * 日期
     */
    private long poolDate;

    /**
     * 场次时段信息
     */
    private String period;

    /**
     * 拼场状态
     */
    private int poolStatus;

    /**
     * 拼场状态描述
     */
    private String poolStatusDesc;

    /**
     * 剩余时间
     */
    private long restTime;

    /**
     * 价格
     */
    private String price;

    /**
     * 优惠标签
     */
    private String promoTag;

}
