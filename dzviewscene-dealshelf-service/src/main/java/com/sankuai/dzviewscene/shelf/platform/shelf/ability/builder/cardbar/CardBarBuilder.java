package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.cardbar;

import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfCardBarVO;
import com.sankuai.dzviewscene.shelf.framework.AbstractAbility;
import com.sankuai.dzviewscene.shelf.framework.annotation.Ability;

/**
 * card bar拼接能力, 默认无, 由业务实现
 * <p>
 * Created by float on 2020/8/22.
 */
@Ability(code = CardBarBuilder.ABILITY_CARD_VAR_CODE, name = "cardBar构造能力")
public abstract class CardBarBuilder extends AbstractAbility<DzShelfCardBarVO, CardBarBuilderExt> {

    public static final String ABILITY_CARD_VAR_CODE = "CardBarBuilder";

}
