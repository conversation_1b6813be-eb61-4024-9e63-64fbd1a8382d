package com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.productshelf.vu.enums.ButtonTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.utils.ProductTitleHighLightUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class HealthCheckBookingFloorBuilder extends FloorsBuilderExtAdapter {

    private static final int DEFAULT_SHOW_NUM = 3;

    @Override
    public Map<Long, List<ProductM>> groupByFilter(ActivityContext activityContext, String groupName, FilterM filterM, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFloorBuilder.groupByFilter(ActivityContext,String,FilterM,ProductGroupM)");
        Map<Long, List<ProductM>> groupProducts = Maps.newHashMap();
        if (filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            groupProducts.put(0L, productGroupM.getProducts());
            return groupProducts;
        }
        return filterM.getFilters().stream().collect(Collectors.toMap(FilterBtnM::getFilterId, FilterBtnM::getProducts));
    }

    @Override
    public String itemComponentRichLabelsTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId){
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFloorBuilder.itemComponentRichLabelsTitle(ActivityContext,String,ProductM,long)");
        return ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(activityContext, productM.getTitle(), ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD);
    }

    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFloorBuilder.floorDefaultShowNum(ActivityContext,String,List)");
        return DEFAULT_SHOW_NUM;
    }

    @Override
    public String moreComponentText(ActivityContext activityContext, String groupName, ProductGroupM productGroupM, List<ProductM> currentProductMs) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFloorBuilder.moreComponentText(ActivityContext,String,ProductGroupM,List)");
        if (currentProductMs.size() <= DEFAULT_SHOW_NUM) {
            return null;
        }
        return String.format("查看更多%d个套餐", currentProductMs.size() - DEFAULT_SHOW_NUM);
    }

    @Override
    public String itemComponentSale(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFloorBuilder.itemComponentSale(ActivityContext,String,ProductM)");
        return Optional.ofNullable(productM.getSale())
                .map(ProductSaleM::getSaleTag).orElse("");
    }

    @Override
    public String itemComponentSalePriceDesc(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFloorBuilder.itemComponentSalePriceDesc(ActivityContext,String,ProductM)");
        return productM.getAttr(GeneralProductAttrEnum.ATTR_DIFFERENT_PRICE_DESC.getKey());
    }

    @Override
    public DzSimpleButtonVO itemComponentButton(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFloorBuilder.itemComponentButton(ActivityContext,String,ProductM)");
        DzSimpleButtonVO dzSimpleButtonVO = new DzSimpleButtonVO();
        dzSimpleButtonVO.setType(ButtonTypeEnums.BUTTON.getType());
        dzSimpleButtonVO.setJumpUrl(productM.getJumpUrl());
        dzSimpleButtonVO.setName("预订");
        return dzSimpleButtonVO;
    }

    @Override
    public FloatTagVO itemComponentPreTitleTag(ActivityContext activityContext, String groupName, long filterId, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFloorBuilder.itemComponentPreTitleTag(ActivityContext,String,long,ProductM)");
        if (StringUtils.isEmpty(productM.getAttr(GeneralProductAttrEnum.ATTR_HEALTH_CHECK_PRODUCT_TAG.getKey())) || filterId >= 0) {
            return null;
        }
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setLabel(new RichLabelVO(14, ColorUtils.color111111, productM.getAttr(GeneralProductAttrEnum.ATTR_HEALTH_CHECK_PRODUCT_TAG.getKey())));
        return floatTagVO;
    }

}
