package com.sankuai.dzviewscene.shelf.framework;

import com.sankuai.dzviewscene.shelf.framework.core.*;

import java.util.List;

/**
 * 框架扩展点查找器
 * <p>
 * Created by float on 2020/8/22.
 */
public interface ComponentFinder {

    /**
     * 查找指定活动的上下文构造器, 找不到抛异常
     *
     * @param activityCode
     * @return
     */
    IContextBuilder findContextBuilder(String activityCode);

    /**
     * 查找指定活动的场景识别器, 找不到抛异常
     *
     * @param activityCode
     * @return
     */
    ISceneIdentifier findSceneIdentifier(String activityCode);

    /**
     * 查找指定活动实例, 找不到抛异常
     *
     * @param activityCode
     * @return
     */
    IActivity findActivity(String activityCode);

    /**
     * 查找模板
     * @param activityContext
     * @return
     */
    IActivityTemplate findTemplate(ActivityContext activityContext);

    /**
     * 查找活动校验器, 找不到返回空
     *
     * @param activityContext
     * @return
     */
    List<IActivityValidator> findActivityValidators(ActivityContext activityContext);

    /**
     * 查找上下文扩展器, 找不到返回空
     *
     * @param activityContext
     * @return
     */
    List<IContextExt> findContextExt(ActivityContext activityContext);

    /**
     * 查找能力实例, 找不到抛异常
     *
     * @param activityContext
     * @param abilityCode
     * @return
     */
    IAbility findAbility(ActivityContext activityContext, String abilityCode);

    /**
     * 查找扩展点实例, 找不到抛异常
     *
     * @param activityContext
     * @param extPointCode
     * @return
     */
    IExtPoint findExtPoint(ActivityContext activityContext, String extPointCode);

    /**
     * 查找活动流程实例
     *
     * @param activityContext
     * @return
     */
    IActivityFlow findActivityFlow(ActivityContext activityContext);
}
