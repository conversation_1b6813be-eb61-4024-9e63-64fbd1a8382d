package com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.ConstantUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.ButtonTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.utils.SchemaUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.shelf.wedding.config.WeddingConfig;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.AbstractDefaultFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 结婚货架楼层VO构造扩展点实现
 *
 * <AUTHOR>
 */
@ExtPointInstance(name = "结婚货架楼层VO构造扩展点实现")
public class WeddingShelfItemAreaBuilderExt extends AbstractDefaultFloorsBuilderExt implements InitializingBean {

    private static final String MORE_TITLE_TEMPLATE = "查看更多%d个精选商品";
    private static final String MORE_JUMP_URL = "%s/migrate-product-universal-web/product-shelf/index.html?poiid=%d&cityid=%d&shopid=%d&shopuuid=%s&shelfcomponentid=%s&shelfnavtagid=%d";
    private static final String JUMP_UTL_TEMPLATE = "%s/migrate-product-universal-web/detail/wedding-product/index.html?productid=%d&shopid=%d&channelofurl=%d&sputypeofurl=%s&shopuuid=%s";
    private static final int MAX_SHOW_NUM = 6;

    @ConfigValue(key = WeddingConfig.WEDDING_LION_KEY)
    private WeddingConfig weddingConfig;
    /**
     * 价格优惠样式变更开关
     * true为带详情的优惠
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.promo.unify.switch", defaultValue = "false")
    private boolean unifyPriceSwitch;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.wedding.prepay.product.constant.config", defaultValue = "{}")
    private FloorConstant floorConstant;

    @Resource
    private WeddingDressShelfItemAreaBuilderExt weddingDressShelfItemAreaBuilderExt;
    @Resource
    private WeddingTravelPhotoShelfItemAreaBuilderExt weddingTravelPhotoShelfItemAreaBuilderExt;
    @Resource
    private WeddingIdPhotoShelfItemAreaBuilderExt weddingIdPhotoShelfItemAreaBuilderExt;
    @Resource
    private WeddingShelfSimpleItemAreaBuilderExt weddingShelfSimpleItemAreaBuilderExt;

    private Map<String, FloorsBuilderExtAdapter> spuTypeAreaBuilderMap = null;

    @Override
    public void afterPropertiesSet() {
        spuTypeAreaBuilderMap = new HashMap<String, FloorsBuilderExtAdapter>(3) {{
            // 婚纱礼服
            put("1342", weddingDressShelfItemAreaBuilderExt);
            // 旅拍婚纱照
            put("1345", weddingTravelPhotoShelfItemAreaBuilderExt);
            // 形象证件照
            put("1367", weddingIdPhotoShelfItemAreaBuilderExt);
        }};
    }

    @Override
    public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.itemComponentPic(ActivityContext,String,ProductM)");
        DzPictureComponentVO pic = buildDzPictureComponentVO(productM, activityContext);
        return buildPicAreaVO(pic, productM.getAttr(GeneralProductAttrEnum.ATTR_DOUBLE_ROW_SHELF_ICON.key));
    }

    @Override
    public String itemComponentMarketPrice(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.itemComponentMarketPrice(ActivityContext,String,ProductM)");
        if (unifyPriceSwitch) {
            return EMPTY_VALUE;
        }
        return productM.getMarketPrice();
    }

    @Override
    public String itemComponentSale(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.itemComponentSale(ActivityContext,String,ProductM)");
        return productM.getSale().getSaleTag();
    }

    @Override
    public DzSimpleButtonVO itemComponentButton(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.itemComponentButton(ActivityContext,String,ProductM)");
        return buildEduCourseBuyBtn(buildProductJumpUrl(activityContext, productM));
    }

    @Override
    public int floorShowType(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.floorShowType(ActivityContext,String,ProductGroupM)");
        return 3;
    }

    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.floorDefaultShowNum(ActivityContext,String,List)");
        return MAX_SHOW_NUM;
    }

    @Override
    public List<String> itemComponentProductTags(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.itemComponentProductTags(ActivityContext,String,ProductM,long)");
        return buildProductTags(activityContext, groupName, productM, filterId);
    }

    @Override
    public String moreComponentText(ActivityContext activityContext, String groupName, ProductGroupM productGroupM, List<ProductM> currentProductMs) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.moreComponentText(ActivityContext,String,ProductGroupM,List)");
        return getMoreTitle(productGroupM, currentProductMs);
    }

    @Override
    public String moreComponentJumpUrl(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.moreComponentJumpUrl(ActivityContext,String,ProductGroupM)");
        Map<String, Object> params = activityContext.getParameters();
        return String.format(MORE_JUMP_URL, getDomain(params), getPoiId(params), getCityId(params), getPoiId(params), getShopUuId(params), getComponentId(activityContext), getSelectId(activityContext));
    }

    private String getDomain(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getDomain(java.util.Map)");
        int platform = getPlatform(params);
        if (PlatformUtil.isMT(platform)) {
            return SchemaUtils.getMTIDomain();
        }
        return SchemaUtils.getDPMDomain();
    }

    private String getComponentId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getComponentId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        if (MapUtils.isEmpty(weddingConfig.getCategoryFilterComponentMap())) {
            return StringUtils.EMPTY;
        }
        return weddingConfig.getCategoryFilterComponentMap().get(Integer.parseInt(getShopCategory(activityContext)));
    }

    private int getSelectId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getSelectId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.selectedFilterId);
    }

    @Override
    public String jumpUrl(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.jumpUrl(ActivityContext,String,ProductM)");
        return buildProductJumpUrl(activityContext, productM);
    }

    @Override
    public List<DzTagVO> itemComponentPriceBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.itemComponentPriceBottomTags(ActivityContext,String,ProductM)");
        if (CollectionUtils.isEmpty(productM.getCoupons())) {
            return null;
        }
        return productM.getCoupons().stream()
                .filter(productCouponM -> productCouponM != null && StringUtils.isNotEmpty(productCouponM.getCouponTag()))
                .map(productCouponM -> new DzTagVO("#FF6633", false, productCouponM.getCouponTag()))
                .collect(Collectors.toList());
    }

    private int getCityId(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getCityId(java.util.Map)");
        int platform = getPlatform(params);
        if (PlatformUtil.isMT(platform)) {
            return ParamsUtil.getIntSafely(params, ShelfActivityConstants.Params.mtCityId);
        }
        return ParamsUtil.getIntSafely(params, ShelfActivityConstants.Params.dpCityId);
    }

    private List<String> buildProductTags(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.buildProductTags(ActivityContext,String,ProductM,long)");
        String spuType = getSpuType(productM);
        return Optional.ofNullable(spuTypeAreaBuilderMap.get(spuType)).orElse(weddingShelfSimpleItemAreaBuilderExt).itemComponentProductTags(activityContext, groupName, productM, filterId);
    }

    private String getSpuType(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getSpuType(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        return productM.getAttr(QueryFetcher.Params.spuType);
    }

    private PicAreaVO buildPicAreaVO(DzPictureComponentVO pic, String activityIcon) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.buildPicAreaVO(com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO,java.lang.String)");
        PicAreaVO picAreaVO = new PicAreaVO();
        picAreaVO.setPic(pic);
        picAreaVO.setFloatTags(buildFloatTags(activityIcon));
        return picAreaVO;
    }

    private List<FloatTagVO> buildFloatTags(String activityIcon) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.buildFloatTags(java.lang.String)");
        if (StringUtils.isEmpty(activityIcon)) {
            return null;
        }
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setPosition(floorConstant.floatTagPosition);
        floatTagVO.setIcon(new DzPictureComponentVO(activityIcon, floorConstant.floatPicAspectRadio));
        return Lists.newArrayList(floatTagVO);
    }

    private DzPictureComponentVO buildDzPictureComponentVO(ProductM productM, ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.buildDzPictureComponentVO(ProductM,ActivityContext)");
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setAspectRadio(getRatio(activityContext));
        pic.setPicUrl(getCutPic(productM, activityContext));
        return pic;
    }

    private String getShopCategory(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getShopCategory(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        ShopM shopM = ParamsUtil.getValue(activityContext, ShelfActivityConstants.Ctx.ctxShop, null);
        if (shopM == null) {
            return StringUtils.EMPTY;
        }
        return String.valueOf(shopM.getCategory());
    }

    private double getRatio(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getRatio(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        Map<String, Object> cutRatioMap = weddingConfig.getProductPicCutMap().get(getShopCategory(activityContext));
        if (MapUtils.isEmpty(cutRatioMap)) {
            return ConstantUtils.ASPECT_RADIO;
        }
        return Double.parseDouble(cutRatioMap.getOrDefault(WeddingConfig.Constant.RATIO, ConstantUtils.ASPECT_RADIO).toString());
    }

    private String getCutPic(ProductM productM, ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getCutPic(ProductM,ActivityContext)");
        Map<String, Object> picCutMap = weddingConfig.getProductPicCutMap().getOrDefault(getShopCategory(activityContext), new HashMap<>());
        if (MapUtils.isEmpty(picCutMap)) {
            return PictureURLBuilders.toHttpsUrl(productM.getPicUrl(), 480, 360, PictureURLBuilders.ScaleType.Cut);
        }
        return PictureURLBuilders.toHttpsUrl(productM.getPicUrl(), Integer.parseInt(picCutMap.get(WeddingConfig.Constant.PIC_WIDTH).toString()), Integer.parseInt(picCutMap.get(WeddingConfig.Constant.PIC_HEIGHT).toString()), PictureURLBuilders.ScaleType.Cut);
    }


    private String getMoreTitle(ProductGroupM productGroupM, List<ProductM> currentProductMs) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getMoreTitle(com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM,java.util.List)");
        if (productGroupM.getTotal() <= MAX_SHOW_NUM) {
            return null;
        }
        return String.format(MORE_TITLE_TEMPLATE, (productGroupM.getTotal() - MAX_SHOW_NUM));
    }


    private String buildProductJumpUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.buildProductJumpUrl(ActivityContext,ProductM)");
        if (StringUtils.isNotBlank(productM.getJumpUrl())) {
            return productM.getJumpUrl();
        }
        Map<String, Object> params = activityContext.getParameters();
        return String.format(JUMP_UTL_TEMPLATE, SchemaUtils.getDPMDomain(), productM.getProductId(), getPoiId(params), getPlatform(params), getSpuType(productM), getShopUuId(params));
    }

    /**
     * 构造货架-按钮区域
     */
    private DzSimpleButtonVO buildEduCourseBuyBtn(String jumpUrl) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.buildEduCourseBuyBtn(java.lang.String)");
        DzSimpleButtonVO buttonVO = new DzSimpleButtonVO();
        buttonVO.setJumpUrl(jumpUrl);
        buttonVO.setName("查看");
        buttonVO.setType(ButtonTypeEnums.BUTTON.getType());
        return buttonVO;
    }

    private String getShopUuId(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getShopUuId(java.util.Map)");
        Object shopUuId = ParamsUtil.getValue(params, ShelfActivityConstants.Params.shopUuid, null);
        if (shopUuId == null) {
            return "";
        }
        return shopUuId.toString();
    }

    public long getPoiId(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getPoiId(java.util.Map)");
        int platform = getPlatform(params);
        if (PlatformUtil.isMT(platform)) {
            return PoiIdUtil.getMtPoiIdL(params, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(params, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }

    private int getPlatform(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfItemAreaBuilderExt.getPlatform(java.util.Map)");
        return ParamsUtil.getIntSafely(params, ShelfActivityConstants.Params.platform);
    }

    @Data
    public static class FloorConstant {
        private int floatTagPosition;
        private double floatPicAspectRadio;
    }
}
