package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle;

import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.framework.exception.ComponentNotFoundException;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import joptsimple.internal.Strings;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/8/26.
 */
@AbilityInstance(name = "默认主标题模块构能力")
public class ShelfMainTitleBuilder extends MainTitleBuilder {

    @Override
    public CompletableFuture<MainTitleComponentVO> build(ActivityContext ctx) {
        MainTitleComponentVO mainTitleComponentVO = new MainTitleComponentVO();
        try {
            MainTitleBuilderExt mainTitleBuilderExt = findExtPoint(ctx, MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE);
            if (mainTitleBuilderExt instanceof NullMainTitleBuilderExt) {
                return CompletableFuture.completedFuture(null);
            }
            mainTitleComponentVO.setJumpUrl(getJumpUrl(mainTitleBuilderExt, ctx));
            mainTitleComponentVO.setIcon(getIcon(mainTitleBuilderExt, ctx));
            mainTitleComponentVO.setTitle(getTitle(mainTitleBuilderExt, ctx));
            mainTitleComponentVO.setSubTitle(getSubTitle(mainTitleBuilderExt, ctx));
            mainTitleComponentVO.setTags(getTags(mainTitleBuilderExt, ctx));
        } catch (ComponentNotFoundException componentNotFoundException) {
            ShelfErrorUtils.reportComponentNotFoundExceptionEvent(ctx, MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
        }
        return CompletableFuture.completedFuture(mainTitleComponentVO);
    }

    private RichLabelVO getSubTitle(MainTitleBuilderExt mainTitleBuilderExt, ActivityContext ctx) {
        try {
            return mainTitleBuilderExt.subTitle(ctx);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
        }
        return null;
    }

    private String getIcon(MainTitleBuilderExt mainTitleBuilderExt, ActivityContext ctx) {
        try {
            return mainTitleBuilderExt.icon(ctx);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
        }
        return Strings.EMPTY;
    }

    private String getTitle(MainTitleBuilderExt mainTitleBuilderExt, ActivityContext ctx) {
        try {
            return mainTitleBuilderExt.title(ctx);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
        }
        return Strings.EMPTY;
    }

    private List<IconRichLabelVO> getTags(MainTitleBuilderExt mainTitleBuilderExt, ActivityContext ctx) {
        try {
            return mainTitleBuilderExt.tags(ctx);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
        }
        return null;
    }

    private String getJumpUrl(MainTitleBuilderExt mainTitleBuilderExt, ActivityContext ctx) {
        try {
            return mainTitleBuilderExt.jumpUrl(ctx);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
        }
        return Strings.EMPTY;
    }
}
