package com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.ExtTransformer;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.function.SwapFunction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/4/16 8:49 下午
 */
public class AviatorExtTransformer implements ExtTransformer {

    private String expression;

    static {
        AviatorEvaluator.addFunction(new SwapFunction());
    }

    public AviatorExtTransformer(String expression) {
        this.expression = expression;
    }

    @Override
    public List<RankingItem> ext(RankingContext context, List<RankingItem> items) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans.AviatorExtTransformer.ext(com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext,java.util.List)");
        Expression expression = AviatorEvaluator.getInstance().compile(this.expression, true);

        Object result = expression.execute(buildExtEnv(context, items));

        return convertToListSafely(result);
    }

    private List<RankingItem> convertToListSafely(Object value) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans.AviatorExtTransformer.convertToListSafely(java.lang.Object)");
        if (value == null) {
            return Lists.newArrayList();
        }
        if (value instanceof List) {
            return (List) value;
        }
        return Lists.newArrayList();
    }


    private Map<String, Object> buildExtEnv(RankingContext rankingContext, List<RankingItem> items) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans.AviatorExtTransformer.buildExtEnv(com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext,java.util.List)");
        Map<String, Object> env = new HashMap<>();
        env.put("pool", rankingContext.getPool());
        env.put("matched", rankingContext.getMatched());
        env.put("params", rankingContext.getParams());
        env.put("items", items);
        return env;
    }

    @Override
    public List<RankingItem> execute(RankingContext context, List<RankingItem> items) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans.AviatorExtTransformer.execute(com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext,java.util.List)");
        return ext(context, items);
    }


}
