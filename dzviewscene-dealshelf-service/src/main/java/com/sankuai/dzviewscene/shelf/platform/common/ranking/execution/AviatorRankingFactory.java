package com.sankuai.dzviewscene.shelf.platform.common.ranking.execution;

import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.CommandTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.Ranking;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingFactory;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.GroupByTransformer;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans.*;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.MultiplexRanking;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.SimplexRanking;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.Transformer;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.MultiplexTemplate;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.SimplexTemplate;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.Template;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.TransTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by float on 2020/11/8.
 */
public class AviatorRankingFactory implements RankingFactory {

    @Override
    public Ranking createRanking(Template rankingTemplate) {
        if (rankingTemplate instanceof SimplexTemplate) {
            return createSimplexRanking((SimplexTemplate) rankingTemplate);
        }
        if (rankingTemplate instanceof MultiplexTemplate) {
            return createMultiplexRanking((MultiplexTemplate) rankingTemplate);
        }
        return null;
    }

    private SimplexRanking createSimplexRanking(SimplexTemplate simplexTemplate) {
        List<TransTemplate> transTemplates = simplexTemplate.getTransformers();
        if (CollectionUtils.isEmpty(transTemplates)) {
            return null;
        }
        List<Transformer> transformers = createTransformers(transTemplates);
        return new SimplexRanking(transformers);
    }

    private MultiplexRanking createMultiplexRanking(MultiplexTemplate multiplexTemplate) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.AviatorRankingFactory.createMultiplexRanking(com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.MultiplexTemplate)");
        List<List<TransTemplate>> transTemplatesList = multiplexTemplate.getSimplexTransformers();

        List<SimplexRanking> simplexRankings = buildSimplexRankings(transTemplatesList);
        List<Transformer> transformers = createTransformers(multiplexTemplate.getTransformers());

        GroupByTransformer transformer = buildGroupByTransformer(multiplexTemplate.getGroupTransformer());
        return new MultiplexRanking(transformer, simplexRankings, transformers);
    }

    private GroupByTransformer buildGroupByTransformer(TransTemplate transTemplate) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.AviatorRankingFactory.buildGroupByTransformer(com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.TransTemplate)");
        if (transTemplate == null) {
            return null;
        }
        return new AviatorGroupByTransformer(transTemplate.getExpression());
    }

    private List<SimplexRanking> buildSimplexRankings(List<List<TransTemplate>> TransTemplatesList) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.AviatorRankingFactory.buildSimplexRankings(java.util.List)");
        if (CollectionUtils.isEmpty(TransTemplatesList)) {
            return Lists.newArrayList();
        }
        return TransTemplatesList.stream().map(transTemplates -> {
            List<Transformer> transformers = createTransformers(transTemplates);
            return new SimplexRanking(transformers);
        }).collect(Collectors.toList());
    }


    // 多个排序组合在一起, 实现多值排序
    private List<Transformer> createTransformers(List<TransTemplate> transTemplates) {
        if (CollectionUtils.isEmpty(transTemplates)) {
            return Lists.newArrayList();
        }
        List<Transformer> transformers = new ArrayList<>();
        List<String> sortExpressions = new ArrayList<>();
        for (TransTemplate transTemplate : transTemplates) {
            if (!CommandTypeEnum.SORT.name.equals(transTemplate.getType())
                    && !CollectionUtils.isEmpty(sortExpressions)) {
                transformers.add(new AviatorCompareTransformer(new ArrayList<>(sortExpressions)));
                sortExpressions.clear();
            }
            if (CommandTypeEnum.MATCH.name.equals(transTemplate.getType())) {
                transformers.add(new AviatorMatchTransformer(transTemplate.getExpression()));
            }
            if (CommandTypeEnum.LIMIT.name.equals(transTemplate.getType())) {
                transformers.add(new AviatorTopNTransformer(transTemplate.getExpression()));
            }
            if (CommandTypeEnum.REF.name.equals(transTemplate.getType())) {
                transformers.add(new AviatorRefTransformer(transTemplate.getExpression()));
            }
            if (CommandTypeEnum.EXT.name.equals(transTemplate.getType())) {
                transformers.add(new AviatorExtTransformer(transTemplate.getExpression()));
            }
            if (CommandTypeEnum.SORT.name.equals(transTemplate.getType())) {
                sortExpressions.add(transTemplate.getExpression());
            }
        }
        if (!CollectionUtils.isEmpty(sortExpressions)) {
            transformers.add(new AviatorCompareTransformer(sortExpressions));
        }
        return transformers;
    }

}