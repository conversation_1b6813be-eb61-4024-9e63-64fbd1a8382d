package com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls;

import lombok.Data;

import java.util.List;

/**
 * 多路召回策略配置模板
 *
 * Created by float.lu on 2020/11/7.
 */
@Data
public class MultiplexTemplate extends Template {

    /**
     * 分组规则
     */
    private TransTemplate groupTransformer;
    /**
     * 单路召回规则列表
     */
    private List<List<TransTemplate>> simplexTransformers;
    /**
     * 叠加的排序算子及规则列表
     */
    private List<TransTemplate> transformers;
}
