package com.sankuai.dzviewscene.shelf.business.shelf.backroom.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.util.List;

/**
 * Title: PoolInfoVO
 * Description: 密室拼场规则信息
 *
 * <AUTHOR>
 * @date 2020-09-16
 */
@Data
@MobileDo(id = 0x9992)
public class PoolRuleDescVO {

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 子模块列表
     */
    @MobileDo.MobileField(key = 0x987a)
    private List<PoolRuleItemDescVO> itemList;
}
