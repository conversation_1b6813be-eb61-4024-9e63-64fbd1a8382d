package com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder;

import com.dianping.cat.Cat;
import com.dianping.dztrade.refund.api.bean.ShopRefundConfigDTO;
import com.google.common.collect.Lists;
import com.sankuai.athena.client.executor.Futures;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.ConstantUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.shelf.snapshot.context.BookingRefundRuleContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @title 快照预订主标题扩展点
 * @package com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder
 * @date 2021/6/10
 */
@Component
public class SnapshotBookingMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    private static final String REFUND_FORMAT_TEXT = "提前%d小时可退";

    private static final String TITLE = "在线预订";

    @Override
    public String title(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder.SnapshotBookingMainTitleBuilderExt.title(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return TITLE;
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder.SnapshotBookingMainTitleBuilderExt.tags(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<IconRichLabelVO> iconRichLabelList = Lists.newArrayList();
        String refundText = buildRefundText(ctx);
        if (StringUtils.isNotBlank(refundText)) {
            iconRichLabelList.add(buildRefundTag(refundText));
        }
        return iconRichLabelList;
    }

    private IconRichLabelVO buildRefundTag(String refundText) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder.SnapshotBookingMainTitleBuilderExt.buildRefundTag(java.lang.String)");
        IconRichLabelVO iconRichLabelVO = new IconRichLabelVO();
        iconRichLabelVO.setText(new RichLabelVO(11, ColorUtils.color777777, refundText));
        iconRichLabelVO.setIcon(ConstantUtils.CHECK_MARK_ICON_GRAY);
        return iconRichLabelVO;
    }

    private ShopRefundConfigDTO getShopRefundConfigDTO(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder.SnapshotBookingMainTitleBuilderExt.getShopRefundConfigDTO(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CompletableFuture<ShopRefundConfigDTO> snapshotRefundConfigDTOCf = activityContext.getExtContext(BookingRefundRuleContext.CONTEXT_KEY);
        return Futures.get(snapshotRefundConfigDTOCf);
    }

    private String buildRefundText(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder.SnapshotBookingMainTitleBuilderExt.buildRefundText(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        ShopRefundConfigDTO shopRefundConfigDTO = getShopRefundConfigDTO(ctx);
        if (shopRefundConfigDTO == null || shopRefundConfigDTO.getRefundBefore() < 60 ) {
            return StringUtils.EMPTY;
        }
        return String.format(REFUND_FORMAT_TEXT, shopRefundConfigDTO.getRefundBefore() / 60);
    }

    @Override
    public String icon(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder.SnapshotBookingMainTitleBuilderExt.icon(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        if (PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform))) {
            return ConstantUtils.MT_BOOKING_TITLE_ICON;
        }
        return ConstantUtils.DP_BOOKING_TITLE_ICON;
    }
}
