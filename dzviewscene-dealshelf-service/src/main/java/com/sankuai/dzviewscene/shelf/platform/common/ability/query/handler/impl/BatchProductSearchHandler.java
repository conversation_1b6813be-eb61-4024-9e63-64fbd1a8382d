package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.dianping.cat.Cat;
import com.dianping.degrade.util.JsonCodec;
import com.dianping.general.unified.search.api.generalsearchv2.dto.ProductSearchIdDto;
import com.dianping.general.unified.search.api.generalsearchv2.request.BaseQuery;
import com.dianping.general.unified.search.api.generalsearchv2.request.ProductSearchRequestV2;
import com.dianping.general.unified.search.api.sku.ProductSearchResponse;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.monitor.TraceElement;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.GroupQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import graphql.execution.Async;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 批量商品搜索
 */
@Component
public class BatchProductSearchHandler implements GroupQueryHandler {

    /**
     * 默认每次请求商品数量限制
     */
    private static final int DEFAULT_PRODUCT_LIMIT = 100;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityContext activityContext, String groupName, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.BatchProductSearchHandler.query(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String,java.util.Map)");
        CompletableFuture<List<ProductSearchResponse<ProductSearchIdDto>>> productSearchResponseListFuture = getProductSearchResponseListFuture(activityContext, params);
        return productSearchResponseListFuture.thenApply(this::buildProductGroupM);
    }

    private ProductGroupM buildProductGroupM(List<ProductSearchResponse<ProductSearchIdDto>> productSearchResponseList) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.BatchProductSearchHandler.buildProductGroupM(java.util.List)");
        if (CollectionUtils.isEmpty(productSearchResponseList)) {
            return null;
        }
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(buildProducts(productSearchResponseList));
        return productGroupM;
    }

    private List<ProductM> buildProducts(List<ProductSearchResponse<ProductSearchIdDto>> productSearchResponseList) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.BatchProductSearchHandler.buildProducts(java.util.List)");
        List<ProductM> productMList = new ArrayList<>();
        for (ProductSearchResponse<ProductSearchIdDto> productSearchResponse : productSearchResponseList) {
            if (productSearchResponse == null || CollectionUtils.isEmpty(productSearchResponse.getResult())) {
                continue;
            }
            productMList.addAll(productSearchResponse.getResult().stream().map(productDto -> {
                if (Objects.isNull(productDto) || Objects.isNull(productDto.getProductId())) {
                    return null;
                }
                List<Long> shopId = getShopId(productDto.getShopIds());
                ProductM productM = new ProductM();
                productM.setProductId(productDto.getProductId().intValue());
                productM.setShopIds(shopId.stream().map(Long::intValue).collect(Collectors.toList()));
                productM.setShopLongIds(shopId);
                return productM;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return productMList;
    }

    private List<Long> getShopId(List<Long> shopIdLongs) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.BatchProductSearchHandler.getShopId(java.util.List)");
        if (CollectionUtils.isEmpty(shopIdLongs)) {
            return Collections.emptyList();
        }
        return shopIdLongs.stream()
                .filter(shopId -> shopId > 0)
                .collect(Collectors.toList());
    }

    private CompletableFuture<List<ProductSearchResponse<ProductSearchIdDto>>> getProductSearchResponseListFuture(ActivityContext ctx, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.BatchProductSearchHandler.getProductSearchResponseListFuture(ActivityContext,Map)");
        List<CompletableFuture<ProductSearchResponse<ProductSearchIdDto>>> productSearchResponseFutures = new ArrayList<>();
        int maxPageCount = getMaxPageCount(ctx, params);
        for (int pageNo = 0 ; pageNo < maxPageCount ; pageNo ++) {
            ProductSearchRequestV2 productSearchRequestV2 = buildProductSearchRequestV2(ctx, pageNo, params);
            CompletableFuture<ProductSearchResponse<ProductSearchIdDto>> productSearchFuture = compositeAtomService.searchProductServiceV2(productSearchRequestV2);
            addInvokeTraceElement(ctx, System.currentTimeMillis(), productSearchFuture, productSearchRequestV2);
            productSearchResponseFutures.add(productSearchFuture);
        }
        return Async.each(productSearchResponseFutures);
    }

    private int getMaxPageCount(ActivityContext ctx, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.BatchProductSearchHandler.getMaxPageCount(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.Map)");
        int queryPageMaxCount = ParamsUtil.getIntSafely(params, QueryFetcher.Params.queryPageMaxCount);
        //最大页数优先以模板中
        if (queryPageMaxCount > 0) {
            return queryPageMaxCount;
        }
        //默认召回1页
        return 1;
    }

    private ProductSearchRequestV2 buildProductSearchRequestV2(ActivityContext ctx, int pageNo, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.BatchProductSearchHandler.buildProductSearchRequestV2(ActivityContext,int,Map)");
        int dpCityId = ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.dpCityId);
        ProductSearchRequestV2 productSearchRequestV2 = new ProductSearchRequestV2();
        BaseQuery baseQuery = new BaseQuery();
        baseQuery.setCustomerId(ParamsUtil.getLongSafely(params, QueryFetcher.Params.queryCustomerId));
        baseQuery.setProductTypes(Lists.newArrayList(ParamsUtil.getIntSafely(params, QueryFetcher.Params.productType)));
        baseQuery.setProductStatus(1);
        if (dpCityId > 0) {
            baseQuery.setCityIds(Lists.newArrayList(dpCityId));
        }
        baseQuery.setSpuTypes(Lists.newArrayList(ParamsUtil.getIntSafely(params, QueryFetcher.Params.spuType)));
        baseQuery.setDateFilterType(1);
        //页号从0开始
        productSearchRequestV2.setPagNum(pageNo);
        productSearchRequestV2.setPagSize(DEFAULT_PRODUCT_LIMIT);
        productSearchRequestV2.setBaseQuery(baseQuery);
        return productSearchRequestV2;
    }

    private void addInvokeTraceElement(ActivityContext activityContext, long startTime, CompletableFuture<ProductSearchResponse<ProductSearchIdDto>> productResponseFuture, ProductSearchRequestV2 request) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.BatchProductSearchHandler.addInvokeTraceElement(ActivityContext,long,CompletableFuture,ProductSearchRequestV2)");
        if (productResponseFuture == null || !activityContext.isTrace()) {
            return;
        }
        productResponseFuture.whenComplete((result, throwable) -> {
            try {
                activityContext.addTrace(TraceElement.build(
                        "业务点",
                        "按项目类型或客户ID等参数召回商品信息",
                        JsonCodec.encode(request),
                        result,
                        throwable,
                        System.currentTimeMillis() - startTime
                ));
            } catch (Exception e) {

            }
        });
    }

}
