package com.sankuai.dzviewscene.shelf.platform.common.ranking.execution;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.Ranking;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.SimplexRanking;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.Transformer;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.MultiplexTemplate;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.SimplexTemplate;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.Template;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.template.tpls.TransTemplate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AviatorRankingFactoryTest {

    private AviatorRankingFactory aviatorRankingFactory;

    @Mock
    private SimplexTemplate simplexTemplate;

    @Mock
    private MultiplexTemplate multiplexTemplate;

    @Mock
    private TransTemplate transTemplate;

    private void setUpCommonMocks() {
        aviatorRankingFactory = new AviatorRankingFactory();
        when(simplexTemplate.getTransformers()).thenReturn(Collections.singletonList(transTemplate));
        when(multiplexTemplate.getSimplexTransformers()).thenReturn(null);
    }

    @Test
    public void testCreateRankingWithSimplexTemplate() throws Throwable {
        setUpCommonMocks();
        Ranking result = aviatorRankingFactory.createRanking(simplexTemplate);
        assertNotNull(result);
    }

    @Test
    public void testCreateRankingWithMultiplexTemplate() throws Throwable {
        setUpCommonMocks();
        Ranking result = aviatorRankingFactory.createRanking(multiplexTemplate);
        assertNotNull(result);
    }

    @Test
    public void testCreateRankingWithInvalidTemplate() throws Throwable {
        setUpCommonMocks();
        Ranking result = aviatorRankingFactory.createRanking(new Template() {
        });
        assertNull(result);
    }
}
