package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.productshelf.vu.vo.VipPriceVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShelfFloorsBuilderExt_ItemComponentVipPriceTest {

    /**
     * 测试 itemComponentVipPrice 方法是否总是返回 null
     */
    @Test
    public void testItemComponentVipPriceReturnNull() throws Throwable {
        // arrange
        ShelfFloorsBuilderExt shelfFloorsBuilderExt = new ShelfFloorsBuilderExt();
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        ProductM productM = new ProductM();
        // act
        VipPriceVO result = shelfFloorsBuilderExt.itemComponentVipPrice(activityContext, groupName, productM);
        // assert
        assertNull(result);
    }
}
