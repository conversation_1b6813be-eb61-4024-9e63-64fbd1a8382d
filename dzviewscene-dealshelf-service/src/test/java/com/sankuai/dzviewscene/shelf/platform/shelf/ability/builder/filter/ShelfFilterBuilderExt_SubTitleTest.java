package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShelfFilterBuilderExt_SubTitleTest {

    /**
     * 测试 subTitle 方法是否返回 null
     */
    @Test
    public void testSubTitleReturnNull() throws Throwable {
        // arrange
        ShelfFilterBuilderExt shelfFilterBuilderExt = new ShelfFilterBuilderExt();
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        FilterBtnM filterBtnM = new FilterBtnM();
        // act
        RichLabelVO result = shelfFilterBuilderExt.subTitle(activityContext, groupName, filterBtnM);
        // assert
        assertNull(result);
    }
}
