package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.productlist;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.exception.ComponentNotFoundException;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzIconTagVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductListBuilder_ListTagsTest {

    @Mock
    private ActivityContext activityContext;

    @Mock
    private ComponentFinder componentFinder;

    @InjectMocks
    private DefaultProductListBuilder defaultProductListBuilder;

    private MockedStatic<ShelfErrorUtils> mockedShelfErrorUtils;

    @Before
    public void setUp() {
        mockedShelfErrorUtils = mockStatic(ShelfErrorUtils.class);
    }

    @After
    public void tearDown() {
        mockedShelfErrorUtils.close();
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testListTagsNormal() throws Throwable {
        // arrange
        ProductListBuilderExt productListBuilderExt = mock(ProductListBuilderExt.class);
        List<DzIconTagVO> expected = Collections.singletonList(new DzIconTagVO());
        when(componentFinder.findExtPoint(any(), anyString())).thenReturn(productListBuilderExt);
        when(productListBuilderExt.listTags(activityContext)).thenReturn(expected);
        // act
        List<DzIconTagVO> actual = defaultProductListBuilder.listTags(activityContext);
        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试找不到扩展点的情况
     */
    @Test
    public void testListTagsComponentNotFoundException() throws Throwable {
        // arrange
        when(componentFinder.findExtPoint(any(), anyString())).thenThrow(ComponentNotFoundException.class);
        // act
        List<DzIconTagVO> actual = defaultProductListBuilder.listTags(activityContext);
        // assert
        assertNull(actual);
        mockedShelfErrorUtils.verify(() -> ShelfErrorUtils.reportComponentNotFoundExceptionEvent(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE));
    }

    /**
     * 测试获取标签列表过程中出现其他异常的情况
     */
    @Test
    public void testListTagsOtherException() throws Throwable {
        // arrange
        ProductListBuilderExt productListBuilderExt = mock(ProductListBuilderExt.class);
        when(componentFinder.findExtPoint(any(), anyString())).thenReturn(productListBuilderExt);
        when(productListBuilderExt.listTags(activityContext)).thenThrow(RuntimeException.class);
        // act
        List<DzIconTagVO> actual = defaultProductListBuilder.listTags(activityContext);
        // assert
        assertNull(actual);
        mockedShelfErrorUtils.verify(() -> ShelfErrorUtils.addBuilderCatError(eq(activityContext), any(RuntimeException.class)));
    }
}
