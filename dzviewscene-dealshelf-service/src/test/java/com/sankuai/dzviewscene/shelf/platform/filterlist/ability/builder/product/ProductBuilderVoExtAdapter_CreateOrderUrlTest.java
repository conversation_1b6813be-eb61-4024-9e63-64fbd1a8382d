package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class ProductBuilderVoExtAdapter_CreateOrderUrlTest {

    /**
     * Test when ProductM is null
     */
    @Test
    public void testCreateOrderUrl_NullProductM() throws Throwable {
        // arrange
        ProductBuilderVoExtAdapter productBuilder = mock(ProductBuilderVoExtAdapter.class, CALLS_REAL_METHODS);
        ActivityContext activityContext = mock(ActivityContext.class);
        ProductM productM = null;
        // act
        String result = productBuilder.createOrderUrl(activityContext, productM);
        // assert
        assertNull(result);
    }

    /**
     * Test when ActivityContext is unsuccessful
     */
    @Test
    public void testCreateOrderUrl_UnsuccessfulActivityContext() throws Throwable {
        // arrange
        ProductBuilderVoExtAdapter productBuilder = mock(ProductBuilderVoExtAdapter.class, CALLS_REAL_METHODS);
        ActivityContext activityContext = mock(ActivityContext.class);
        when(activityContext.isSuccess()).thenReturn(false);
        ProductM productM = mock(ProductM.class);
        when(productM.getAvailable()).thenReturn(true);
        // act
        String result = productBuilder.createOrderUrl(activityContext, productM);
        // assert
        assertNull(result);
    }

    /**
     * Test when ProductM is unavailable
     */
    @Test
    public void testCreateOrderUrl_UnavailableProduct() throws Throwable {
        // arrange
        ProductBuilderVoExtAdapter productBuilder = mock(ProductBuilderVoExtAdapter.class, CALLS_REAL_METHODS);
        ActivityContext activityContext = mock(ActivityContext.class);
        when(activityContext.isSuccess()).thenReturn(true);
        ProductM productM = mock(ProductM.class);
        when(productM.getAvailable()).thenReturn(false);
        // act
        String result = productBuilder.createOrderUrl(activityContext, productM);
        // assert
        assertNull(result);
    }

    /**
     * Test successful creation of order URL
     */
    @Test
    public void testCreateOrderUrl_Success() throws Throwable {
        // arrange
        ProductBuilderVoExtAdapter productBuilder = new ProductBuilderVoExtAdapter() {

            @Override
            public String createOrderUrl(ActivityContext activityContext, ProductM productM) {
                return "https://order.example.com?productId=" + productM.getProductId() + "&activityCode=" + activityContext.getActivityCode();
            }
        };
        ActivityContext activityContext = mock(ActivityContext.class);
        when(activityContext.isSuccess()).thenReturn(true);
        when(activityContext.getActivityCode()).thenReturn("ACT123");
        ProductM productM = mock(ProductM.class);
        when(productM.getAvailable()).thenReturn(true);
        when(productM.getProductId()).thenReturn(456);
        // act
        String result = productBuilder.createOrderUrl(activityContext, productM);
        // assert
        assertEquals("https://order.example.com?productId=456&activityCode=ACT123", result);
    }
}
