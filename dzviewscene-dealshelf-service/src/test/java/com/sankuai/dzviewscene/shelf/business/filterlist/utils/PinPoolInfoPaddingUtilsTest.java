package com.sankuai.dzviewscene.shelf.business.filterlist.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.joy.booking.api.generalpool.enums.PoolStatusEnum;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzTagVO;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PinPoolInfoPaddingUtilsTest {

    @Test
    public void testBuildUserHasJoinedPinPoolProgressDescRePooling() throws Throwable {
        ProductPinPoolM poolInfo = new ProductPinPoolM();
        poolInfo.setPoolStatus(PoolStatusEnum.RE_POOLING.code);
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildUserHasJoinedPinPoolProgressDesc(poolInfo);
        assertEquals(1, result.size());
        assertEquals("您已加入|该场次已拼成", result.get(0).getText());
    }

    @Test
    public void testBuildUserHasJoinedPinPoolProgressDescPoolSuccess() throws Throwable {
        ProductPinPoolM poolInfo = new ProductPinPoolM();
        poolInfo.setPoolStatus(PoolStatusEnum.POOL_SUCCESS.code);
        // Assuming isCanJoin is true for POOL_SUCCESS status
        // Simulate isCanJoin returning true
        poolInfo.setCanJoin(true);
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildUserHasJoinedPinPoolProgressDesc(poolInfo);
        assertEquals(1, result.size());
        assertEquals("您已加入|该场次已拼成", result.get(0).getText());
    }

    @Test
    public void testBuildUserHasJoinedPinPoolProgressDescOther() throws Throwable {
        ProductPinPoolM poolInfo = new ProductPinPoolM();
        poolInfo.setPoolStatus(-1);
        poolInfo.setCurrentNum(3);
        poolInfo.setMinNum(5);
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildUserHasJoinedPinPoolProgressDesc(poolInfo);
        assertEquals(1, result.size());
        assertEquals("您已加入|差2人可开场", result.get(0).getText());
    }

    /**
     * 测试 buildFullPeopleTag 方法是否能正确创建 DzTagVO 对象并添加到列表中
     */
    @Test
    public void testBuildFullPeopleTag() throws Throwable {
        // arrange
        int expectedTextSize = FrontSizeUtils.front20;
        String expectedName = "已锁车";
        String expectedTextColor = "#24BD78";
        String expectedBackground = "#EDFAF3";
        // act
        List<DzTagVO> result = PinPoolInfoPaddingUtils.buildFullPeopleTag();
        // assert
        assertEquals(1, result.size());
        DzTagVO dzTagVO = result.get(0);
        assertEquals(expectedTextSize, dzTagVO.getTextSize());
        assertEquals(expectedName, dzTagVO.getName());
        assertEquals(expectedTextColor, dzTagVO.getTextColor());
        assertEquals(expectedBackground, dzTagVO.getBackground());
    }

    /**
     * 测试 buildLackOfPeopleTag 方法
     */
    @Test
    public void testBuildLackOfPeopleTag() throws Throwable {
        // arrange
        int expectedTextSize = FrontSizeUtils.front20;
        String expectedName = "缺人";
        String expectedTextColor = "#FF5F57";
        String expectedBackground = "#FFF5F2";
        // act
        List<DzTagVO> result = PinPoolInfoPaddingUtils.buildLackOfPeopleTag();
        // assert
        assertEquals(1, result.size());
        DzTagVO dzTagVO = result.get(0);
        assertEquals(expectedTextSize, dzTagVO.getTextSize());
        assertEquals(expectedName, dzTagVO.getName());
        assertEquals(expectedTextColor, dzTagVO.getTextColor());
        assertEquals(expectedBackground, dzTagVO.getBackground());
    }

    /**
     * 测试 buildFailedPoolDesc 方法，当 pinPoolM.getPoolGroups() 为空时
     */
    @Test
    public void testBuildFailedPoolDescWhenPoolGroupsIsEmpty() {
        // arrange
        ProductPinPoolM pinPoolM = new ProductPinPoolM();
        // act
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildFailedPoolDesc(pinPoolM);
        // assert
        assertEquals(1, result.size());
        assertEquals("已流车，不含线下玩家", result.get(0).getText());
    }

    /**
     * 测试 buildFailedPoolDesc 方法，当 pinPoolM.getPoolGroups() 不为空，但不存在 pinPoolGroupM.getOrderFrom() == 1 的条件时
     */
    @Test
    public void testBuildFailedPoolDescWhenPoolGroupsIsNotEmptyAndOrderFromIsNot1() {
        // arrange
        ProductPinPoolM pinPoolM = new ProductPinPoolM();
        ProductPinPoolGroupM pinPoolGroupM = new ProductPinPoolGroupM();
        pinPoolGroupM.setOrderFrom(2);
        pinPoolM.setPoolGroups(Arrays.asList(pinPoolGroupM));
        // act
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildFailedPoolDesc(pinPoolM);
        // assert
        assertEquals(1, result.size());
        assertEquals("已流车，不含线下玩家", result.get(0).getText());
    }

    /**
     * 测试 buildFailedPoolDesc 方法，当 pinPoolM.getPoolGroups() 不为空，且存在 pinPoolGroupM.getOrderFrom() == 1 的条件时
     */
    @Test
    public void testBuildFailedPoolDescWhenPoolGroupsIsNotEmptyAndOrderFromIs1() {
        // arrange
        ProductPinPoolM pinPoolM = new ProductPinPoolM();
        ProductPinPoolGroupM pinPoolGroupM = new ProductPinPoolGroupM();
        pinPoolGroupM.setOrderFrom(1);
        pinPoolM.setPoolGroups(Arrays.asList(pinPoolGroupM));
        // act
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildFailedPoolDesc(pinPoolM);
        // assert
        assertEquals(1, result.size());
        assertEquals("已流车，含线下玩家", result.get(0).getText());
    }

    /**
     * 测试 buildCanStartDesc 方法，当 maxJoinPeople 为正数时
     */
    @Test
    public void testBuildCanStartDescPositive() throws Throwable {
        // arrange
        int maxJoinPeople = 10;
        // act
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildCanStartDesc(maxJoinPeople);
        // assert
        assertEquals(3, result.size());
        assertEquals("可开场，还可加入", result.get(0).getText());
        assertEquals(String.valueOf(maxJoinPeople), result.get(1).getText());
        assertEquals("人", result.get(2).getText());
    }

    /**
     * 测试 buildCanStartDesc 方法，当 maxJoinPeople 为零时
     */
    @Test
    public void testBuildCanStartDescZero() throws Throwable {
        // arrange
        int maxJoinPeople = 0;
        // act
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildCanStartDesc(maxJoinPeople);
        // assert
        assertEquals(3, result.size());
        assertEquals("可开场，还可加入", result.get(0).getText());
        assertEquals(String.valueOf(maxJoinPeople), result.get(1).getText());
        assertEquals("人", result.get(2).getText());
    }

    /**
     * 测试 buildCanStartDesc 方法，当 maxJoinPeople 为负数时
     */
    @Test
    public void testBuildCanStartDescNegative() throws Throwable {
        // arrange
        int maxJoinPeople = -10;
        // act
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildCanStartDesc(maxJoinPeople);
        // assert
        assertEquals(3, result.size());
        assertEquals("可开场，还可加入", result.get(0).getText());
        assertEquals(String.valueOf(maxJoinPeople), result.get(1).getText());
        assertEquals("人", result.get(2).getText());
    }

    @Test
    public void testBuildUserNotJoinPinPoolProgressDescWhenPoolStatusIsRePooling() throws Throwable {
        ProductPinPoolM poolInfo = new ProductPinPoolM();
        poolInfo.setPoolStatus(PoolStatusEnum.RE_POOLING.code);
        // Assuming the method buildItemDescIfSuccess requires canJoin to be true
        poolInfo.setCanJoin(true);
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildUserNotJoinPinPoolProgressDesc(poolInfo);
        assertEquals(1, result.size());
    }

    @Test
    public void testBuildUserNotJoinPinPoolProgressDescWhenPoolStatusIsPooling() throws Throwable {
        ProductPinPoolM poolInfo = new ProductPinPoolM();
        poolInfo.setPoolStatus(PoolStatusEnum.POOLING.code);
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildUserNotJoinPinPoolProgressDesc(poolInfo);
        assertEquals(3, result.size());
    }

    @Test
    public void testBuildUserNotJoinPinPoolProgressDescWhenPoolStatusIsOther() throws Throwable {
        ProductPinPoolM poolInfo = new ProductPinPoolM();
        poolInfo.setPoolStatus(-1);
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildUserNotJoinPinPoolProgressDesc(poolInfo);
        assertEquals(0, result.size());
    }

    /**
     * 测试 buildFailureTag 方法
     */
    @Test
    public void testBuildFailureTag() throws Throwable {
        // arrange
        int expectedTextSize = FrontSizeUtils.front20;
        String expectedName = "即将流车";
        String expectedTextColor = "#FF5F57";
        String expectedBackground = "#FFF5F2";
        // act
        List<DzTagVO> result = PinPoolInfoPaddingUtils.buildFailureTag();
        // assert
        assertEquals(1, result.size());
        DzTagVO dzTagVO = result.get(0);
        assertEquals(expectedTextSize, dzTagVO.getTextSize());
        assertEquals(expectedName, dzTagVO.getName());
        assertEquals(expectedTextColor, dzTagVO.getTextColor());
        assertEquals(expectedBackground, dzTagVO.getBackground());
    }

    /**
     * Tests the buildNotStartDesc method when minJoinPeople and maxJoinPeople are equal.
     */
    @Test
    public void testBuildNotStartDescWhenMinAndMaxEqual() throws Throwable {
        // Arrange
        int minJoinPeople = 5;
        int maxJoinPeople = 5;
        // Act
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildNotStartDesc(minJoinPeople, maxJoinPeople);
        // Assert
        assertEquals(3, result.size());
        assertEquals("差", result.get(0).getText());
        assertEquals("5", result.get(1).getText());
        assertEquals("人可开场", result.get(2).getText());
    }

    /**
     * Tests the buildNotStartDesc method when minJoinPeople and maxJoinPeople are not equal.
     */
    @Test
    public void testBuildNotStartDescWhenMinAndMaxNotEqual() throws Throwable {
        // Arrange
        int minJoinPeople = 3;
        int maxJoinPeople = 7;
        // Act
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildNotStartDesc(minJoinPeople, maxJoinPeople);
        // Assert
        assertEquals(6, result.size());
        assertEquals("差", result.get(0).getText());
        assertEquals("3", result.get(1).getText());
        assertEquals("人可开场", result.get(2).getText());
        // Corrected the expected value
        assertEquals("，最多可加入", result.get(3).getText());
        // Corrected the expected value
        assertEquals("7", result.get(4).getText());
        assertEquals("人", result.get(5).getText());
    }

    /**
     * 测试 buildNoStockTag 方法
     */
    @Test
    public void testBuildNoStockTag() throws Throwable {
        // arrange
        int expectedTextSize = FrontSizeUtils.front20;
        String expectedName = "库存售罄";
        String expectedTextColor = "#FE8C00";
        String expectedBackground = "#FFF5E9";
        // act
        List<DzTagVO> result = PinPoolInfoPaddingUtils.buildNoStockTag();
        // assert
        assertEquals(1, result.size());
        DzTagVO dzTagVO = result.get(0);
        assertEquals(expectedTextSize, dzTagVO.getTextSize());
        assertEquals(expectedName, dzTagVO.getName());
        assertEquals(expectedTextColor, dzTagVO.getTextColor());
        assertEquals(expectedBackground, dzTagVO.getBackground());
    }

    /**
     * 测试 buildFullPeopleDesc 方法
     */
    @Test
    public void testBuildFullPeopleDesc() throws Throwable {
        // arrange
        // 无需准备任何数据
        // act
        List<RichLabelVO> result = PinPoolInfoPaddingUtils.buildFullPeopleDesc();
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("可开场，已满员/锁场", result.get(0).getText());
        assertEquals(12, result.get(0).getTextSize());
        assertEquals("#777777", result.get(0).getTextColor());
    }

    /**
     * 测试 buildCanStartTag 方法是否能正确返回一个 DzTagVO 对象
     */
    @Test
    public void testBuildCanStartTag() throws Throwable {
        // arrange
        // 无需准备任何数据
        // act
        List<DzTagVO> result = PinPoolInfoPaddingUtils.buildCanStartTag();
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DzTagVO dzTagVO = result.get(0);
        assertEquals(FrontSizeUtils.front20, dzTagVO.getTextSize());
        assertEquals("可开场", dzTagVO.getName());
        assertEquals("#1C6CDC", dzTagVO.getTextColor());
        assertEquals("#E8F0FB", dzTagVO.getBackground());
    }
}
