package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试通过分组的方式构造筛选数据
 *
 * Created by float.lu on 2020/9/11.
 */
public class MultiplexGroupFilterFetcherExtMock extends MultiplexFilterFetcherExtBaseMock {


    ////////////////////////////////////////完全自己实现召回逻辑的方式//////////////////////////////////////////////////
    @Override
    public Map<Long, List<ProductM>> groupByFilter(ActivityContext activityContext, String groupName, FilterM filterM, ProductGroupM productGroupM) {
        Map<Long, List<ProductM>> productGroups = new HashMap<>();
        productGroups.put(tab1, new ArrayList<>());
        productGroups.put(tab2, new ArrayList<>());
        productGroupM.getProducts().forEach(productM -> {
            filterM.getFilters().forEach(filterBtnM -> {
                if (productM.getProductId() % 2 == 0 && !contains(productGroups.get(tab1), productM)/*自己实现去重*/) {
                    productGroups.get(tab1).add(productM);//放全部
                }
                if (productM.getProductId() % 2 != 0 && !contains(productGroups.get(tab2), productM)/*自己实现去重*/) {
                    productGroups.get(tab2).add(productM);//放洗脚
                }
                // 其他不放
            });
        });
        return productGroups;
    }

    private boolean contains(List<ProductM> productMs, ProductM productM) {
        if (CollectionUtils.isEmpty(productMs)) {
            return false;
        }
        return productMs.stream().filter(productM1 -> productM.getProductId() == productM1.getProductId()).findFirst().orElse(null) != null;
    }
}
