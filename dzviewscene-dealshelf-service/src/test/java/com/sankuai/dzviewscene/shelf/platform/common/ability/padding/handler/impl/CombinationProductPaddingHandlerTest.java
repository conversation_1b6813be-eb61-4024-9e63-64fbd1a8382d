package com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl;

import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections.CollectionUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.sankuai.mppack.api.client.response.CombineProductInfoResponse;

import static org.junit.Assert.*;

import org.junit.*;
import org.junit.runner.RunWith.*;

import static org.mockito.Mockito.*;

import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.testng.collections.Lists;

@RunWith(MockitoJUnitRunner.class)
public class CombinationProductPaddingHandlerTest {

    @InjectMocks
    private CombinationProductPaddingHandler combinationProductPaddingHandler;

    @Mock
    private ActivityContext activityContext;

    @Mock
    private ProductGroupM productGroupM;

    @Mock
    private CompositeAtomService compositeAtomService;
    private MockedStatic<ExecutorServices> executorServicesMockedStatic;

    @Before
    public void onBefore() {
        executorServicesMockedStatic = Mockito.mockStatic(ExecutorServices.class);
    }

    @After
    public void onAfter() {
        executorServicesMockedStatic.close();
    }

    /**
     * 测试 productGroupM 或 params 为空的情况
     */
    @Test
    public void testPaddingWhenProductGroupMOrParamsIsNull() throws Throwable {
        // arrange
        Map<String, Object> params = new HashMap<>();
        productGroupM = null;
        // act
        CompletableFuture<ProductGroupM> result = combinationProductPaddingHandler.padding(activityContext, productGroupM, params);
        // assert
        Assert.assertNotNull(result.get());
    }

    /**
     * 测试 productGroupM 和 params 都不为空，但 productGroupM.getProducts() 为空的情况
     */
    @Test
    public void testPaddingWhenProductsIsEmpty() throws Throwable {
        // arrange
        Map<String, Object> params = new HashMap<>();
        when(productGroupM.getProducts()).thenReturn(null);
        // act
        CompletableFuture<ProductGroupM> result = combinationProductPaddingHandler.padding(activityContext, productGroupM, params);
        // assert
        Assert.assertNotNull(result.get());
    }

    /**
     * 测试 productGroupM 和 params 都不为空，productGroupM.getProducts() 也不为空的情况
     */
    @Test
    public void testPaddingWhenProductsIsNotEmpty() throws Throwable {
        // arrange
        Map<String, Object> params = new HashMap<>();
        when(productGroupM.getProducts()).thenReturn(new ArrayList<>());
        // act
        CompletableFuture<ProductGroupM> result = combinationProductPaddingHandler.padding(activityContext, productGroupM, params);
        // assert
        Assert.assertNotNull(result.get());
    }

    /**
     * 测试当 usePrePadding 返回 true 时，预填充逻辑是否被执行
     */
    @Test
    public void testPaddingWhenUsePrePaddingReturnsTrue() throws Throwable {
        // arrange
        ProductGroupM mockProductGroupM = mockProductGroupM();
        Map<String, Object> params = new HashMap<>();
        params.put(PaddingFetcher.Params.enablePaddingDiffProductTypes, Lists.newArrayList(ProductTypeEnum.PACK.getType()));

        //when(activityContext.getParameters()).thenReturn(params);
        executorServicesMockedStatic.when(() -> ExecutorServices.forThreadPoolExecutor(anyString(), anyInt(), anyInt(), anyInt(), any(), any())).thenReturn(null);

        // act
        CompletableFuture<ProductGroupM> result = combinationProductPaddingHandler.padding(activityContext, mockProductGroupM, params);

        // assert
        Assert.assertNotNull(result.get());
        Assert.assertFalse(result.get().getProducts().isEmpty());
        Assert.assertEquals(result.get().getProducts().get(0).getTitle(), "第1个商品预填充");
    }

    @Test
    public void testPaddingWhenUsePrePaddingReturnsTrueAndDiff() throws Throwable {
        // arrange
        ProductGroupM mockProductGroupM = mockProductGroupM();
        Map<String, Object> params = new HashMap<>();
        params.put(PaddingFetcher.Params.enablePaddingDiffProductTypes, Collections.singletonList(3));
        ActivityContext activityContext = new ActivityContext();
        activityContext.setParameters(params);

        CompletableFuture<CombineProductInfoResponse> future = CompletableFuture.completedFuture(new CombineProductInfoResponse());
        when(compositeAtomService.batchQueryCombinationProductInfo(any())).thenReturn(future);
        executorServicesMockedStatic.when(() -> ExecutorServices.forThreadPoolExecutor(anyString(), anyInt(), anyInt(), anyInt(), any(), any())).thenReturn(null);
        // act
        CompletableFuture<ProductGroupM> result = combinationProductPaddingHandler.padding(activityContext, mockProductGroupM, params);

        // assert
        Assert.assertNotNull(result.get());
        Assert.assertFalse(result.get().getProducts().isEmpty());
        Assert.assertEquals(result.get().getProducts().get(0).getTitle(), "第1个商品");
    }

    private ProductGroupM mockProductGroupM() {
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(new ArrayList<ProductM>() {{
            add(mockProduct(1, "第1个商品"));
            add(mockProduct(2, "第2个商品"));
            add(mockProduct(3, "第3个商品"));
            add(mockProduct(4, "第4个商品"));
            add(mockProduct(5, "第5个商品"));
            add(mockProduct(6, "第6个商品"));
        }});

        productGroupM.setPreLoadProducts(new HashMap<Integer, List<ProductM>>() {{
            put(ProductTypeEnum.PACK.getType(), Arrays.asList(
                    mockProduct(1, "第1个商品预填充"),
                    mockProduct(2, "第2个商品预填充")
            ));
        }});
        return productGroupM;
    }

    private ProductM mockProduct(int id, String title) {
        ProductM productM = new ProductM();
        productM.setProductId(id);
        productM.setTitle(title);
        return productM;
    }

}
