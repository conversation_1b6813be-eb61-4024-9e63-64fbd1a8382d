package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class MultiplexQueryFilterFetcher_BuildTest {

    @Mock
    private ActivityContext activityContext;

    @Test
    public void testBuildNoAttachment() throws Throwable {
        MultiplexQueryFilterFetcher multiplexQueryFilterFetcher = new MultiplexQueryFilterFetcher();
        CompletableFuture<Map<String, FilterM>> result = multiplexQueryFilterFetcher.build(activityContext);
        assertTrue(result.isDone());
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildWithAttachmentButNullFilters() throws Throwable {
        MultiplexQueryFilterFetcher multiplexQueryFilterFetcher = new MultiplexQueryFilterFetcher();
        CompletableFuture<Map<String, FilterM>> result = multiplexQueryFilterFetcher.build(activityContext);
        assertTrue(result.isDone());
        assertTrue(result.get().isEmpty());
    }
}
