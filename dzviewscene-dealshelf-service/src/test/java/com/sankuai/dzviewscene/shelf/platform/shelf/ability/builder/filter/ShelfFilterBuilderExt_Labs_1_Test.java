package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShelfFilterBuilderExt_Labs_1_Test {

    /**
     * 测试 labs 方法是否返回 null
     */
    @Test
    public void testLabsReturnNull() throws Throwable {
        // arrange
        ShelfFilterBuilderExt shelfFilterBuilderExt = new ShelfFilterBuilderExt();
        ActivityContext activityContext = new ActivityContext();
        FilterBtnM filterBtnM = new FilterBtnM();
        // act
        String result = shelfFilterBuilderExt.labs(activityContext, filterBtnM);
        // assert
        assertNull(result);
    }
}
