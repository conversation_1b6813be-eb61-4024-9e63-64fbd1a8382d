package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.powermock.reflect.Whitebox;

@RunWith(MockitoJUnitRunner.class)
public class GeneralSearchProductHandlerTest {

    @InjectMocks
    private GeneralSearchProductHandler generalSearchProductHandler;

    @Mock
    private ActivityContext activityContext;

    private void mockActivityContextForShopIds() {
        Map<String, List<Long>> mockMap = new HashMap<>();
        mockMap.put("groupName", Arrays.asList(1L, 2L));
        when(activityContext.getParam(QueryFetcher.Params.groupName2ShopIds)).thenReturn(mockMap);
    }

    private void mockActivityContextForPlatform() {
        when(activityContext.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
    }

    @Test
    public void testQueryNoPlatform() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        mockActivityContextForPlatform();
        CompletableFuture<ProductGroupM> result = generalSearchProductHandler.query(activityContext, "groupName", params);
        assertNotNull(result);
    }

    @Test
    public void testQueryLongPoiProcessEmptyShopIds() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        mockActivityContextForShopIds();
        mockActivityContextForPlatform();
        CompletableFuture<ProductGroupM> result = generalSearchProductHandler.query(activityContext, "groupName", params);
        assertNotNull(result);
    }

    @Test
    public void testQueryLongPoiProcessNotEmptyShopIds() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        mockActivityContextForShopIds();
        mockActivityContextForPlatform();
        CompletableFuture<ProductGroupM> result = generalSearchProductHandler.query(activityContext, "groupName", params);
        assertNotNull(result);
    }

    @Test
    public void testQueryOriginalProcessEmptyShopIds() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        mockActivityContextForShopIds();
        mockActivityContextForPlatform();
        CompletableFuture<ProductGroupM> result = generalSearchProductHandler.query(activityContext, "groupName", params);
        assertNotNull(result);
    }

    @Test
    public void testQueryOriginalProcessNotEmptyShopIds() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        mockActivityContextForShopIds();
        mockActivityContextForPlatform();
        CompletableFuture<ProductGroupM> result = generalSearchProductHandler.query(activityContext, "groupName", params);
        assertNotNull(result);
    }

    /**
     * Test query method when PoiMigrate is enabled and shopIds is not empty
     */
    @Test
    public void testQueryWhenPoiMigrateEnabledAndShopIdsNotEmpty() throws Throwable {
        // arrange
        String groupName = "testGroup";
        Map<String, Object> params = new HashMap<>();
        List<Long> shopIds = Arrays.asList(1L, 2L);
        Map<String, List<Long>> shopIdMap = new HashMap<>();
        shopIdMap.put(groupName, shopIds);
        try (MockedStatic<PoiMigrateUtils> poiMigrateUtils = mockStatic(PoiMigrateUtils.class)) {
            poiMigrateUtils.when(() -> PoiMigrateUtils.needLongPoiProcess(anyString())).thenReturn(true);
            when(activityContext.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
            when(activityContext.getParam(eq("groupName2ShopIds"))).thenReturn(shopIdMap);
            Map<String, Object> extParams = new HashMap<>();
            List<Object> tagIds = Arrays.asList("tag1", "tag2");
            extParams.put(QueryFetcher.Params.tagId, tagIds);
            when(activityContext.getParam(eq("extParams"))).thenReturn(extParams);
            // act
            CompletableFuture<ProductGroupM> result = generalSearchProductHandler.query(activityContext, groupName, params);
            // assert
            assertNotNull(result);
            verify(activityContext, times(1)).getParam(eq("groupName2ShopIds"));
        }
    }

    /**
     * Test query method when PoiMigrate is disabled and shopIds is not empty
     */
    @Test
    public void testQueryWhenPoiMigrateDisabledAndShopIdsNotEmpty() throws Throwable {
        // arrange
        String groupName = "testGroup";
        Map<String, Object> params = new HashMap<>();
        List<Integer> shopIds = Arrays.asList(1, 2);
        Map<String, List<Integer>> shopIdMap = new HashMap<>();
        shopIdMap.put(groupName, shopIds);
        try (MockedStatic<PoiMigrateUtils> poiMigrateUtils = mockStatic(PoiMigrateUtils.class)) {
            poiMigrateUtils.when(() -> PoiMigrateUtils.needLongPoiProcess(anyString())).thenReturn(false);
            when(activityContext.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
            when(activityContext.getParam(eq("groupName2ShopIds"))).thenReturn(shopIdMap);
            Map<String, Object> extParams = new HashMap<>();
            List<Object> tagIds = Arrays.asList("tag1", "tag2");
            extParams.put(QueryFetcher.Params.tagId, tagIds);
            when(activityContext.getParam(eq("extParams"))).thenReturn(extParams);
            // act
            CompletableFuture<ProductGroupM> result = generalSearchProductHandler.query(activityContext, groupName, params);
            // assert
            assertNotNull(result);
            verify(activityContext, times(1)).getParam(eq("groupName2ShopIds"));
        }
    }

    /**
     * Test query method when shopIds is empty in old logic path
     */
    @Test
    public void testQueryWhenShopIdsEmptyInOldLogic() throws Throwable {
        // arrange
        String groupName = "testGroup";
        Map<String, Object> params = new HashMap<>();
        when(activityContext.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
        when(activityContext.getParam(eq("extParams"))).thenReturn(new HashMap<>());
        // act
        CompletableFuture<ProductGroupM> result = generalSearchProductHandler.query(activityContext, groupName, params);
        // assert
        assertNotNull(result);
        verify(activityContext).getParam(ShelfActivityConstants.Params.platform);
        verify(activityContext).getParam(eq("extParams"));
    }
}
