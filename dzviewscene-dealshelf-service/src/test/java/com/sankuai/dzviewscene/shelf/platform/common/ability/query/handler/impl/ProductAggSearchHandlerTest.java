package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.dianping.general.unified.search.api.generalsearchv2.dto.ProductSearchIdDto;
import com.dianping.general.unified.search.api.sku.ProductSearchResponse;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductAggSearchHandlerTest {

    @InjectMocks
    private ProductAggSearchHandler productAggSearchHandler;

    @Mock
    private CompositeAtomService compositeAtomService;

    private ActivityContext activityContext;

    private String groupName;

    private Map<String, Object> params;

    @Before
    public void setUp() {
        activityContext = mock(ActivityContext.class);
        groupName = "testGroup";
        params = new HashMap<>();
    }

    /**
     * Tests normal case.
     */
    @Test
    public void testQueryNormal() throws Throwable {
        // Arrange
        ProductSearchResponse<ProductSearchIdDto> mockResponse = mock(ProductSearchResponse.class);
        List<ProductSearchIdDto> resultList = new ArrayList<>();
        ProductSearchIdDto productSearchIdDto = new ProductSearchIdDto();
        productSearchIdDto.setProductId(1L);
        resultList.add(productSearchIdDto);
        when(mockResponse.getResult()).thenReturn(resultList);
        when(mockResponse.getTotalCount()).thenReturn(10);
        when(compositeAtomService.searchProductServiceV2(any())).thenReturn(CompletableFuture.completedFuture(mockResponse));
        // Act
        CompletableFuture<ProductGroupM> result = productAggSearchHandler.query(activityContext, groupName, params);
        // Assert
        assertNotNull(result);
        ProductGroupM productGroupM = result.get();
        assertNotNull(productGroupM);
        assertEquals(10, productGroupM.getTotal());
    }

    /**
     * Tests exception case.
     */
    @Test(expected = Exception.class)
    public void testQueryException() throws Throwable {
        // Arrange
        when(compositeAtomService.searchProductServiceV2(any())).thenThrow(new Exception());
        // Act
        productAggSearchHandler.query(activityContext, groupName, params);
    }
}
