package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OceanBuilderExtAdapter_ProductItemLabsTest {

    /**
     * 测试 productItemLabs 方法是否总是返回 null
     */
    @Test
    public void testProductItemLabsReturnNull() throws Throwable {
        // arrange
        OceanBuilderExtAdapter oceanBuilderExtAdapter = new OceanBuilderExtAdapter() {

            @Override
            public String productItemLabs(ActivityContext activityContext) {
                return null;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        // act
        String result = oceanBuilderExtAdapter.productItemLabs(activityContext);
        // assert
        assertNull(result);
    }

    /**
     * 测试 productItemLabs 方法在 activityContext 为 null 时是否返回 null
     */
    @Test
    public void testProductItemLabsWithNullActivityContext() throws Throwable {
        // arrange
        OceanBuilderExtAdapter oceanBuilderExtAdapter = new OceanBuilderExtAdapter() {

            @Override
            public String productItemLabs(ActivityContext activityContext) {
                return null;
            }
        };
        // act
        String result = oceanBuilderExtAdapter.productItemLabs(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 productItemLabs 方法在 activityContext 不为 null 时是否返回 null
     */
    @Test
    public void testProductItemLabsWithNonNullActivityContext() throws Throwable {
        // arrange
        OceanBuilderExtAdapter oceanBuilderExtAdapter = new OceanBuilderExtAdapter() {

            @Override
            public String productItemLabs(ActivityContext activityContext) {
                return null;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        // act
        String result = oceanBuilderExtAdapter.productItemLabs(activityContext);
        // assert
        assertNull(result);
    }
}
