package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/9/11.
 */
@Ignore("没有可执行的方法")
@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {SpringScope.class})
public class MultiplexQueryFilterFetcherTest {

    @MockBean
    private ComponentFinder componentFinder;
    @MockBean
    private AtomFacadeService atomFacadeService;
    @MockBean
    private CompositeAtomService compositeAtomService;
    @Resource
    private MultiplexQueryFilterFetcher multiplexQueryFilterFetcher;

    //@Test
    @DisplayName("测试通过分组的方式构造筛选数据")
    public void test_by_group() throws Exception {
        Mockito.when(componentFinder.findExtPoint(Mockito.any(), Mockito.anyString())).thenReturn(new MultiplexGroupFilterFetcherExtMock());
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.cosmetic.name()));

        activityContext.addParam(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.cosmetic.name(), new HashMap<String, Object>() {{
                put(PaddingFetcher.Params.planId, "10100008");
                put(PaddingFetcher.Params.statisticSaleBizType, 12);
            }});
        }});
        activityContext.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(mockProductGroups()));
        CompletableFuture<Map<String, FilterM>> filterCompletableFuture = multiplexQueryFilterFetcher.build(activityContext);
        Assert.assertThat(filterCompletableFuture.join(), Matchers.notNullValue());// 过滤数据不为空
        Assert.assertThat(filterCompletableFuture.join().get(ProductEnums.cosmetic.name()).getFilters(), Matchers.hasSize(2));// 没有商品的filter被过滤
        Assert.assertThat(activityContext.getAttachment(FloorsBuilder.Attachments.groupedProducts).join(), Matchers.notNullValue()); // 分组数据存在上下文中
    }


    //@Test
    @DisplayName("测试通过多路召回的方式构造筛选数据")
    public void test_by_multiplex_query() throws Exception {
        Mockito.when(componentFinder.findExtPoint(Mockito.any(), Mockito.anyString())).thenReturn(new MultiplexFilterFetcherExtMock());
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.cosmetic.name()));

        activityContext.addParam(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.cosmetic.name(), new HashMap<String, Object>() {{
                put(PaddingFetcher.Params.planId, "10100008");
                put(PaddingFetcher.Params.statisticSaleBizType, 12);
            }});
        }});
        activityContext.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(mockProductGroups()));
        CompletableFuture<Map<String, FilterM>> filterCompletableFuture = multiplexQueryFilterFetcher.build(activityContext);
        Assert.assertThat(filterCompletableFuture.join(), Matchers.notNullValue());// 过滤数据不为空
        Assert.assertThat(filterCompletableFuture.join().get(ProductEnums.cosmetic.name()).getFilters(), Matchers.hasSize(2));// 没有商品的filter被过滤
        Assert.assertThat(activityContext.getAttachment(FloorsBuilder.Attachments.groupedProducts).join(), Matchers.notNullValue()); // 分组数据存在上下文中
    }



    /////////////////////////////////////////////////////测试数据///////////////////////////////////////////////////////////

    private Map<String, ProductGroupM> mockProductGroups() {
        Map<String, ProductGroupM> productGroupMMap = new HashMap<>();
        productGroupMMap.put(ProductEnums.cosmetic.name(), mockProductGroupM());
        return productGroupMMap;
    }

    private ProductGroupM mockProductGroupM() {
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(new ArrayList<ProductM>(){{
            add(mockProduct(1, "第1个商品"));
            add(mockProduct(2, "第2个商品"));
            add(mockProduct(3, "第3个商品"));
            add(mockProduct(4, "第4个商品"));
            add(mockProduct(5, "第5个商品"));
            add(mockProduct(6, "第6个商品"));
        }});
        return productGroupM;
    }

    private ProductM mockProduct(int id, String title) {
        ProductM productM = new ProductM();
        productM.setProductId(id);
        productM.setTitle(title);
        return productM;
    }
}
