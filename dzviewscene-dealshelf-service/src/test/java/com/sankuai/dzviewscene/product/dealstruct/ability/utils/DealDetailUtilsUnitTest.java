package com.sankuai.dzviewscene.product.dealstruct.ability.utils;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import org.junit.Ignore;
import org.junit.Test;
import org.testng.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/2 10:23 上午
 */
@Ignore("没有可执行的方法")
public class DealDetailUtilsUnitTest {

    //@Test
    public void test_getSkuAttrChnNameBySkuAttrName_given_skuAttrs_and_skuAttrName_should_return_skuAttrChnName() {
        List<SkuAttrItemDto> skuAttrItemDtos = buildSkuAttrItemDtos();
        String skuAttrChnName = DealDetailUtils.getSkuAttrChnNameBySkuAttrName(skuAttrItemDtos, "testAttrName");
        Assert.assertNotNull(skuAttrChnName);
        Assert.assertTrue(Objects.equals(skuAttrChnName, "sku属性中文名测试"));
    }

    //@Test
    public void test_getSkuAttrChnNameBySkuAttrName_given_empty_skuAttrs_and_skuAttrName_should_return_skuAttrChnName() {
        List<SkuAttrItemDto> skuAttrItemDtos = new ArrayList<>();
        String skuAttrChnName = DealDetailUtils.getSkuAttrChnNameBySkuAttrName(skuAttrItemDtos, "testAttrName");
        Assert.assertNull(skuAttrChnName);
    }

    //@Test
    public void test_getSkuAttrChnNameBySkuAttrName_given_null_skuAttrs_and_skuAttrName_should_return_skuAttrChnName() {
        String skuAttrChnName = DealDetailUtils.getSkuAttrChnNameBySkuAttrName(null, "testAttrName");
        Assert.assertNull(skuAttrChnName);
    }

    //@Test
    public void test_getSkuAttrChnNameBySkuAttrName_given_skuAttrs_and_null_skuAttrName_should_return_skuAttrChnName() {
        List<SkuAttrItemDto> skuAttrItemDtos = buildSkuAttrItemDtos();
        String skuAttrChnName = DealDetailUtils.getSkuAttrChnNameBySkuAttrName(skuAttrItemDtos, null);
        Assert.assertNull(skuAttrChnName);
    }

    //@Test
    public void test_getFirstSkuAttrList_given_dealDetailDtoModel_should_return_skuAttrList() {
        DealDetailDtoModel dealDetailDtoModel = buildDealDetailDtoModel();
        List<SkuAttrItemDto> skuAttrItemDtos = DealDetailUtils.getFirstMustGroupFirstSkuAttrList(dealDetailDtoModel);
        Assert.assertNotNull(skuAttrItemDtos);
        Assert.assertTrue(skuAttrItemDtos.size() == 1);
    }

    //@Test
    public void test_getFirstSkuAttrList_given_null_should_return_empty_attr_list() {
        List<SkuAttrItemDto> skuAttrItemDtos = DealDetailUtils.getFirstMustGroupFirstSkuAttrList(null);
        Assert.assertNotNull(skuAttrItemDtos);
        Assert.assertTrue(skuAttrItemDtos.size() == 0);
    }

    //@Test
    public void test_extractFirstMustSkuAttrListFromDealDetailInfoModel_given_DealDetailInfoModel_should_return_firstSkuAttr() {
        List<SkuAttrItemDto> skuAttrItemDtos = DealDetailUtils.extractFirstMustSkuAttrListFromDealDetailInfoModel(buildDealDetailInfoModel());
        Assert.assertNotNull(skuAttrItemDtos);
        Assert.assertTrue(skuAttrItemDtos.size() == 1);
    }

    //@Test
    public void test_extractFirstMustSkuAttrListFromDealDetailInfoModel_given_null_should_return_emptyAttrList() {
        List<SkuAttrItemDto> skuAttrItemDtos = DealDetailUtils.extractFirstMustSkuAttrListFromDealDetailInfoModel(null);
        Assert.assertNotNull(skuAttrItemDtos);
        Assert.assertTrue(skuAttrItemDtos.size() == 0);
    }

    //@Test
    public void test_extractFirstMustSkuFromDealDetailInfoModel_given_DealDetailInfoModel_should_return_firstSku() {
        SkuItemDto skuItemDto = DealDetailUtils.extractFirstMustSkuFromDealDetailInfoModel(buildDealDetailInfoModel());
        Assert.assertNotNull(skuItemDto);
    }

    //@Test
    public void test_extractFirstMustSkuFromDealDetailInfoModel_given_null_should_return_null() {
        SkuItemDto skuItemDto = DealDetailUtils.extractFirstMustSkuFromDealDetailInfoModel(null);
        Assert.assertNull(skuItemDto);
    }

    private DealDetailInfoModel buildDealDetailInfoModel() {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("skuAttrNameTest");
        skuAttrItemDto.setAttrValue("skuAttrValueTest");
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Lists.newArrayList(skuAttrItemDto));
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        mustSkuItemsGroupDto.setSkuItems(Lists.newArrayList(skuItemDto));
        DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        dealDetailSkuUniStructuredDto.setMustGroups(Lists.newArrayList(mustSkuItemsGroupDto));
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        dealDetailDtoModel.setSkuUniStructuredDto(dealDetailSkuUniStructuredDto);
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setDealDetailDtoModel(dealDetailDtoModel);
        return dealDetailInfoModel;
    }

    private List<SkuAttrItemDto> buildSkuAttrItemDtos() {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("testAttrName");
        skuAttrItemDto.setAttrValue("testAttrValue");
        skuAttrItemDto.setChnName("sku属性中文名测试");
        return Lists.newArrayList(skuAttrItemDto);
    }

    private DealDetailDtoModel buildDealDetailDtoModel() {
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        MustSkuItemsGroupDto mustSkuItemsGroupDto = new MustSkuItemsGroupDto();
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrValue("testAttrName");
        skuAttrItemDto.setAttrValue("testAttrValue");
        skuItemDto.setAttrItems(Lists.newArrayList(skuAttrItemDto));
        mustSkuItemsGroupDto.setSkuItems(Lists.newArrayList(skuItemDto));
        dealDetailSkuUniStructuredDto.setMustGroups(Lists.newArrayList(mustSkuItemsGroupDto));
        dealDetailDtoModel.setSkuUniStructuredDto(dealDetailSkuUniStructuredDto);
        return dealDetailDtoModel;
    }
}
