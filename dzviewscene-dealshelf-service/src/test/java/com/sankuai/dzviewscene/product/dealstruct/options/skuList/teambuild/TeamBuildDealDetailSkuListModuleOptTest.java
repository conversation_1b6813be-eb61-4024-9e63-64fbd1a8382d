package com.sankuai.dzviewscene.product.dealstruct.options.skuList.teambuild;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2024/5/29 11:20 AM
 */
import static org.mockito.Mockito.*;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TeamBuildDealDetailSkuListModuleOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private TeamBuildDealDetailSkuListModuleOpt.Param mockParam;
    @Mock
    private TeamBuildDealDetailSkuListModuleOpt.Config mockConfig;
    private DealDetailInfoModel dealDetailInfoModel;

    private TeamBuildDealDetailSkuListModuleOpt teamBuildDealDetailSkuListModuleOpt;


    @Before
    public void setUp() {
        String str = "{\"dealId\":**********,\"dealDetailDtoModel\":{\"dealGroupId\":**********,\"title\":\"\\u56e2\\u8d2d\\u8be6\\u60c5\",\"skuUniStructuredDto\":{\"salePrice\":\"2000.0\",\"marketPrice\":\"2500.0\",\"mustGroups\":[],\"optionalGroups\":[{\"optionalCount\":3,\"skuItems\":[{\"skuId\":0,\"productCategory\":0,\"name\":null,\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[]},{\"skuId\":0,\"productCategory\":0,\"name\":null,\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[]},{\"skuId\":0,\"productCategory\":0,\"name\":null,\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[]},{\"skuId\":0,\"productCategory\":0,\"name\":null,\"copies\":1,\"marketPrice\":null,\"status\":10,\"addTime\":null,\"updateTime\":null,\"attrItems\":[]}]}]},\"structType\":\"uniform-structure-table\"},\"productCategories\":null,\"desc\":null,\"salePrice\":\"2000\",\"marketPrice\":\"2500\",\"dealTitle\":\"\\u3010\\u6ef4\\u6c34\\u6e561\\u65e5\\u56e2\\u5efa\\u3011\\u76ae\\u5212\\u8247&\\u9a91\\u884c\\u7545\\u5feb\\u4e00\\u65e5\\u884c\",\"dealAttrs\":[{\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":**********,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":**********,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\\\\\u56e2\\\\\\\\u8d2d\\\\\\\\u8be6\\\\\\\\u60c5\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"2000.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"2500.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[],\\\\\\\"optionalGroups\\\\\\\":[{\\\\\\\"optionalCount\\\\\\\":3,\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":0,\\\\\\\"name\\\\\\\":null,\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[]},{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":0,\\\\\\\"name\\\\\\\":null,\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[]},{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":0,\\\\\\\"name\\\\\\\":null,\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[]},{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":0,\\\\\\\"name\\\\\\\":null,\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[]}]}]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":**********,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"2000.0\\\",\\\"marketPrice\\\":\\\"2500.0\\\",\\\"mustGroups\\\":[],\\\"optionalGroups\\\":[{\\\"optionalCount\\\":3,\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":0,\\\"name\\\":null,\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[]},{\\\"skuId\\\":0,\\\"productCategory\\\":0,\\\"name\\\":null,\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[]},{\\\"skuId\\\":0,\\\"productCategory\\\":0,\\\"name\\\":null,\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[]},{\\\"skuId\\\":0,\\\"productCategory\\\":0,\\\"name\\\":null,\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[]}]}]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":**********,\\\"title\\\":\\\"\\\\u56e2\\\\u8d2d\\\\u8be6\\\\u60c5\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"2000.0\\\",\\\"marketPrice\\\":\\\"2500.0\\\",\\\"mustGroups\\\":null,\\\"optionalGroups\\\":[{\\\"optionalCount\\\":3,\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":0,\\\"name\\\":null,\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":null},{\\\"skuId\\\":0,\\\"productCategory\\\":0,\\\"name\\\":null,\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":null},{\\\"skuId\\\":0,\\\"productCategory\\\":0,\\\"name\\\":null,\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":null},{\\\"skuId\\\":0,\\\"productCategory\\\":0,\\\"name\\\":null,\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":null}]}]}},\\\"dealResourceInfoList\\\":null}\"},{\"name\":\"standardDealStructContent\",\"value\":\"{\\\"mustGroups\\\":[],\\\"optionalGroups\\\":[{\\\"serviceProjectItems\\\":[{\\\"serviceProjectName\\\":null,\\\"standardAttribute\\\":{\\\"attrs\\\":[{\\\"attrName\\\":\\\"duration\\\",\\\"attrCnName\\\":\\\"\\u670d\\u52a1\\u65f6\\u957f\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"4\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"project\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u5206\\u7c7b\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"\\u559d\\u9152\\u6ce1\\u5427\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"project_name\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u540d\\u79f0\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"\\u56f4\\u7089\\u716e\\u9152\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"sys_originalPrice\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u4ef7\\u683c\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"100\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"drinkBarCheck\\\",\\\"attrCnName\\\":\\\"\\u559d\\u9152\\u6ce1\\u5427\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"[\\\\\\\"\\u56f4\\u7089\\u716e\\u9152\\\\\\\"]\\\"],\\\"complexValues\\\":null}]}],\\\"cpvObjectId\\\":20306618,\\\"cpvObjectVersion\\\":1}},{\\\"serviceProjectName\\\":null,\\\"standardAttribute\\\":{\\\"attrs\\\":[{\\\"attrName\\\":\\\"duration\\\",\\\"attrCnName\\\":\\\"\\u670d\\u52a1\\u65f6\\u957f\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"4\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"project\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u5206\\u7c7b\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"\\u6309\\u6469/\\u8db3\\u7597\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"project_name\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u540d\\u79f0\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"\\u5f71\\u9662\\u8db3\\u9053\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"sys_originalPrice\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u4ef7\\u683c\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"100\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"massageFootTherapyCheck\\\",\\\"attrCnName\\\":\\\"\\u6309\\u6469/\\u8db3\\u7597\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"[\\\\\\\"\\u6309\\u6469\\u7545\\u5403\\\\\\\",\\\\\\\"\\u5f71\\u9662\\u8db3\\u9053\\\\\\\"]\\\"],\\\"complexValues\\\":null}]}],\\\"cpvObjectId\\\":20306618,\\\"cpvObjectVersion\\\":1}},{\\\"serviceProjectName\\\":null,\\\"standardAttribute\\\":{\\\"attrs\\\":[{\\\"attrName\\\":\\\"duration\\\",\\\"attrCnName\\\":\\\"\\u670d\\u52a1\\u65f6\\u957f\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"4\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"project\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u5206\\u7c7b\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"\\u91c7\\u6458/\\u519c\\u5bb6\\u4e50\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"pickingAgritourismCheck\\\",\\\"attrCnName\\\":\\\"\\u91c7\\u6458/\\u519c\\u5bb6\\u4e50\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"[\\\\\\\"\\u52a8\\u7269\\u6295\\u5582\\\\\\\",\\\\\\\"\\u4f11\\u95f2\\u5782\\u9493\\\\\\\",\\\\\\\"\\u81ea\\u52a9\\u70e7\\u70e4\\\\\\\"]\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"project_name\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u540d\\u79f0\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"\\u5782\\u9493\\u70e7\\u70e4\\u91c7\\u6458\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"sys_originalPrice\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u4ef7\\u683c\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"100\\\"],\\\"complexValues\\\":null}]}],\\\"cpvObjectId\\\":20306618,\\\"cpvObjectVersion\\\":1}},{\\\"serviceProjectName\\\":null,\\\"standardAttribute\\\":{\\\"attrs\\\":[{\\\"attrName\\\":\\\"duration\\\",\\\"attrCnName\\\":\\\"\\u670d\\u52a1\\u65f6\\u957f\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"4\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"karaokeCheck\\\",\\\"attrCnName\\\":\\\"K\\u6b4c\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"[\\\\\\\"\\u5403\\u996d\\u5531K\\\\\\\",\\\\\\\"\\u706b\\u9505KTV\\\\\\\",\\\\\\\"K\\u6b4c\\\\\\\"]\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"project\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u5206\\u7c7b\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"K\\u6b4c\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"project_name\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u540d\\u79f0\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"K\\u6b4c\\u7545\\u73a9\\\"],\\\"complexValues\\\":null}]},{\\\"attrName\\\":\\\"sys_originalPrice\\\",\\\"attrCnName\\\":\\\"\\u9879\\u76ee\\u4ef7\\u683c\\\",\\\"attrValues\\\":[{\\\"type\\\":0,\\\"simpleValues\\\":[\\\"100\\\"],\\\"complexValues\\\":null}]}],\\\"cpvObjectId\\\":20306618,\\\"cpvObjectVersion\\\":1}}],\\\"optionalCount\\\":3}]}\"},{\"name\":\"detailInfo\",\"value\":\"{\\\"version\\\":1,\\\"headline\\\":\\\"\\u56e2\\u8d2d\\u8be6\\u60c5\\\",\\\"content\\\":[{\\\"type\\\":\\\"uniform-structure-table\\\",\\\"data\\\":{\\\"totalPrice\\\":2500.0,\\\"salePrice\\\":2000.0,\\\"groups\\\":[{\\\"optionalCount\\\":3,\\\"units\\\":[{\\\"amount\\\":1,\\\"attrValues\\\":{\\\"duration\\\":\\\"4\\\",\\\"project\\\":\\\"\\u559d\\u9152\\u6ce1\\u5427\\\",\\\"project_name\\\":\\\"\\u56f4\\u7089\\u716e\\u9152\\\",\\\"sys_originalPrice\\\":\\\"100\\\",\\\"drinkBarCheck\\\":\\\"\\u56f4\\u7089\\u716e\\u9152\\\"}},{\\\"amount\\\":1,\\\"attrValues\\\":{\\\"duration\\\":\\\"4\\\",\\\"project\\\":\\\"\\u6309\\u6469/\\u8db3\\u7597\\\",\\\"project_name\\\":\\\"\\u5f71\\u9662\\u8db3\\u9053\\\",\\\"massageFootTherapyCheck\\\":\\\"\\u6309\\u6469\\u7545\\u5403\\u3001\\u5f71\\u9662\\u8db3\\u9053\\\",\\\"sys_originalPrice\\\":\\\"100\\\"}},{\\\"amount\\\":1,\\\"attrValues\\\":{\\\"duration\\\":\\\"4\\\",\\\"project\\\":\\\"\\u91c7\\u6458/\\u519c\\u5bb6\\u4e50\\\",\\\"pickingAgritourismCheck\\\":\\\"\\u52a8\\u7269\\u6295\\u5582\\u3001\\u4f11\\u95f2\\u5782\\u9493\\u3001\\u81ea\\u52a9\\u70e7\\u70e4\\\",\\\"project_name\\\":\\\"\\u5782\\u9493\\u70e7\\u70e4\\u91c7\\u6458\\\",\\\"sys_originalPrice\\\":\\\"100\\\"}},{\\\"amount\\\":1,\\\"attrValues\\\":{\\\"duration\\\":\\\"4\\\",\\\"karaokeCheck\\\":\\\"\\u5403\\u996d\\u5531K\\u3001\\u706b\\u9505KTV\\u3001K\\u6b4c\\\",\\\"project\\\":\\\"K\\u6b4c\\\",\\\"project_name\\\":\\\"K\\u6b4c\\u7545\\u73a9\\\",\\\"sys_originalPrice\\\":\\\"100\\\"}}]}]}},{\\\"type\\\":\\\"serviceItem-structure-table\\\",\\\"data\\\":{\\\"groups\\\":[{\\\"units\\\":[{\\\"serviceItemValue\\\":{\\\"objectVersion\\\":1,\\\"objectValues\\\":{\\\"duration\\\":4,\\\"project\\\":\\\"\\u559d\\u9152\\u6ce1\\u5427\\\",\\\"project_name\\\":\\\"\\u56f4\\u7089\\u716e\\u9152\\\",\\\"sys_originalPrice\\\":\\\"100\\\",\\\"drinkBarCheck\\\":[\\\"\\u56f4\\u7089\\u716e\\u9152\\\"]},\\\"objectId\\\":20306618}},{\\\"serviceItemValue\\\":{\\\"objectVersion\\\":1,\\\"objectValues\\\":{\\\"duration\\\":4,\\\"project\\\":\\\"\\u6309\\u6469/\\u8db3\\u7597\\\",\\\"project_name\\\":\\\"\\u5f71\\u9662\\u8db3\\u9053\\\",\\\"sys_originalPrice\\\":\\\"100\\\",\\\"massageFootTherapyCheck\\\":[\\\"\\u6309\\u6469\\u7545\\u5403\\\",\\\"\\u5f71\\u9662\\u8db3\\u9053\\\"]},\\\"objectId\\\":20306618}},{\\\"serviceItemValue\\\":{\\\"objectVersion\\\":1,\\\"objectValues\\\":{\\\"duration\\\":4,\\\"project\\\":\\\"\\u91c7\\u6458/\\u519c\\u5bb6\\u4e50\\\",\\\"pickingAgritourismCheck\\\":[\\\"\\u52a8\\u7269\\u6295\\u5582\\\",\\\"\\u4f11\\u95f2\\u5782\\u9493\\\",\\\"\\u81ea\\u52a9\\u70e7\\u70e4\\\"],\\\"project_name\\\":\\\"\\u5782\\u9493\\u70e7\\u70e4\\u91c7\\u6458\\\",\\\"sys_originalPrice\\\":\\\"100\\\"},\\\"objectId\\\":20306618}},{\\\"serviceItemValue\\\":{\\\"objectVersion\\\":1,\\\"objectValues\\\":{\\\"duration\\\":4,\\\"karaokeCheck\\\":[\\\"\\u5403\\u996d\\u5531K\\\",\\\"\\u706b\\u9505KTV\\\",\\\"K\\u6b4c\\\"],\\\"project\\\":\\\"K\\u6b4c\\\",\\\"project_name\\\":\\\"K\\u6b4c\\u7545\\u73a9\\\",\\\"sys_originalPrice\\\":\\\"100\\\"},\\\"objectId\\\":20306618}}],\\\"optionalCount\\\":3}]}}]}\"},{\"name\":\"service_type\",\"value\":\"\\u56e2\\u5efa/\\u805a\\u4f1a\"},{\"name\":\"convenienceServiceCheck\",\"value\":\"[\\\"\\u63d0\\u4f9b\\u63a5\\u9001\\\",\\\"\\u516c\\u5171\\u505c\\u8f66\\u573a\\\"]\"},{\"name\":\"serviceStaffCheck\",\"value\":\"[\\\"\\u8c03\\u9152\\u5e08\\\",\\\"\\u8868\\u6f14\\u4eba\\u5458\\\"]\"},{\"name\":\"photographyVideographyCheck\",\"value\":\"[\\\"\\u6444\\u5f71\\u6444\\u50cf\\\"]\"},{\"name\":\"serviceCategoryCheck\",\"value\":\"[\\\"\\u6444\\u5f71\\u6444\\u50cf\\\",\\\"\\u670d\\u52a1\\u4eba\\u5458\\\",\\\"\\u4fbf\\u5229\\u670d\\u52a1\\\"]\"}],\"dealModuleAttrs\":null,\"standardServiceProjectDTO\":{\"mustGroups\":[],\"optionalGroups\":[{\"serviceProjectItems\":[{\"serviceProjectName\":null,\"standardAttribute\":{\"attrs\":[{\"attrName\":\"duration\",\"attrCnName\":\"\\u670d\\u52a1\\u65f6\\u957f\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"4\"],\"complexValues\":null}]},{\"attrName\":\"project\",\"attrCnName\":\"\\u9879\\u76ee\\u5206\\u7c7b\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"\\u559d\\u9152\\u6ce1\\u5427\"],\"complexValues\":null}]},{\"attrName\":\"project_name\",\"attrCnName\":\"\\u9879\\u76ee\\u540d\\u79f0\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"\\u56f4\\u7089\\u716e\\u9152\"],\"complexValues\":null}]},{\"attrName\":\"sys_originalPrice\",\"attrCnName\":\"\\u9879\\u76ee\\u4ef7\\u683c\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"100\"],\"complexValues\":null}]},{\"attrName\":\"drinkBarCheck\",\"attrCnName\":\"\\u559d\\u9152\\u6ce1\\u5427\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"[\\\"\\u56f4\\u7089\\u716e\\u9152\\\"]\"],\"complexValues\":null}]}],\"cpvObjectId\":20306618,\"cpvObjectVersion\":1}},{\"serviceProjectName\":null,\"standardAttribute\":{\"attrs\":[{\"attrName\":\"duration\",\"attrCnName\":\"\\u670d\\u52a1\\u65f6\\u957f\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"4\"],\"complexValues\":null}]},{\"attrName\":\"project\",\"attrCnName\":\"\\u9879\\u76ee\\u5206\\u7c7b\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"\\u6309\\u6469/\\u8db3\\u7597\"],\"complexValues\":null}]},{\"attrName\":\"project_name\",\"attrCnName\":\"\\u9879\\u76ee\\u540d\\u79f0\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"\\u5f71\\u9662\\u8db3\\u9053\"],\"complexValues\":null}]},{\"attrName\":\"sys_originalPrice\",\"attrCnName\":\"\\u9879\\u76ee\\u4ef7\\u683c\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"100\"],\"complexValues\":null}]},{\"attrName\":\"massageFootTherapyCheck\",\"attrCnName\":\"\\u6309\\u6469/\\u8db3\\u7597\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"[\\\"\\u6309\\u6469\\u7545\\u5403\\\",\\\"\\u5f71\\u9662\\u8db3\\u9053\\\"]\"],\"complexValues\":null}]}],\"cpvObjectId\":20306618,\"cpvObjectVersion\":1}},{\"serviceProjectName\":null,\"standardAttribute\":{\"attrs\":[{\"attrName\":\"duration\",\"attrCnName\":\"\\u670d\\u52a1\\u65f6\\u957f\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"4\"],\"complexValues\":null}]},{\"attrName\":\"project\",\"attrCnName\":\"\\u9879\\u76ee\\u5206\\u7c7b\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"\\u91c7\\u6458/\\u519c\\u5bb6\\u4e50\"],\"complexValues\":null}]},{\"attrName\":\"pickingAgritourismCheck\",\"attrCnName\":\"\\u91c7\\u6458/\\u519c\\u5bb6\\u4e50\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"[\\\"\\u52a8\\u7269\\u6295\\u5582\\\",\\\"\\u4f11\\u95f2\\u5782\\u9493\\\",\\\"\\u81ea\\u52a9\\u70e7\\u70e4\\\"]\"],\"complexValues\":null}]},{\"attrName\":\"project_name\",\"attrCnName\":\"\\u9879\\u76ee\\u540d\\u79f0\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"\\u5782\\u9493\\u70e7\\u70e4\\u91c7\\u6458\"],\"complexValues\":null}]},{\"attrName\":\"sys_originalPrice\",\"attrCnName\":\"\\u9879\\u76ee\\u4ef7\\u683c\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"100\"],\"complexValues\":null}]}],\"cpvObjectId\":20306618,\"cpvObjectVersion\":1}},{\"serviceProjectName\":null,\"standardAttribute\":{\"attrs\":[{\"attrName\":\"duration\",\"attrCnName\":\"\\u670d\\u52a1\\u65f6\\u957f\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"4\"],\"complexValues\":null}]},{\"attrName\":\"karaokeCheck\",\"attrCnName\":\"K\\u6b4c\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"[\\\"\\u5403\\u996d\\u5531K\\\",\\\"\\u706b\\u9505KTV\\\",\\\"K\\u6b4c\\\"]\"],\"complexValues\":null}]},{\"attrName\":\"project\",\"attrCnName\":\"\\u9879\\u76ee\\u5206\\u7c7b\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"K\\u6b4c\"],\"complexValues\":null}]},{\"attrName\":\"project_name\",\"attrCnName\":\"\\u9879\\u76ee\\u540d\\u79f0\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"K\\u6b4c\\u7545\\u73a9\"],\"complexValues\":null}]},{\"attrName\":\"sys_originalPrice\",\"attrCnName\":\"\\u9879\\u76ee\\u4ef7\\u683c\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"100\"],\"complexValues\":null}]}],\"cpvObjectId\":20306618,\"cpvObjectVersion\":1}}],\"optionalCount\":3}]},\"tradeType\":3,\"unifyProduct\":false}";
        teamBuildDealDetailSkuListModuleOpt = new TeamBuildDealDetailSkuListModuleOpt();
        dealDetailInfoModel = JsonCodec.decode(str, DealDetailInfoModel.class);
    }

    @Test
    public void testCompute() {

        // 根据实际情况调整mock返回值
        when(mockParam.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);

        // 执行测试方法
        List<DealDetailSkuListModuleGroupModel> result = teamBuildDealDetailSkuListModuleOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // 验证结果
        assertNotNull(result);
    }

}
