package com.sankuai.dzviewscene.product.shelf.options.builder.filtertree.multipleselect;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filtertree.vp.FilterNodeMultipleSelectVP.Param;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class FilterNodeDefaultMultipleSelectOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    private FilterNodeDefaultMultipleSelectOpt filterNodeDefaultMultipleSelectOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        filterNodeDefaultMultipleSelectOpt = new FilterNodeDefaultMultipleSelectOpt();
    }

    /**
     * 测试 compute 方法，当 Param 的 multipleSelect 为 true 时
     */
    @Test
    public void testComputeWhenMultipleSelectIsTrue() {
        // arrange
        Param param = Param.builder().multipleSelect(true).build();

        // act
        Boolean result = filterNodeDefaultMultipleSelectOpt.compute(mockActivityCxt, param, null);

        // assert
        assertTrue("当 multipleSelect 为 true 时，应返回 true", result);
    }

    /**
     * 测试 compute 方法，当 Param 的 multipleSelect 为 false 时
     */
    @Test
    public void testComputeWhenMultipleSelectIsFalse() {
        // arrange
        Param param = Param.builder().multipleSelect(false).build();

        // act
        Boolean result = filterNodeDefaultMultipleSelectOpt.compute(mockActivityCxt, param, null);

        // assert
        assertFalse("当 multipleSelect 为 false 时，应返回 false", result);
    }
}
