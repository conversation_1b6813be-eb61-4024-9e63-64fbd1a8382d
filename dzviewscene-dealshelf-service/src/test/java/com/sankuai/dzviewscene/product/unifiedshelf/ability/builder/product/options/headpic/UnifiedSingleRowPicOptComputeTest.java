package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic.UnifiedSingleRowPicOpt.Config;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedSingleRowPicOptComputeTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private UnifiedSingleRowPicOpt.Param mockParam;

    @Mock
    private ProductM mockProductM;

    private UnifiedSingleRowPicOpt unifiedSingleRowPicOpt;

    @Before
    public void setUp() {
        unifiedSingleRowPicOpt = new UnifiedSingleRowPicOpt();
    }

    @Test
    public void testComputeProductMIsNull() throws Throwable {
        when(mockParam.getProductM()).thenReturn(null);
        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, new Config());
        assertNull(result);
    }

    @Test
    public void testComputePicUrlIsEmpty() throws Throwable {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("");
        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, new Config());
        assertNull(result);
    }

    @Test
    public void testComputeCalcBySpuIsFalse() throws Throwable {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("http://example.com/pic.jpg");
        Config config = new Config();
        config.setCalcBySpu(false);
        PictureModel result = unifiedSingleRowPicOpt.compute(mockActivityCxt, mockParam, config);
        assertNotNull(result);
        assertEquals("https://example.com/pic.jpg", result.getPicUrl());
        assertEquals(1, result.getAspectRadio(), 0);
    }
}
