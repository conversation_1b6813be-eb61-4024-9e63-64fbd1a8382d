package com.sankuai.dzviewscene.product.dealstruct.ability.assemble;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleBuilderFactory;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopupTypeVO;
import com.sankuai.dzviewscene.product.factory.DealCategoryFactory;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailAssembleAbilityBuildTest {

    @InjectMocks
    private DealDetailAssembleAbility dealDetailAssembleAbility;

    @Mock
    private ModuleBuilderFactory moduleBuilderFactory;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailAssembleParam assembleParam;

    @Mock
    private DealDetailAssembleCfg assembleCfg;

    @Mock
    private DealCategoryFactory dealCategoryFactory;

    /**
     * 测试正常情况
     */
    @Test
    public void testBuildNormal() throws Throwable {
        // arrange
        List<DealDetailModuleVO> detailModuleVOList = Collections.singletonList(new DealDetailModuleVO());
        List<PopupTypeVO> popupTypes = Collections.singletonList(new PopupTypeVO());
        when(moduleBuilderFactory.buildModuleList(any(ActivityCxt.class), any(DealDetailAssembleParam.class), any(List.class))).thenReturn(detailModuleVOList);
        when(dealCategoryFactory.getModuleList(any(ActivityCxt.class), any(DealDetailAssembleCfg.class))).thenReturn(Collections.emptyList());
        // act
        CompletableFuture<DealModuleDetailVO> result = dealDetailAssembleAbility.build(activityCxt, assembleParam, assembleCfg);
        // assert
        assertNotNull(result);
        verify(moduleBuilderFactory, times(1)).buildModuleList(any(ActivityCxt.class), any(DealDetailAssembleParam.class), any(List.class));
    }

    /**
     * 测试buildModuleList返回空列表
     */
    @Test
    public void testBuildModuleListEmpty() throws Throwable {
        // arrange
        when(moduleBuilderFactory.buildModuleList(any(ActivityCxt.class), any(DealDetailAssembleParam.class), any(List.class))).thenReturn(Collections.emptyList());
        when(dealCategoryFactory.getModuleList(any(ActivityCxt.class), any(DealDetailAssembleCfg.class))).thenReturn(Collections.emptyList());
        // act
        CompletableFuture<DealModuleDetailVO> result = dealDetailAssembleAbility.build(activityCxt, assembleParam, assembleCfg);
        // assert
        assertNotNull(result);
        verify(moduleBuilderFactory, times(1)).buildModuleList(any(ActivityCxt.class), any(DealDetailAssembleParam.class), any(List.class));
    }

    /**
     * 测试activityCxt.getSource返回null
     */
    @Test
    public void testBuildSourceNull() throws Throwable {
        // arrange
        when(activityCxt.getSource(any(String.class))).thenReturn(null);
        when(dealCategoryFactory.getModuleList(any(ActivityCxt.class), any(DealDetailAssembleCfg.class))).thenReturn(Collections.emptyList());
        // act
        CompletableFuture<DealModuleDetailVO> result = dealDetailAssembleAbility.build(activityCxt, assembleParam, assembleCfg);
        // assert
        assertNotNull(result);
        verify(moduleBuilderFactory, times(1)).buildModuleList(any(ActivityCxt.class), any(DealDetailAssembleParam.class), any(List.class));
    }

    /**
     * 测试buildDealModuleDetailVO方法抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testBuildDealModuleDetailVOException() throws Throwable {
        // arrange
        when(moduleBuilderFactory.buildModuleList(any(ActivityCxt.class), any(DealDetailAssembleParam.class), any(List.class))).thenThrow(new RuntimeException());
        // act
        dealDetailAssembleAbility.build(activityCxt, assembleParam, assembleCfg);
        // assert
        // Exception is expected
    }
}
