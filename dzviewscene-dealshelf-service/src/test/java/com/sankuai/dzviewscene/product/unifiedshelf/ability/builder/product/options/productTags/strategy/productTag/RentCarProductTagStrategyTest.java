package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.productTag;

import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagBuildReq;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * RentCarProductTagStrategy测试类
 */
public class RentCarProductTagStrategyTest {

    private RentCarProductTagStrategy strategy;
    private ProductTagBuildReq req;
    private ProductM productM;

    @Before
    public void setUp() {
        strategy = new RentCarProductTagStrategy();
        req = new ProductTagBuildReq();
        productM = mock(ProductM.class);
    }

    /**
     * 测试build方法，当ProductM为null时
     */
    @Test
    public void testBuildWhenProductMIsNull() {
        // arrange
        req.setProductM(null);

        // act
        List<String> result = strategy.build(req);

        // assert
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试build方法，当ProductM不为null且所有属性均不为空时
     */
    @Test
    public void testBuildWhenProductMIsNotNullAndAllAttributesAreNotNull() {
        // arrange
        req.setProductM(productM);
        when(productM.getAttr(Mockito.anyString())).thenReturn("value");

        // act
        List<String> result = strategy.build(req);

        // assert
        assertFalse("结果不应为空列表", result.isEmpty());
        assertEquals("结果列表大小应为5", 5, result.size());
    }

    /**
     * 测试build方法，当ProductM不为null且所有属性均为空时
     */
    @Test
    public void testBuildWhenProductMIsNotNullAndAllAttributesAreNull() {
        // arrange
        req.setProductM(productM);
        when(productM.getAttr(Mockito.anyString())).thenReturn("");

        // act
        List<String> result = strategy.build(req);

        // assert
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试build方法，当ProductM不为null且部分属性为空时
     */
    @Test
    public void testBuildWhenProductMIsNotNullAndSomeAttributesAreNull() {
        // arrange
        req.setProductM(productM);
        when(productM.getAttr(Mockito.anyString())).thenReturn("value").thenReturn("");

        // act
        List<String> result = strategy.build(req);

        // assert
        assertFalse("结果不应为空列表", result.isEmpty());
        assertEquals("结果列表大小应小于5", true, result.size() < 5);
    }
}
