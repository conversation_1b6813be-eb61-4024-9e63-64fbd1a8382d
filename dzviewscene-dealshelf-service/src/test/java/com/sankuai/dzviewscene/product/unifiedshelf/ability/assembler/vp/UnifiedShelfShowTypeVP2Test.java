package com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.vp;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.options.title.UnifiedShelfMultiActivityTitleOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterTitleVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试UnifiedShelfMultiActivityTitleOpt的compute方法
 */
public class UnifiedShelfShowTypeVP2Test {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    @Mock
    private UnifiedShelfMultiActivityTitleOpt.Config config;
    @Mock
    private FilterBtnM filterBtnM;
    @Mock
    private ProductActivityM productActivityM;

    private UnifiedShelfMultiActivityTitleOpt target;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        target = new UnifiedShelfMultiActivityTitleOpt();
    }

    /**
     * 测试当图片模型为空时，应返回文本富文本模型
     */
    @Test
    public void testCompute_WithNullPictureModel() {
        // arrange
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        when(filterBtnM.isActivity()).thenReturn(false);

        // act
        IconRichLabelModel result = target.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertEquals(0, result.getType());
    }

    /**
     * 测试当图片模型为空时，应返回文本富文本模型
     */
    @Test
    public void testCompute_WithNullPictureModel2() {
        // arrange
        ProductActivityM productActivityM1 = buildActivity(1, 1);
        productActivityM1.setActivityExtraAttrs(new HashMap<>());
        productActivityM1.getActivityExtraAttrs().put("测试", "test");
        List<ProductActivityM> activities = Lists.newArrayList(productActivityM1, buildActivity(2, 2));
        UnifiedShelfFilterTitleVP.Param param1 = UnifiedShelfFilterTitleVP.Param.builder().build();
        param1.setActivities(activities);

        FilterBtnM filterBtnM1 = new FilterBtnM();
        filterBtnM1.setTitle("测试");
        param1.setFilterBtnM(filterBtnM1);

        IconRichLabelModel result = UnifiedShelfFilterTitleVP.buildTextRichLabModel(param1);

        // assert
        assertNotNull(result);
        assertEquals(0, result.getType());
    }

    public ProductActivityM buildActivity(int scene, int pageId) {
        ProductActivityM productActivityM = new ProductActivityM();
        productActivityM.setActivityScene(scene);
        productActivityM.setPageId(pageId);
        return productActivityM;
    }
}
