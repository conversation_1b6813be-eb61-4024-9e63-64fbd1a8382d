package com.sankuai.dzviewscene.product.ability.collect;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import static org.junit.Assert.*;
import com.dianping.vc.enums.VCPlatformEnum;
import org.junit.*;
import static org.mockito.Mockito.*;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;

@RunWith(MockitoJUnitRunner.class)
public class CollectInfoAbilityTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private CollectInfoQueryParam param;

    @Mock
    private CollectInfoQueryCfg cfg;

    @Mock
    private CompositeAtomService compositeAtomService;

    @InjectMocks
    private CollectInfoAbility ability;

    /**
     * Tests the scenario when cfg is null.
     */
    @Test
    public void testBuildCfgIsNull() throws Throwable {
        CompletableFuture<Boolean> result = ability.build(activityCxt, param, null);
        assertFalse(result.get());
    }

    /**
     * Tests the scenario when platform is not VCPlatformEnum.MT.getType().
     */
    @Test
    public void testBuildPlatformIsNotMT() throws Throwable {
        when(cfg.getDpBizId()).thenReturn("expectedDpBizId");
        when(compositeAtomService.getDpFavorStatus(anyList(), anyInt(), anyLong())).thenReturn(CompletableFuture.completedFuture(Collections.singletonMap("expectedDpBizId", true)));
        CompletableFuture<Boolean> result = ability.build(activityCxt, param, cfg);
        assertTrue(result.get());
    }
}
