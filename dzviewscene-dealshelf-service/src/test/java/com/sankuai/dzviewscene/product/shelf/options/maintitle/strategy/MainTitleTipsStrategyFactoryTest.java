package com.sankuai.dzviewscene.product.shelf.options.maintitle.strategy;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class MainTitleTipsStrategyFactoryTest {

    @Mock
    private ApplicationContext appCtx;

    private MainTitleTipsStrategyFactory mainTitleTipsStrategyFactory;

    @Before
    public void setUp() {
        mainTitleTipsStrategyFactory = new MainTitleTipsStrategyFactory();
        mainTitleTipsStrategyFactory.setApplicationContext(appCtx);
    }

    /**
     * 测试 afterPropertiesSet 方法，当 appCtx.getBeansOfType 返回的 Map 为空时
     */
    @Test
    public void testAfterPropertiesSetWhenBeansOfTypeIsEmpty() throws Exception {
        // arrange
        when(appCtx.getBeansOfType(MainTitleTipsStrategy.class)).thenReturn(new HashMap<>());
        // act
        mainTitleTipsStrategyFactory.afterPropertiesSet();
        // assert
        verify(appCtx, times(1)).getBeansOfType(MainTitleTipsStrategy.class);
    }

    /**
     * 测试 afterPropertiesSet 方法，当 appCtx.getBeansOfType 返回的 Map 不为空时
     */
    @Test
    public void testAfterPropertiesSetWhenBeansOfTypeIsNotEmpty() throws Exception {
        // arrange
        Map<String, MainTitleTipsStrategy> beanMap = new HashMap<>();
        MainTitleTipsStrategy strategy = mock(MainTitleTipsStrategy.class);
        when(strategy.getName()).thenReturn("test");
        beanMap.put("test", strategy);
        when(appCtx.getBeansOfType(MainTitleTipsStrategy.class)).thenReturn(beanMap);
        // act
        mainTitleTipsStrategyFactory.afterPropertiesSet();
        // assert
        verify(appCtx, times(1)).getBeansOfType(MainTitleTipsStrategy.class);
        verify(strategy, times(1)).getName();
    }
}
