package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import org.junit.*;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class ProcessStepAttrVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private ProcessStepAttrVOListOpt.StepConfig config;

    /**
     * Tests the compute method when processStepAttrModules is null.
     */
    @Test
    public void testComputeWhenProcessStepAttrModulesIsNull() throws Throwable {
        ProcessStepAttrVOListOpt processStepAttrVOListOpt = new ProcessStepAttrVOListOpt();
        when(param.getDealAttrs()).thenReturn(null);
        List<DealDetailStructAttrModuleGroupModel> result = processStepAttrVOListOpt.compute(context, param, config);
        assertNull(result);
    }
}
