package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemwarmup;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfButtonVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemActivityVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfWarmUpVO;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemWarmUpVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemShopWarmUpOptTest {

    @InjectMocks
    private UnifiedShelfItemShopWarmUpOpt opt;

    @Mock
    private UnifiedShelfItemWarmUpVP.Param param;

    @Mock
    private UnifiedShelfItemShopWarmUpOpt.Config config;

    @Mock
    private ProductM productM;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private WarmUpStageEnum.WarmUpStageResult warmUpStageResult;

    /**
     * 测试时间小于24小时的场景
     */
    @Test
    public void testCreateShelfWarmUpVO_TimeLessThan24Hours() throws Throwable {
        // arrange
        long currentTime = System.currentTimeMillis();
        // 23小时后
        long time = currentTime + 1000 * 60 * 60 * 23;
        String prefix = "剩余";
        String suffix = "结束";
        int type = 1;
        boolean buttonDisabled = false;
        String buttonUrl = "http://example.com";
        String buttonText = "点击";
        // act
        ShelfWarmUpVO result = (ShelfWarmUpVO) ReflectionTestUtils.invokeMethod(opt, "createShelfWarmUpVO", time, prefix, suffix, type, buttonDisabled, buttonUrl, buttonText);
        // assert
        assertNotNull(result);
        assertEquals(prefix, result.getActivity().getPreText());
        assertEquals(suffix, result.getActivity().getSuffix());
        assertEquals(time, result.getActivity().getActivityEndTime());
        assertEquals(type, result.getButton().getType());
        assertEquals(buttonDisabled, result.getButton().isDisable());
        assertEquals(buttonUrl, result.getButton().getJumpUrl());
        assertEquals(buttonText, result.getButton().getName());
    }

    /**
     * 测试时间大于24小时的场景
     */
    @Test
    public void testCreateShelfWarmUpVO_TimeGreaterThan24Hours() throws Throwable {
        // arrange
        long currentTime = System.currentTimeMillis();
        // 25小时后
        long time = currentTime + 1000 * 60 * 60 * 25;
        String prefix = "剩余";
        String suffix = "结束";
        int type = 1;
        boolean buttonDisabled = false;
        String buttonUrl = "http://example.com";
        String buttonText = "点击";
        // act
        ShelfWarmUpVO result = (ShelfWarmUpVO) ReflectionTestUtils.invokeMethod(opt, "createShelfWarmUpVO", time, prefix, suffix, type, buttonDisabled, buttonUrl, buttonText);
        // assert
        assertNotNull(result);
        long days = ChronoUnit.DAYS.between(LocalDateTime.ofInstant(Instant.ofEpochMilli(currentTime), ZoneId.systemDefault()), LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault()));
        assertEquals(prefix + days + "天", result.getActivity().getPreText());
        assertEquals(0L, result.getActivity().getActivityEndTime());
    }
}
