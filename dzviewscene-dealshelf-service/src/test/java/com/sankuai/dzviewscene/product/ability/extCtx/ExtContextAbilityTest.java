package com.sankuai.dzviewscene.product.ability.extCtx;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility;
import com.sankuai.dzviewscene.product.ability.extCtx.ExtContextCfg;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class ExtContextAbilityTest {

    @Mock
    private ActivityCxt ctx;

    @Test
    public void testBuildWhenCfgIsNull() throws Throwable {
        // arrange
        ExtContextAbility ability = new ExtContextAbility();
        ExtContextCfg cfg = null;
        // act
        CompletableFuture<Void> result = ability.build(ctx, null, cfg);
        // assert
        assertTrue(result.isDone());
        assertNull(result.get());
    }

    @Test
    public void testBuildWhenNeedFieldsIsEmpty() throws Throwable {
        // arrange
        ExtContextAbility ability = new ExtContextAbility();
        ExtContextCfg cfg = new ExtContextCfg();
        cfg.setNeedFields(Collections.emptyList());
        // act
        CompletableFuture<Void> result = ability.build(ctx, null, cfg);
        // assert
        assertTrue(result.isDone());
        assertNull(result.get());
    }

    @Test
    public void testBuildWhenPlatformIsMT() throws Throwable {
        // arrange
        ExtContextAbility ability = new ExtContextAbility();
        ExtContextCfg cfg = new ExtContextCfg();
        cfg.setNeedFields(Collections.singletonList(1));
        when(ctx.getParameters()).thenReturn(Collections.singletonMap("platform", "mt"));
        // act
        CompletableFuture<Void> result = ability.build(ctx, null, cfg);
        // assert
        assertTrue(result.isDone());
        assertNull(result.get());
    }

    @Test
    public void testBuildWhenPlatformIsNotMT() throws Throwable {
        // arrange
        ExtContextAbility ability = new ExtContextAbility();
        ExtContextCfg cfg = new ExtContextCfg();
        cfg.setNeedFields(Collections.singletonList(1));
        when(ctx.getParameters()).thenReturn(Collections.singletonMap("platform", "dp"));
        // act
        CompletableFuture<Void> result = ability.build(ctx, null, cfg);
        // assert
        assertTrue(result.isDone());
        assertNull(result.get());
    }
}
