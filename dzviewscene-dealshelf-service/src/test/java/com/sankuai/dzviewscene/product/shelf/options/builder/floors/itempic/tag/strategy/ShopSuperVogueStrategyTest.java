package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopSuperVogueStrategyTest {

    private ShopSuperVogueStrategy shopSuperVogueStrategy = new ShopSuperVogueStrategy();

    @Test
    public void testBuildTagSingleBigPicShelf() throws Throwable {
        FloatTagBuildReq param = new FloatTagBuildReq();
        param.setShelfShowType(ShelfShowTypeEnum.SINGLE_BIG_PIC_SHELF.getType());
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        FloatTagVO result = shopSuperVogueStrategy.buildTag(param, config);
        assertEquals(2.875, result.getIcon().getAspectRadio(), 0.001);
    }

    @Test
    public void testBuildTagPicHeightExists() throws Throwable {
        FloatTagBuildReq param = new FloatTagBuildReq();
        param.setShelfShowType(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType());
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        FloatTagVO result = shopSuperVogueStrategy.buildTag(param, config);
        assertEquals(16, result.getIcon().getPicHeight());
    }

    @Test
    public void testBuildTagPicHeightNotExists() throws Throwable {
        FloatTagBuildReq param = new FloatTagBuildReq();
        // Using a non-existing shelf show type to simulate the scenario
        param.setShelfShowType(999);
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        FloatTagVO result = shopSuperVogueStrategy.buildTag(param, config);
        // Since picHeight is an int and cannot be null, we check for the default value instead
        assertEquals(0, result.getIcon().getPicHeight());
    }
}
