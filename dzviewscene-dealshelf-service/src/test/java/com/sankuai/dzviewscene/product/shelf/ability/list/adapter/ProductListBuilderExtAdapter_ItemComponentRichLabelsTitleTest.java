package com.sankuai.dzviewscene.product.shelf.ability.list.adapter;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.execution.PmfExecutionHelper;
import com.sankuai.dzviewscene.product.shelf.ability.list.vpoints.RichLabelsTitleVP;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.HashMap;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

public class ProductListBuilderExtAdapter_ItemComponentRichLabelsTitleTest {

    @InjectMocks
    private ProductListBuilderExtAdapter productListBuilderExtAdapter;

    @Mock
    private PmfExecutionHelper pmfExecutionHelper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testItemComponentRichLabelsTitleActivityContextIsNull() throws Throwable {
        productListBuilderExtAdapter.itemComponentRichLabelsTitle(null, "groupName", new ProductM(), 1L);
    }

    @Test
    public void testItemComponentRichLabelsTitleParametersIsNull() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        RichLabelsTitleVP richLabelsTitleVP = mock(RichLabelsTitleVP.class);
        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), anyString(), anyString())).thenReturn(richLabelsTitleVP);
        when(richLabelsTitleVP.execute(any(ActivityCxt.class), any())).thenReturn("");
        String result = productListBuilderExtAdapter.itemComponentRichLabelsTitle(activityContext, "groupName", new ProductM(), 1L);
        assertEquals("", result);
    }

    @Test
    public void testItemComponentRichLabelsTitleParamsNotExist() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setParameters(new HashMap<>());
        RichLabelsTitleVP richLabelsTitleVP = mock(RichLabelsTitleVP.class);
        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), anyString(), anyString())).thenReturn(richLabelsTitleVP);
        when(richLabelsTitleVP.execute(any(ActivityCxt.class), any())).thenReturn("");
        String result = productListBuilderExtAdapter.itemComponentRichLabelsTitle(activityContext, "groupName", new ProductM(), 1L);
        assertEquals("", result);
    }

    @Test(expected = RuntimeException.class)
    public void testItemComponentRichLabelsTitleExecuteThrowException() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setParameters(new HashMap<>());
        activityContext.getParameters().put("platform", 1);
        activityContext.getParameters().put("searchKeyword", "keyword");
        RichLabelsTitleVP richLabelsTitleVP = mock(RichLabelsTitleVP.class);
        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), anyString(), anyString())).thenReturn(richLabelsTitleVP);
        when(richLabelsTitleVP.execute(any(ActivityCxt.class), any())).thenThrow(new RuntimeException());
        productListBuilderExtAdapter.itemComponentRichLabelsTitle(activityContext, "groupName", new ProductM(), 1L);
    }

    @Test
    public void testItemComponentRichLabelsTitleNormal() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setParameters(new HashMap<>());
        activityContext.getParameters().put("platform", 1);
        activityContext.getParameters().put("searchKeyword", "keyword");
        RichLabelsTitleVP richLabelsTitleVP = mock(RichLabelsTitleVP.class);
        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), anyString(), anyString())).thenReturn(richLabelsTitleVP);
        when(richLabelsTitleVP.execute(any(ActivityCxt.class), any())).thenReturn("title");
        String result = productListBuilderExtAdapter.itemComponentRichLabelsTitle(activityContext, "groupName", new ProductM(), 1L);
        assertEquals("title", result);
    }
}
