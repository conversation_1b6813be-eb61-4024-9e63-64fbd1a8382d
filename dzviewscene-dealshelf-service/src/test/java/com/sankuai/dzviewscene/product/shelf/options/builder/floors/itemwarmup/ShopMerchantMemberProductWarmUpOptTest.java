package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemwarmup;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductWarmUpVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.warmup.ShopMerchantMemberProductWarmUpOpt;
import com.sankuai.dzviewscene.productshelf.vu.vo.WarmUpVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.Mock;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShopMerchantMemberProductWarmUpOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    @Mock
    private ProductWarmUpVP.Param param;

    @Test
    public void testComputeWarmUpStageResultIsNull() throws Throwable {
        ShopMerchantMemberProductWarmUpOpt opt = new ShopMerchantMemberProductWarmUpOpt();
        when(param.getProductM()).thenReturn(productM);
        WarmUpStageEnum.WarmUpStageResult warmUpStageResult = WarmUpStageEnum.getWarmUpStageResult(productM);
        if(null == warmUpStageResult){
            warmUpStageResult = new WarmUpStageEnum.WarmUpStageResult();
        }
        warmUpStageResult.setStage(null);
        WarmUpVO result = opt.compute(activityCxt, param, null);
        assertNull(result);
    }

    @Test
    public void testComputeWarmUpStageResultIsInvalid() throws Throwable {
        ShopMerchantMemberProductWarmUpOpt opt = new ShopMerchantMemberProductWarmUpOpt();
        when(param.getProductM()).thenReturn(productM);
        WarmUpStageEnum.WarmUpStageContext warmUpStageContext = mock(WarmUpStageEnum.WarmUpStageContext.class);
        //when(warmUpStageContext.getProductM()).thenReturn(productM);
        when(productM.getAttr("warmUpStartTimeAttr")).thenReturn("1706177736000");
        when(productM.getAttr("timeStockAttr")).thenReturn("{\"timeStockStatus\":1,\"currentTimeStockItem\":{\"startTime\": 1706091336000,\"endTime\":1706094936000},\"nextTimeStockItem\":{\"startTime\": 1706091336000,\"endTime\":1706282136000}}");
        WarmUpStageEnum.WarmUpStageResult warmUpStageResult = WarmUpStageEnum.getWarmUpStageResult(productM);
        if(null == warmUpStageResult){
            warmUpStageResult = new WarmUpStageEnum.WarmUpStageResult();
        }
        //1.ONLY_WARM_UP_PRESALE
        //when(warmUpStageContext.getWarmUpStartTime()).thenReturn(1706091336000L);
        when(productM.getBeginDate()).thenReturn(1706177736000L);
        warmUpStageResult.setStage(WarmUpStageEnum.ONLY_WARM_UP_PRESALE);
        WarmUpVO result = opt.compute(activityCxt, param, null);
        Assert.assertNotNull(result);

    }

}
