package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import java.math.BigDecimal;
import org.mockito.Mockito;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class PetSimilarAndTimesDealTitleOptTest {

    private PetSimilarAndTimesDealTitleOpt petSimilarAndTimesDealTitleOpt;

    private ActivityCxt context;

    private PetSimilarAndTimesDealTitleOpt.Param mockParam;

    private PetSimilarAndTimesDealTitleOpt.Config mockConfig;

    @Test
    public void testComputeNotTimesDeal() throws Throwable {
        PetSimilarAndTimesDealTitleOpt petSimilarAndTimesDealTitleOpt = new PetSimilarAndTimesDealTitleOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        PetSimilarAndTimesDealTitleOpt.Param mockParam = mock(PetSimilarAndTimesDealTitleOpt.Param.class);
        PetSimilarAndTimesDealTitleOpt.Config mockConfig = mock(PetSimilarAndTimesDealTitleOpt.Config.class);
        ProductM productM = new ProductM();
        productM.setProductId(1);
        // Non-times deal
        productM.setTradeType(20);
        productM.setTitle("title");
        when(mockParam.getProductM()).thenReturn(productM);
        String title = petSimilarAndTimesDealTitleOpt.compute(context, mockParam, mockConfig);
        assertEquals("title", title);
    }

    @Test
    public void testComputeTimesDealButTimesOrSinglePriceIsNull() throws Throwable {
        PetSimilarAndTimesDealTitleOpt petSimilarAndTimesDealTitleOpt = new PetSimilarAndTimesDealTitleOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        PetSimilarAndTimesDealTitleOpt.Param mockParam = mock(PetSimilarAndTimesDealTitleOpt.Param.class);
        PetSimilarAndTimesDealTitleOpt.Config mockConfig = mock(PetSimilarAndTimesDealTitleOpt.Config.class);
        ProductM productM = new ProductM();
        productM.setProductId(1);
        // Times deal
        productM.setTradeType(19);
        productM.setTitle("title");
        when(mockParam.getProductM()).thenReturn(productM);
        String title = petSimilarAndTimesDealTitleOpt.compute(context, mockParam, mockConfig);
        assertEquals("title", title);
    }
}
