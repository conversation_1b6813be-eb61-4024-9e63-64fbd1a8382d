package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemwarmup;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemWarmUpVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemWarmUpVP.Param;
import com.sankuai.dzviewscene.productshelf.vu.vo.WarmUpVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class NullItemWarmUpOptTest {

    /**
     * Tests whether the compute method returns null.
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // arrange
        NullItemWarmUpOpt nullItemWarmUpOpt = new NullItemWarmUpOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        // Using the builder pattern to create an instance of Param, assuming it's available.
        ItemWarmUpVP.Param param = ItemWarmUpVP.Param.builder().productM(productM).build();
        // act
        WarmUpVO result = nullItemWarmUpOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }
}
