package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempriceabovetags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceAboveTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.collections.Lists;

import java.util.HashMap;
import java.util.List;

import static org.mockito.Mockito.when;

public class ConfigItemPriceAboveTagsOptTest {

    private ConfigItemPriceAboveTagsOpt configItemPriceAboveTagsOpt;

    private ActivityCxt activityCxt;

    private ItemPriceAboveTagsVP.Param param;

    private ConfigItemPriceAboveTagsOpt.Config config;

    private ProductM productM;

    @Before
    public void initialize() {
        configItemPriceAboveTagsOpt = new ConfigItemPriceAboveTagsOpt();
        activityCxt = new ActivityCxt();
        productM = Mockito.mock(ProductM.class);
        param = ItemPriceAboveTagsVP.Param.builder().productM(productM).build();
        config = new ConfigItemPriceAboveTagsOpt.Config();
    }

    @Test
    public void test(){
        config.setAttrKeys(Lists.newArrayList("available_time"));
        config.setAttrKey2Cfg(new HashMap<>());
        activityCxt.setSceneCode("beauty_deal_reserve_shelf");
        HashMap<String, Object> params = new HashMap<>();
        ShopM shopM = new ShopM();
        shopM.setCategory(34281);
        params.put(ShelfActivityConstants.Ctx.ctxShop, shopM);
        activityCxt.setParameters(params);
        when(productM.getAttr("available_time")).thenReturn("最早12点可约");
        List<DzTagVO> compute = configItemPriceAboveTagsOpt.compute(activityCxt, param, config);
        Assert.assertTrue(compute.size() == 1);
    }
}
