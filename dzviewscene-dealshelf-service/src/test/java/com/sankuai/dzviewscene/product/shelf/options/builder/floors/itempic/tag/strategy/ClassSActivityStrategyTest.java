package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.gmkt.activ.api.enums.ExposureActivityTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ClassSActivityStrategyTest {

    @InjectMocks
    private ClassSActivityStrategy classSActivityStrategy;

    @Mock
    private FloatTagBuildReq floatTagBuildReq;

    @Mock
    private ProductM productM;

    @Mock
    private ProductActivityM productActivityM;

    private FloatTagBuildReq param;

    private FloatTagBuildCfg config;

    private ActivityCxt activityCxt;

    @Before
    public void setUp() {
        param = new FloatTagBuildReq();
        config = new FloatTagBuildCfg();
        activityCxt = new ActivityCxt();
        param.setContext(activityCxt);
        param.setProductM(productM);
    }

    @Test
    public void testBuildTagWhenNoClassSActivity() throws Throwable {
        when(floatTagBuildReq.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(Collections.singletonList(productActivityM));
        when(productActivityM.getShelfActivityType()).thenReturn(ExposureActivityTypeEnum.CLASS_S.getType());
        FloatTagVO result = classSActivityStrategy.buildTag(floatTagBuildReq, new FloatTagBuildCfg());
        assertNotNull(result);
    }

    @Test
    public void testBuildTagWhenClassSActivityUrlIsEmpty() throws Throwable {
        when(floatTagBuildReq.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(Collections.singletonList(productActivityM));
        when(productActivityM.getShelfActivityType()).thenReturn(ExposureActivityTypeEnum.CLASS_S.getType());
        when(productActivityM.getUrl()).thenReturn("");
        FloatTagVO result = classSActivityStrategy.buildTag(floatTagBuildReq, new FloatTagBuildCfg());
        assertNotNull(result);
    }

    @Test
    public void testBuildTagWhenClassSActivityUrlIsNotEmpty() throws Throwable {
        when(floatTagBuildReq.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(Collections.singletonList(productActivityM));
        when(productActivityM.getShelfActivityType()).thenReturn(ExposureActivityTypeEnum.CLASS_S.getType());
        when(productActivityM.getUrl()).thenReturn("http://example.com/image.jpg");
        FloatTagVO result = classSActivityStrategy.buildTag(floatTagBuildReq, new FloatTagBuildCfg());
        assertNotNull(result);
    }

    /**
     * Test case to verify isMatch returns false when PromoSimplifyUtils.hitPromoSimplifyV2 returns true
     */
    @Test
    public void testIsMatch_WhenHitPromoSimplifyV2_ReturnsFalse() throws Throwable {
        // arrange
        try (MockedStatic<PromoSimplifyUtils> mockedStatic = mockStatic(PromoSimplifyUtils.class)) {
            mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(activityCxt)).thenReturn(true);
            // act & assert
            assertFalse(classSActivityStrategy.isMatch(param, config));
        }
    }

    /**
     * Test case to verify isMatch when activity list is empty
     */
    @Test
    public void testIsMatch_WhenActivitiesEmpty_ReturnsFalse() throws Throwable {
        // arrange
        when(productM.getActivities()).thenReturn(Collections.emptyList());
        try (MockedStatic<PromoSimplifyUtils> mockedStatic = mockStatic(PromoSimplifyUtils.class)) {
            mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(activityCxt)).thenReturn(false);
            // act & assert
            assertFalse(classSActivityStrategy.isMatch(param, config));
        }
    }

    /**
     * Test case to verify isMatch when activity type is CLASS_S and URL is not empty
     */
    @Test
    public void testIsMatch_WhenClassSActivityWithUrl_ReturnsTrue() throws Throwable {
        // arrange
        ProductActivityM activity = new ProductActivityM();
        activity.setShelfActivityType(ExposureActivityTypeEnum.CLASS_S.getType());
        activity.setUrl("test-url");
        when(productM.getActivities()).thenReturn(Collections.singletonList(activity));
        try (MockedStatic<PromoSimplifyUtils> mockedStatic = mockStatic(PromoSimplifyUtils.class)) {
            mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(activityCxt)).thenReturn(false);
            // act & assert
            assertTrue(classSActivityStrategy.isMatch(param, config));
        }
    }

    /**
     * Test case to verify isMatch when activity type is CLASS_S and label is empty
     */
    @Test
    public void testIsMatch_WhenClassSActivityWithEmptyLabel_ReturnsTrue() throws Throwable {
        // arrange
        ProductActivityM activity = new ProductActivityM();
        activity.setShelfActivityType(ExposureActivityTypeEnum.CLASS_S.getType());
        activity.setLable("");
        when(productM.getActivities()).thenReturn(Collections.singletonList(activity));
        try (MockedStatic<PromoSimplifyUtils> mockedStatic = mockStatic(PromoSimplifyUtils.class)) {
            mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(activityCxt)).thenReturn(false);
            // act & assert
            assertTrue(classSActivityStrategy.isMatch(param, config));
        }
    }

    /**
     * Test case to verify isMatch when activity type is not CLASS_S
     */
    @Test
    public void testIsMatch_WhenNotClassSActivity_ReturnsFalse() throws Throwable {
        // arrange
        ProductActivityM activity = new ProductActivityM();
        activity.setShelfActivityType(ExposureActivityTypeEnum.DEFAULT.getType());
        when(productM.getActivities()).thenReturn(Collections.singletonList(activity));
        try (MockedStatic<PromoSimplifyUtils> mockedStatic = mockStatic(PromoSimplifyUtils.class)) {
            mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(activityCxt)).thenReturn(false);
            // act & assert
            assertFalse(classSActivityStrategy.isMatch(param, config));
        }
    }

    /**
     * Test case to verify isMatch when multiple activities exist but none match CLASS_S
     */
    @Test
    public void testIsMatch_WhenMultipleActivitiesNoClassS_ReturnsFalse() throws Throwable {
        // arrange
        ProductActivityM activity1 = new ProductActivityM();
        activity1.setShelfActivityType(ExposureActivityTypeEnum.DEFAULT.getType());
        ProductActivityM activity2 = new ProductActivityM();
        activity2.setShelfActivityType(ExposureActivityTypeEnum.WAN_MEI_JI.getType());
        when(productM.getActivities()).thenReturn(Arrays.asList(activity1, activity2));
        try (MockedStatic<PromoSimplifyUtils> mockedStatic = mockStatic(PromoSimplifyUtils.class)) {
            mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(activityCxt)).thenReturn(false);
            // act & assert
            assertFalse(classSActivityStrategy.isMatch(param, config));
        }
    }
}
