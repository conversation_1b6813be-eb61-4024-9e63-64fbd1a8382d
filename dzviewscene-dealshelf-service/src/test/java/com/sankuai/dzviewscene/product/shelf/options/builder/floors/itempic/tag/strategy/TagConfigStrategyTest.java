package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;

@RunWith(MockitoJUnitRunner.class)
public class TagConfigStrategyTest {

    /**
     * Test the buildTag method under normal conditions.
     * @throws Throwable
     */
    @Test
    public void testBuildTagNormal() throws Throwable {
        // Arrange
        TagConfigStrategy tagConfigStrategy = new TagConfigStrategy();
        FloatTagBuildReq param = mock(FloatTagBuildReq.class);
        FloatTagBuildCfg config = mock(FloatTagBuildCfg.class);
        when(config.getPicUrl()).thenReturn("picUrl");
        when(config.getPicAspectRadio()).thenReturn(1.0);
        when(config.getPosition()).thenReturn(1);
        // Act
        FloatTagVO result = tagConfigStrategy.buildTag(param, config);
        // Assert
        assertNotNull(result);
        assertNotNull(result.getIcon());
        assertEquals("picUrl", result.getIcon().getPicUrl());
        assertEquals(1.0, result.getIcon().getAspectRadio(), 0.0);
        assertEquals(1, result.getPosition());
    }

    /**
     * Test the buildTag method with a null config.
     * @throws Throwable
     */
    @Test(expected = NullPointerException.class)
    public void testBuildTagConfigNull() throws Throwable {
        // Arrange
        TagConfigStrategy tagConfigStrategy = new TagConfigStrategy();
        FloatTagBuildReq param = mock(FloatTagBuildReq.class);
        FloatTagBuildCfg config = null;
        // Act
        tagConfigStrategy.buildTag(param, config);
    }

    /**
     * Test the buildTag method with a null picture URL in the config.
     * @throws Throwable
     */
    @Test
    public void testBuildTagConfigPicUrlNull() throws Throwable {
        // Arrange
        TagConfigStrategy tagConfigStrategy = new TagConfigStrategy();
        FloatTagBuildReq param = mock(FloatTagBuildReq.class);
        FloatTagBuildCfg config = mock(FloatTagBuildCfg.class);
        when(config.getPicUrl()).thenReturn(null);
        when(config.getPicAspectRadio()).thenReturn(1.0);
        when(config.getPosition()).thenReturn(1);
        // Act
        FloatTagVO result = tagConfigStrategy.buildTag(param, config);
        // Assert
        assertNotNull(result);
        assertNull(result.getIcon().getPicUrl());
    }

    /**
     * Test the buildTag method with a null picture aspect ratio in the config.
     * @throws Throwable
     */
    @Test
    public void testBuildTagConfigPicAspectRadioNull() throws Throwable {
        // Arrange
        TagConfigStrategy tagConfigStrategy = new TagConfigStrategy();
        FloatTagBuildReq param = mock(FloatTagBuildReq.class);
        FloatTagBuildCfg config = mock(FloatTagBuildCfg.class);
        when(config.getPicUrl()).thenReturn("picUrl");
        doReturn(0.0).when(config).getPicAspectRadio();
        when(config.getPosition()).thenReturn(1);
        // Act
        FloatTagVO result = tagConfigStrategy.buildTag(param, config);
        // Assert
        assertNotNull(result);
        assertEquals(0.0, result.getIcon().getAspectRadio(), 0.0);
    }
}
