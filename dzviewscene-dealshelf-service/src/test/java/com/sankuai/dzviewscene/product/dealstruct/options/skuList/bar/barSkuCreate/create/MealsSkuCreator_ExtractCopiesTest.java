package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class MealsSkuCreator_ExtractCopiesTest {

    private MealsSkuCreator mealsSkuCreator = new MealsSkuCreator();

    @Test
    public void testExtractCopiesWhenAttrItemsIsEmpty() {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(null);
        skuItemDto.setCopies(2);
        // act
        String result = mealsSkuCreator.extractCopies(skuItemDto, new BarDealDetailSkuListModuleOpt.Config());
        // assert
        assertEquals("(2份)", result);
    }

    @Test
    public void testExtractCopiesWhenAttrItemsHasUnitAttrButValueIsEmpty() {
        // arrange
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("unit");
        skuAttrItemDto.setAttrValue("");
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        skuItemDto.setCopies(2);
        // act
        String result = mealsSkuCreator.extractCopies(skuItemDto, new BarDealDetailSkuListModuleOpt.Config());
        // assert
        assertEquals("(2份)", result);
    }

    @Test
    public void testExtractCopiesWhenAttrItemsHasUnitAttrAndValueIsNotEmpty() {
        // arrange
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("unit");
        skuAttrItemDto.setAttrValue("3");
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        skuItemDto.setCopies(2);
        // act
        String result = mealsSkuCreator.extractCopies(skuItemDto, new BarDealDetailSkuListModuleOpt.Config());
        // assert
        assertEquals("(6份)", result);
    }

    @Test
    public void testExtractCopiesWhenCopiesIsZero() {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(null);
        skuItemDto.setCopies(0);
        // act
        String result = mealsSkuCreator.extractCopies(skuItemDto, new BarDealDetailSkuListModuleOpt.Config());
        // assert
        assertEquals("(0份)", result);
    }
}
