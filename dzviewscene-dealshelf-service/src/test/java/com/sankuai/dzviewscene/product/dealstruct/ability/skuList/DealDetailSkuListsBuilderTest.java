package com.sankuai.dzviewscene.product.dealstruct.ability.skuList;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailSkuListsBuilderTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailSkuListsParam dealDetailSkuListsParam;

    @Mock
    private DealDetailSkuListsCfg dealDetailSkuListsCfg;

    @Mock
    private DealDetailAssembleParam assembleParam;

    private DealDetailSkuListsBuilder dealDetailSkuListsBuilder;

    @Before
    public void setUp() {
        dealDetailSkuListsBuilder = new DealDetailSkuListsBuilder();
    }

    // Helper method to create a valid DealDetailInfoModel
    private DealDetailInfoModel createValidDealDetailInfoModel() {
        DealDetailInfoModel model = new DealDetailInfoModel();
        DealDetailDtoModel dtoModel = new DealDetailDtoModel();
        dtoModel.setSkuUniStructuredDto(new DealDetailSkuUniStructuredDto());
        model.setDealDetailDtoModel(dtoModel);
        return model;
    }

    @Test
    public void testBuildWhenDealDetailInfoModelsIsEmpty() throws Throwable {
        DealDetailSkuListsBuilder builder = new DealDetailSkuListsBuilder();
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, dealDetailSkuListsParam, dealDetailSkuListsCfg);
        assertEquals(0, result.get().size());
    }

    @Test
    public void testBuildWhenAllDetailModelsAreInvalid() throws Throwable {
        DealDetailSkuListsBuilder builder = new DealDetailSkuListsBuilder();
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(new DealDetailInfoModel()));
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, dealDetailSkuListsParam, dealDetailSkuListsCfg);
        assertEquals(0, result.get().size());
    }

    @Test
    public void testBuildModelVODealSkuGroupModuleVoListsIsNull() throws Throwable {
        when(activityCxt.getSource(DealDetailSkuListsBuilder.CODE)).thenReturn(null);
        DealDetailModuleVO result = dealDetailSkuListsBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNull(result);
    }

    @Test
    public void testBuildModelVODealSkuGroupModuleVOSIsNull() throws Throwable {
        when(activityCxt.getSource(DealDetailSkuListsBuilder.CODE)).thenReturn(Collections.emptyList());
        DealDetailModuleVO result = dealDetailSkuListsBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNull(result);
    }

    @Test
    public void testBuildModelVODealDetailInfoModelsIsNull() throws Throwable {
        when(activityCxt.getSource(DealDetailSkuListsBuilder.CODE)).thenReturn(Collections.singletonList(Collections.singletonList(new DealSkuGroupModuleVO())));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(null);
        DealDetailModuleVO result = dealDetailSkuListsBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNotNull(result);
    }

    @Test
    public void testBuildModelVODealDetailInfoModelIsNull() throws Throwable {
        when(activityCxt.getSource(DealDetailSkuListsBuilder.CODE)).thenReturn(Collections.singletonList(Collections.singletonList(new DealSkuGroupModuleVO())));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());
        DealDetailModuleVO result = dealDetailSkuListsBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNotNull(result);
    }

    @Test
    public void testBuildModelVODealDetailInfoModelIsNotTimesDeal() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        dealDetailInfoModel.setTradeType(18);
        when(activityCxt.getSource(DealDetailSkuListsBuilder.CODE)).thenReturn(Collections.singletonList(Collections.singletonList(new DealSkuGroupModuleVO())));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(dealDetailInfoModel));
        DealDetailModuleVO result = dealDetailSkuListsBuilder.buildModelVO(activityCxt, assembleParam, "config");
        assertNotNull(result);
        assertNull(result.getName());
    }
}
