package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class KtvSimilarListOpt_SortFoodListTest {

    private KtvSimilarListOpt ktvSimilarListOpt;

    @Test
    public void testSortFoodListWithEmptyList() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        List<ProductM> filterList = Collections.emptyList();
        List<ProductM> result = ktvSimilarListOpt.sortFoodList(filterList);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testSortFoodListWithUnknownServiceType() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        ProductM productM = Mockito.mock(ProductM.class);
        ProductSaleM saleM = Mockito.mock(ProductSaleM.class);
        Mockito.when(productM.getExtAttrs()).thenReturn(Collections.singletonList(new AttrM("serviceType", "UNKNOWN_SERVICE_TYPE")));
        Mockito.when(productM.getSale()).thenReturn(saleM);
        Mockito.when(saleM.getSale()).thenReturn(10);
        List<ProductM> filterList = Collections.singletonList(productM);
        List<ProductM> result = ktvSimilarListOpt.sortFoodList(filterList);
        Assert.assertTrue(result.isEmpty());
    }
}
