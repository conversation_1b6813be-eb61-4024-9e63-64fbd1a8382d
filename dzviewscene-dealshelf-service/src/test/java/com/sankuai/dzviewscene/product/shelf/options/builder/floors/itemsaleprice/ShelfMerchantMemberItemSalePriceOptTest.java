package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsaleprice;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice.MerchantMemberProductSalePriceOpt;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice.SimilarShelfProductMerchantMemberSalePriceOpt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import java.math.BigDecimal;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.testng.collections.Lists;

@RunWith(MockitoJUnitRunner.class)
public class ShelfMerchantMemberItemSalePriceOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private CardM cardM;

    @InjectMocks
    private ShelfMerchantMemberItemSalePriceOpt itemSalePriceOpt;

    @Mock
    private ProductM productM;

    private ProductM buildProductM() {
        ProductM productM = new ProductM();
        List<ProductPromoPriceM> promoPrices = Lists.newArrayList();
        ProductPromoPriceM promoPrice1 = new ProductPromoPriceM();
        promoPrice1.setPromoType(PromoTypeEnum.MERCHANT_MEMBER.getType());
        promoPrice1.setPromoTagType(PromoTagTypeEnum.Merchant_Member.getCode());
        promoPrice1.setPromoPrice(new BigDecimal(44));
        promoPrice1.setPromoPriceTag("66");
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromoTypeCode(PromoTypeEnum.MERCHANT_MEMBER.getType());
        promoPrice1.setPromoItemList(Lists.newArrayList(promoItemM));
        promoPrices.add(promoPrice1);
        ProductPromoPriceM promoPrice2 = new ProductPromoPriceM();
        promoPrice2.setPromoType(PromoTypeEnum.NORMAL_PROMO.getType());
        promoPrice2.setPromoTagType(PromoTagTypeEnum.Default.getCode());
        promoPrices.add(promoPrice2);
        promoPrice2.setPromoPrice(new BigDecimal(66));
        promoPrice2.setPromoPriceTag("44");
        productM.setPromoPrices(promoPrices);
        productM.setAttr("MERCHANT_MEMBER_DEAL", "{\"memberDiscountType\":2,\"merchantMember\":{\"isMember\":true,\"isNewMember\":false}}");
        return productM;
    }

    /**
     * 测试场景：cardPromo为空，玩美季活动未进行，merchantMemberPromoPriceM的promoPriceTag为空，productPromoPriceM的promoPriceTag不为空
     */
    @Test
    public void testComputeProductPromoPrice() {
        SimilarShelfProductMerchantMemberSalePriceOpt similarShelfProductMerchantMemberSalePriceOpt = new SimilarShelfProductMerchantMemberSalePriceOpt();
        ShelfMerchantMemberItemSalePriceOpt shelfMerchantMemberItemSalePriceOpt = new ShelfMerchantMemberItemSalePriceOpt();
        MerchantMemberProductSalePriceOpt merchantMemberProductSalePriceOpt = new MerchantMemberProductSalePriceOpt();
        ProductM productM = buildProductM();
        when(activityCxt.getSource(any())).thenReturn(cardM);
        String result = shelfMerchantMemberItemSalePriceOpt.compute(activityCxt, ItemSalePriceVP.Param.builder().productM(productM).build(), null);
        assertNull(result);
        ProductSalePriceVP.Param param1 = mock(ProductSalePriceVP.Param.class);
        String similarResult = similarShelfProductMerchantMemberSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        assertEquals("¥66", similarResult);
        String productSaleResult = merchantMemberProductSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        assertEquals("66", productSaleResult);
    }

    /**
     * Test case for exception handling when CardFetcher.CODE throws exception
     */
    @Test
    public void testComputeWhenExceptionOccurs() {
        // arrange
        String expectedBasePriceTag = "100";
        when(activityCxt.getSource(CardFetcher.CODE)).thenThrow(new RuntimeException("Test exception"));
        when(productM.getBasePriceTag()).thenReturn(expectedBasePriceTag);
        ShelfMerchantMemberItemSalePriceOpt.Param param = ShelfMerchantMemberItemSalePriceOpt.Param.builder().productM(productM).build();
        // act
        String result = itemSalePriceOpt.compute(activityCxt, param, null);
        // assert
        assertEquals(expectedBasePriceTag, result);
    }

    /**
     * Test case for when promoPriceM is null
     */
    @Test
    public void testComputeWhenPromoPriceIsNull() {
        // arrange
        String expectedBasePriceTag = "100";
        CardM cardM = new CardM();
        when(activityCxt.getSource(CardFetcher.CODE)).thenReturn(cardM);
        when(productM.getBasePriceTag()).thenReturn(expectedBasePriceTag);
        ShelfMerchantMemberItemSalePriceOpt.Param param = ShelfMerchantMemberItemSalePriceOpt.Param.builder().productM(productM).build();
        // act
        String result = itemSalePriceOpt.compute(activityCxt, param, null);
        // assert
        assertEquals(expectedBasePriceTag, result);
    }
}
