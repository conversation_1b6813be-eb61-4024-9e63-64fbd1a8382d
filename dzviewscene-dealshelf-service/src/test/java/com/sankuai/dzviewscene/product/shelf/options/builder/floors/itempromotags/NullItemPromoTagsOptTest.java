package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.List;
import org.junit.runner.RunWith;
import java.lang.reflect.Constructor;
import java.util.Map;

import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP.Param;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class NullItemPromoTagsOptTest {

    private ItemPromoTagsVP.Param createParamInstance(ProductM productM, CardM cardM, String subScene, String salePrice, List<DouHuM> douHuList, int productTotalNum, String extra) throws Exception {
        Constructor<ItemPromoTagsVP.Param> constructor = ItemPromoTagsVP.Param.class.getDeclaredConstructor(ProductM.class, CardM.class, String.class, String.class, List.class, int.class, String.class, String.class, Map.class, String.class);
        constructor.setAccessible(true);
        return constructor.newInstance(productM, cardM, subScene, salePrice, douHuList, productTotalNum, extra, "a", Maps.newHashMap(), "团购");
    }

    @Test
    public void testComputeReturnNull() throws Throwable {
        NullItemPromoTagsOpt nullItemPromoTagsOpt = new NullItemPromoTagsOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = mock(ProductM.class);
        CardM cardM = mock(CardM.class);
        List<DouHuM> douHuList = new ArrayList<>();
        ItemPromoTagsVP.Param param = createParamInstance(productM, cardM, "subScene", "salePrice", douHuList, 1, "extra");
        List<DzPromoVO> result = nullItemPromoTagsOpt.compute(context, param, null);
        assertNull(result);
    }

    @Test
    public void testComputeWithNullContext() throws Throwable {
        NullItemPromoTagsOpt nullItemPromoTagsOpt = new NullItemPromoTagsOpt();
        ActivityCxt context = null;
        ProductM productM = mock(ProductM.class);
        CardM cardM = mock(CardM.class);
        List<DouHuM> douHuList = new ArrayList<>();
        ItemPromoTagsVP.Param param = createParamInstance(productM, cardM, "subScene", "salePrice", douHuList, 1, "extra");
        List<DzPromoVO> result = nullItemPromoTagsOpt.compute(context, param, null);
        assertNull(result);
    }

    @Test
    public void testComputeWithNullParam() throws Throwable {
        NullItemPromoTagsOpt nullItemPromoTagsOpt = new NullItemPromoTagsOpt();
        ActivityCxt context = new ActivityCxt();
        ItemPromoTagsVP.Param param = null;
        List<DzPromoVO> result = nullItemPromoTagsOpt.compute(context, param, null);
        assertNull(result);
    }

    @Test
    public void testComputeWithNullUnused() throws Throwable {
        NullItemPromoTagsOpt nullItemPromoTagsOpt = new NullItemPromoTagsOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = mock(ProductM.class);
        CardM cardM = mock(CardM.class);
        List<DouHuM> douHuList = new ArrayList<>();
        ItemPromoTagsVP.Param param = createParamInstance(productM, cardM, "subScene", "salePrice", douHuList, 1, "extra");
        Void unused = null;
        List<DzPromoVO> result = nullItemPromoTagsOpt.compute(context, param, unused);
        assertNull(result);
    }
}
