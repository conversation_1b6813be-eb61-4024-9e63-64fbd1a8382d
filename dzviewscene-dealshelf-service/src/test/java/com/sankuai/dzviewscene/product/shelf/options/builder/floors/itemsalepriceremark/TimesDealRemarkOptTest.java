package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceremark;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceRemarkVP;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TimesDealRemarkOptTest {


    @Test
    public void testComputeNotTimesDeal() {
        ActivityCxt context = mock(ActivityCxt.class);
        TimesDealRemarkOpt.Config config = mock(TimesDealRemarkOpt.Config.class);

        TimesDealRemarkOpt opt = new TimesDealRemarkOpt();
        ProductM productM = new ProductM();
        productM.setTradeType(1);
        String result = opt.compute(context, ItemSalePriceRemarkVP.Param.builder().productM(productM).build(), config);

        assertNull(result);
    }

    @Test
    public void testComputeTimesKeyBlank() {
        ActivityCxt context = mock(ActivityCxt.class);
        TimesDealRemarkOpt opt = new TimesDealRemarkOpt();
        ProductM productM = new ProductM();
        productM.setTradeType(19);

        try (MockedStatic<TimesDealUtil> mockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            mockedStatic.when(() -> TimesDealUtil.hitTimesStyle1(Mockito.any(), Mockito.any())).thenReturn(false);
            TimesDealRemarkOpt.Config config = new TimesDealRemarkOpt.Config();
            String result = opt.compute(context, ItemSalePriceRemarkVP.Param.builder().productM(productM).build(), config);
            assertNull(result);
        }
    }

    @Test
    public void testComputeRemarkTemplateBlank() {
        ActivityCxt context = mock(ActivityCxt.class);
        TimesDealRemarkOpt opt = new TimesDealRemarkOpt();
        ProductM productM = new ProductM();
        productM.setTradeType(19);

        try (MockedStatic<TimesDealUtil> mockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            mockedStatic.when(() -> TimesDealUtil.hitTimesStyle1(Mockito.any(), Mockito.any())).thenReturn(true);
            TimesDealRemarkOpt.Config config = new TimesDealRemarkOpt.Config();
            config.setTimesKey("key");
            String result = opt.compute(context, ItemSalePriceRemarkVP.Param.builder().productM(productM).build(), config);
            assertNull(result);
        }
    }

    @Test
    public void testComputeAttrBlank() {
        ActivityCxt context = mock(ActivityCxt.class);
        TimesDealRemarkOpt opt = new TimesDealRemarkOpt();
        ProductM productM = new ProductM();
        productM.setAttr("key", "");
        productM.setTradeType(19);

        try (MockedStatic<TimesDealUtil> mockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            mockedStatic.when(() -> TimesDealUtil.hitTimesStyle1(Mockito.any(), Mockito.any())).thenReturn(true);
            TimesDealRemarkOpt.Config config = new TimesDealRemarkOpt.Config();
            config.setTimesKey("key");
            config.setRemarkTemplate("/%s次");
            String result = opt.compute(context, ItemSalePriceRemarkVP.Param.builder().productM(productM).build(), config);
            assertNull(result);
        }

    }

    @Test
    public void testComputeAttrNotDigits() {
        ActivityCxt context = mock(ActivityCxt.class);
        TimesDealRemarkOpt opt = new TimesDealRemarkOpt();
        ProductM productM = new ProductM();
        productM.setAttr("key", "aaa");
        productM.setTradeType(19);
        try (MockedStatic<TimesDealUtil> mockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            mockedStatic.when(() -> TimesDealUtil.hitTimesStyle1(Mockito.any(), Mockito.any())).thenReturn(true);
            TimesDealRemarkOpt.Config config = new TimesDealRemarkOpt.Config();
            config.setTimesKey("key");
            config.setRemarkTemplate("/%s次");
            String result = opt.compute(context, ItemSalePriceRemarkVP.Param.builder().productM(productM).build(), config);
            assertNull(result);
        }
    }

    @Test
    public void testComputeNormal() {
        ActivityCxt context = mock(ActivityCxt.class);
        TimesDealRemarkOpt opt = new TimesDealRemarkOpt();
        ProductM productM = new ProductM();
        productM.setAttr("key", "3");
        productM.setTradeType(19);

        try (MockedStatic<TimesDealUtil> mockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            mockedStatic.when(() -> TimesDealUtil.hitTimesStyle1(Mockito.any(), Mockito.any())).thenReturn(true);
            TimesDealRemarkOpt.Config config = new TimesDealRemarkOpt.Config();
            config.setTimesKey("key");
            config.setRemarkTemplate("/%s次");
            String result = opt.compute(context, ItemSalePriceRemarkVP.Param.builder().productM(productM).build(), config);
            assertEquals("/3次", result);
        }
    }
}
