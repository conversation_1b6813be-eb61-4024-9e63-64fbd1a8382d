package com.sankuai.dzviewscene.product.shelf.options.builder.floors.defaultshownum;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

/**
 * Test for PageDealShelfShowNumOpt
 */
@RunWith(MockitoJUnitRunner.class)
public class PageDealShelfShowNumOptComputeTest {

    @Mock
    private ActivityCxt mockContext;

    /**
     * Test compute() when judgeDealShelfHasPage returns false
     * Should return config.getDefaultNum()
     */
    @Test
    public void testComputeWhenNotHasPage() {
        // arrange
        PageDealShelfShowNumOpt target = new PageDealShelfShowNumOpt();
        PageDealShelfShowNumOpt.Config config = new PageDealShelfShowNumOpt.Config();
        // default value is 4
        config.setDefaultNum(4);
        try (MockedStatic<ParamsUtil> mockedStatic = mockStatic(ParamsUtil.class)) {
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(any())).thenReturn(false);
            // act
            Integer result = target.compute(mockContext, null, config);
            // assert
            assertEquals(Integer.valueOf(4), result);
        }
    }

    /**
     * Test compute() when judgeDealShelfHasPage returns false and defaultNum is set to different value
     * Should return config.getDefaultNum()
     */
    @Test
    public void testComputeWhenNotHasPageCustomDefaultNum() {
        // arrange
        PageDealShelfShowNumOpt target = new PageDealShelfShowNumOpt();
        PageDealShelfShowNumOpt.Config config = new PageDealShelfShowNumOpt.Config();
        // custom default value
        config.setDefaultNum(10);
        try (MockedStatic<ParamsUtil> mockedStatic = mockStatic(ParamsUtil.class)) {
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(any())).thenReturn(false);
            // act
            Integer result = target.compute(mockContext, null, config);
            // assert
            assertEquals(Integer.valueOf(10), result);
        }
    }

    /**
     * Test compute() when judgeDealShelfHasPage returns false and defaultNum is set to 0
     * Should return 0
     */
    @Test
    public void testComputeWhenNotHasPageZeroDefaultNum() {
        // arrange
        PageDealShelfShowNumOpt target = new PageDealShelfShowNumOpt();
        PageDealShelfShowNumOpt.Config config = new PageDealShelfShowNumOpt.Config();
        config.setDefaultNum(0);
        try (MockedStatic<ParamsUtil> mockedStatic = mockStatic(ParamsUtil.class)) {
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(any())).thenReturn(false);
            // act
            Integer result = target.compute(mockContext, null, config);
            // assert
            assertEquals(Integer.valueOf(0), result);
        }
    }

    /**
     * Test compute() when judgeDealShelfHasPage returns false and defaultNum is set to negative
     * Should return negative value
     */
    @Test
    public void testComputeWhenNotHasPageNegativeDefaultNum() {
        // arrange
        PageDealShelfShowNumOpt target = new PageDealShelfShowNumOpt();
        PageDealShelfShowNumOpt.Config config = new PageDealShelfShowNumOpt.Config();
        config.setDefaultNum(-1);
        try (MockedStatic<ParamsUtil> mockedStatic = mockStatic(ParamsUtil.class)) {
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(any())).thenReturn(false);
            // act
            Integer result = target.compute(mockContext, null, config);
            // assert
            assertEquals(Integer.valueOf(-1), result);
        }
    }

    /**
     * Test compute() when judgeDealShelfHasPage returns false with null context
     * Should return config.getDefaultNum()
     */
    @Test
    public void testComputeWhenNotHasPageNullContext() {
        // arrange
        PageDealShelfShowNumOpt target = new PageDealShelfShowNumOpt();
        PageDealShelfShowNumOpt.Config config = new PageDealShelfShowNumOpt.Config();
        config.setDefaultNum(4);
        try (MockedStatic<ParamsUtil> mockedStatic = mockStatic(ParamsUtil.class)) {
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(any())).thenReturn(false);
            // act
            Integer result = target.compute(null, null, config);
            // assert
            assertEquals(Integer.valueOf(4), result);
        }
    }

    /**
     * Test compute() when judgeDealShelfHasPage returns false with null config
     * Should return 4 (default value from Config class)
     */
    @Test
    public void testComputeWhenNotHasPageNullConfig() {
        // arrange
        PageDealShelfShowNumOpt target = new PageDealShelfShowNumOpt();
        try (MockedStatic<ParamsUtil> mockedStatic = mockStatic(ParamsUtil.class)) {
            mockedStatic.when(() -> ParamsUtil.judgeDealShelfHasPage(any())).thenReturn(false);
            // act
            Integer result = target.compute(mockContext, null, new PageDealShelfShowNumOpt.Config());
            // assert
            assertEquals(Integer.valueOf(4), result);
        }
    }
}
