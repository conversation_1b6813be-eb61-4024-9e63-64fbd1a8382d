package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SkuAttrIconOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuAttrIconOpt.Param param;

    @Mock
    private SkuAttrIconOpt.Config config;

    @Test
    public void testComputeWhenSkuItemDtoIsNull() {
        // arrange
        SkuAttrIconOpt skuAttrIconOpt = new SkuAttrIconOpt();
        when(param.getSkuItemDto()).thenReturn(null);
        // act
        String result = skuAttrIconOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenAttrItemsIsEmpty() {
        // arrange
        SkuAttrIconOpt skuAttrIconOpt = new SkuAttrIconOpt();
        SkuItemDto skuItemDto = new SkuItemDto();
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // act
        String result = skuAttrIconOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenNoAttrNameEqualsIconKey() {
        // arrange
        SkuAttrIconOpt skuAttrIconOpt = new SkuAttrIconOpt();
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Arrays.asList(new SkuAttrItemDto()));
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getIconKey()).thenReturn("iconKey");
        // act
        String result = skuAttrIconOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenAttrNameEqualsIconKey() {
        // arrange
        SkuAttrIconOpt skuAttrIconOpt = new SkuAttrIconOpt();
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto attrItem = new SkuAttrItemDto();
        attrItem.setAttrName("iconKey");
        attrItem.setAttrValue("attrValue");
        skuItemDto.setAttrItems(Arrays.asList(attrItem));
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getIconKey()).thenReturn("iconKey");
        // act
        String result = skuAttrIconOpt.compute(context, param, config);
        // assert
        assertEquals("attrValue", result);
    }
}
