package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import org.junit.Before;
import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class ModuleBuilderFactory_BuildModuleListTest {

    @InjectMocks
    private ModuleBuilderFactory moduleBuilderFactory;

    @Mock
    private Map<String, ModuleStrategy> strategyMap;

    @Mock
    private ModuleStrategy moduleStrategy;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailAssembleParam dealDetailAssembleParam;

    @Mock
    private ModuleItem moduleItem;

    @Mock
    private DealDetailModuleVO dealDetailModuleVO;

    private void setupCommonMocks() {
        when(moduleItem.getStrategyName()).thenReturn("strategyName");
        // Ensure config is not null
        when(moduleItem.getConfig()).thenReturn("config");
        when(strategyMap.containsKey("strategyName")).thenReturn(true);
        when(strategyMap.get("strategyName")).thenReturn(moduleStrategy);
        when(moduleStrategy.buildModelVO(activityCxt, dealDetailAssembleParam, "config")).thenReturn(dealDetailModuleVO);
    }

    @Test
    public void testBuildModuleListCustomNameEmptyModuleList() throws Throwable {
        List<DealDetailModuleVO> result = moduleBuilderFactory.buildModuleListCustomName(activityCxt, dealDetailAssembleParam, new ArrayList<>());
        assertEquals(0, result.size());
    }

    @Test
    public void testBuildModuleListCustomNameStrategyNotInMap() throws Throwable {
        List<ModuleItem> moduleItems = new ArrayList<>();
        moduleItems.add(moduleItem);
        List<DealDetailModuleVO> result = moduleBuilderFactory.buildModuleListCustomName(activityCxt, dealDetailAssembleParam, moduleItems);
        assertEquals(0, result.size());
    }

    @Test
    public void testBuildModuleListCustomNameBuildModelVONull() throws Throwable {
        List<ModuleItem> moduleItems = new ArrayList<>();
        moduleItems.add(moduleItem);
        List<DealDetailModuleVO> result = moduleBuilderFactory.buildModuleListCustomName(activityCxt, dealDetailAssembleParam, moduleItems);
        assertEquals(0, result.size());
    }

    @Test
    public void testBuildModuleListCustomNameDealDetailModuleVONameNotBlank() throws Throwable {
        setupCommonMocks();
        // Ensure name is not blank
        when(dealDetailModuleVO.getName()).thenReturn("name");
        List<ModuleItem> moduleItems = new ArrayList<>();
        moduleItems.add(moduleItem);
        List<DealDetailModuleVO> result = moduleBuilderFactory.buildModuleListCustomName(activityCxt, dealDetailAssembleParam, moduleItems);
        assertEquals(1, result.size());
        assertEquals("name", result.get(0).getName());
    }
}
