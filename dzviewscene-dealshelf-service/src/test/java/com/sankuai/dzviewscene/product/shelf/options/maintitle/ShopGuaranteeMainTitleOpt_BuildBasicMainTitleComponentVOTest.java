package com.sankuai.dzviewscene.product.shelf.options.maintitle;

import com.sankuai.dzviewscene.product.shelf.options.maintitle.ShopGuaranteeMainTitleOpt;
import com.sankuai.dzviewscene.product.shelf.options.maintitle.ShopGuaranteeMainTitleOpt.Config;
import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import com.dianping.vc.enums.VCPlatformEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopGuaranteeMainTitleOpt_BuildBasicMainTitleComponentVOTest {

    /**
     * Tests the scenario where the platform is MT and the mtTitleIcon in the config exists.
     */
    @Test
    public void testBuildBasicMainTitleComponentVO_MtTitleIconExists() throws Throwable {
        ShopGuaranteeMainTitleOpt shopGuaranteeMainTitleOpt = new ShopGuaranteeMainTitleOpt();
        Config config = new Config();
        config.setMtTitleIcon("mtTitleIcon");
        MainTitleComponentVO result = shopGuaranteeMainTitleOpt.buildBasicMainTitleComponentVO(VCPlatformEnum.MT.getType(), config);
        assertEquals("mtTitleIcon", result.getIcon());
    }

    /**
     * Tests the scenario where the platform is MT and the mtTitleIcon in the config does not exist.
     */
    @Test
    public void testBuildBasicMainTitleComponentVO_MtTitleIconNotExists() throws Throwable {
        ShopGuaranteeMainTitleOpt shopGuaranteeMainTitleOpt = new ShopGuaranteeMainTitleOpt();
        Config config = new Config();
        // Explicitly set to null to simulate "not exists" scenario
        config.setMtTitleIcon(null);
        MainTitleComponentVO result = shopGuaranteeMainTitleOpt.buildBasicMainTitleComponentVO(VCPlatformEnum.MT.getType(), config);
        assertNull(result.getIcon());
    }

    /**
     * Tests the scenario where the platform is not MT and the dpTitleIcon in the config exists.
     */
    @Test
    public void testBuildBasicMainTitleComponentVO_DpTitleIconExists() throws Throwable {
        ShopGuaranteeMainTitleOpt shopGuaranteeMainTitleOpt = new ShopGuaranteeMainTitleOpt();
        Config config = new Config();
        config.setDpTitleIcon("dpTitleIcon");
        MainTitleComponentVO result = shopGuaranteeMainTitleOpt.buildBasicMainTitleComponentVO(VCPlatformEnum.DP.getType(), config);
        assertEquals("dpTitleIcon", result.getIcon());
    }

    /**
     * Tests the scenario where the platform is not MT and the dpTitleIcon in the config does not exist.
     */
    @Test
    public void testBuildBasicMainTitleComponentVO_DpTitleIconNotExists() throws Throwable {
        ShopGuaranteeMainTitleOpt shopGuaranteeMainTitleOpt = new ShopGuaranteeMainTitleOpt();
        Config config = new Config();
        // Explicitly set to null to simulate "not exists" scenario
        config.setDpTitleIcon(null);
        MainTitleComponentVO result = shopGuaranteeMainTitleOpt.buildBasicMainTitleComponentVO(VCPlatformEnum.DP.getType(), config);
        assertNull(result.getIcon());
    }

    /**
     * Tests the scenario where the config is null.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildBasicMainTitleComponentVO_ConfigIsNull() throws Throwable {
        ShopGuaranteeMainTitleOpt shopGuaranteeMainTitleOpt = new ShopGuaranteeMainTitleOpt();
        shopGuaranteeMainTitleOpt.buildBasicMainTitleComponentVO(VCPlatformEnum.MT.getType(), null);
    }
}
