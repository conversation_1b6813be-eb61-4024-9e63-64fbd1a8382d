package com.sankuai.dzviewscene.product.shelf.ability.query;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductQueryFetcherTest {

    @InjectMocks
    private ProductQueryFetcher productQueryFetcher;

    @Mock
    private MultiGroupMergeQueryFetcher multiGroupMergeQueryFetcher;

    private ActivityCxt activityCxt;

    private ProductQueryParam mergeQueryRequest;

    private ProductQueryCfg mergeQueryConfig;

    @Before
    public void setUp() {
        activityCxt = mock(ActivityCxt.class);
        mergeQueryRequest = mock(ProductQueryParam.class);
        mergeQueryConfig = mock(ProductQueryCfg.class);
    }

    /**
     * 测试build方法正常情况
     */
    @Test
    public void testBuildNormal() throws Throwable {
        // arrange
        when(mergeQueryConfig.getGroupNames()).thenReturn(Collections.singletonList("test"));
        when(mergeQueryConfig.getGroupParams()).thenReturn(new HashMap<>());
        when(multiGroupMergeQueryFetcher.build(any())).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<Map<String, ProductGroupM>> result = productQueryFetcher.build(activityCxt, mergeQueryRequest, mergeQueryConfig);
        // assert
        verify(activityCxt).addParam("groupNames", Collections.singletonList("test"));
        verify(activityCxt).addParam("groupParams", new HashMap<>());
        verify(multiGroupMergeQueryFetcher).build(any());
        assertNotNull(result);
    }

    /**
     * 测试build方法异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testBuildException() throws Throwable {
        // arrange
        when(mergeQueryConfig.getGroupNames()).thenReturn(Collections.singletonList("test"));
        when(mergeQueryConfig.getGroupParams()).thenReturn(new HashMap<>());
        when(multiGroupMergeQueryFetcher.build(any())).thenThrow(new RuntimeException());
        // act
        productQueryFetcher.build(activityCxt, mergeQueryRequest, mergeQueryConfig);
    }
}
