package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class MarketingActivityStrategy_BuildTagTest {

    @InjectMocks
    private MarketingActivityStrategy marketingActivityStrategy;

    @Mock
    private FloatTagBuildReq param;

    @Mock
    private FloatTagBuildCfg config;

    @Mock
    private ProductM productM;

    @Mock
    private ProductActivityM productActivityM;

    private MockedStatic<PromoSimplifyUtils> mockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockedStatic = Mockito.mockStatic(PromoSimplifyUtils.class);
    }

    @After
    public void tearDown() {
        mockedStatic.close();
    }

    @Test
    public void testBuildTagWhenProductActivitiesIsNull() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(null);
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        assertNull(result);
    }

    @Test
    public void testBuildTagWhenActivityMIsNull() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        List<ProductActivityM> productActivityMS = new ArrayList<>();
        when(productM.getActivities()).thenReturn(productActivityMS);
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        assertNull(result);
    }

    @Test
    public void testBuildTagWhenActivityPicUrlMapIsNull() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        List<ProductActivityM> productActivityMS = new ArrayList<>();
        productActivityMS.add(productActivityM);
        when(productM.getActivities()).thenReturn(productActivityMS);
        when(productActivityM.getActivityPicUrlMap()).thenReturn(null);
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        assertNull(result);
    }

    @Test
    public void testBuildTagWhenShelfShowTypeEnumIsNull() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        List<ProductActivityM> productActivityMS = new ArrayList<>();
        productActivityMS.add(productActivityM);
        when(productM.getActivities()).thenReturn(productActivityMS);
        when(productActivityM.getActivityPicUrlMap()).thenReturn(new HashMap<>());
        when(param.getShelfShowType()).thenReturn(0);
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        assertNull(result);
    }

    @Test
    public void testBuildTagWhenShowTypeEnumIsOther() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        List<ProductActivityM> productActivityMS = new ArrayList<>();
        productActivityMS.add(productActivityM);
        when(productM.getActivities()).thenReturn(productActivityMS);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        ActivityPicUrlDTO activityPicUrlDTO = new ActivityPicUrlDTO();
        activityPicUrlDTO.setHeight(100);
        activityPicUrlMap.put("key", activityPicUrlDTO);
        when(productActivityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        when(param.getShelfShowType()).thenReturn(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType());
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        assertNull(result);
    }
}
