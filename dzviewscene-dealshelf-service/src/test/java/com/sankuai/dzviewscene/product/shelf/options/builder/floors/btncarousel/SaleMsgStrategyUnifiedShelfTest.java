package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import com.sankuai.dzviewscene.product.unifiedshelf.enums.CarouselMsgTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.CarouselMsg;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemCarouselMsgVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class SaleMsgStrategyUnifiedShelfTest {

    private SaleMsgStrategy strategy;

    private ProductM productM;

    private CarouselBuilderContext context;

    @Before
    public void setUp() {
        strategy = new SaleMsgStrategy();
        productM = Mockito.mock(ProductM.class);
        context = Mockito.mock(CarouselBuilderContext.class);
    }

    /**
     * 测试场景：当Param中的ProductM为null时，应返回null
     */
    @Test
    public void testBuild_WhenProductMIsNull() {
        Mockito.when(context.getProductM()).thenReturn(null);
        CarouselMsg result = strategy.unifiedBuild(context);
        Assert.assertNull(result);
    }

    /**
     * 测试场景：当ProductM中的Sale为null时，应返回null
     */
    @Test
    public void testBuild_WhenSaleIsNull() {
        Mockito.when(context.getProductM()).thenReturn(productM);
        CarouselMsg result = strategy.unifiedBuild(context);
        Assert.assertNull(result);
    }

    /**
     * 测试场景：当Sale中的SaleTag为空时，应返回null
     */
    @Test
    public void testBuild_WhenSaleTagIsEmpty() {
        Mockito.when(context.getProductM()).thenReturn(productM);
        ProductM productM = Mockito.mock(ProductM.class);
        ProductSaleM sale = Mockito.mock(ProductSaleM.class);
        Mockito.when(productM.getSale()).thenReturn(sale);
        Mockito.when(sale.getSaleTag()).thenReturn("");
        CarouselMsg result = strategy.unifiedBuild(context);
        Assert.assertNull(result);
    }

    /**
     * 测试场景：当Sale中的SaleTag不为空时，应返回正确的CarouselMsg
     */
    @Test
    public void testBuild_WhenSaleTagIsNotEmpty() {
        Mockito.when(context.getProductM()).thenReturn(productM);
        ProductM productM = Mockito.mock(ProductM.class);
        ProductSaleM sale = Mockito.mock(ProductSaleM.class);
        Mockito.when(productM.getSale()).thenReturn(sale);
        Mockito.when(sale.getSaleTag()).thenReturn("SaleTag");
        CarouselMsg result = strategy.unifiedBuild(context);

        Assert.assertNull(result);
    }

}
