package com.sankuai.debug.util;

import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.pmf.resource.cfg.ResourceCfg;
import org.junit.Ignore;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * created by zhangzhiyuan04 in 2021/8/23
 */
@Ignore("没有可执行的方法")
public class ResourceTest {

    //@Test
    public void test() {
        String json = "{\"id\":\"00001\",\"abilityNodes\":[{\"code\":\"shopActivityFetcher\",\"dependencies\":null,\"strategy\":\"\",\"extPoints\":[]},{\"code\":\"productMergeQueryFetcher\",\"dependencies\":null,\"strategy\":\"{\\\\\\\"groupNames\\\\\\\":[\\\\\\\"deal\\\\\\\"],\\\\\\\"groupParams\\\\\\\":{\\\\\\\"deal\\\\\\\":{\\\\\\\"paddingType\\\\\\\":\\\\\\\"dealThemePadding\\\\\\\",\\\\\\\"rankId\\\\\\\":\\\\\\\"10100007\\\\\\\",\\\\\\\"planId\\\\\\\":\\\\\\\"10002156\\\\\\\",\\\\\\\"attributeKeys\\\\\\\":[\\\\\\\"sys_deal_universal_type\\\\\\\"],\\\\\\\"queryType\\\\\\\":\\\\\\\"shopOnLineDealsQuery\\\\\\\"}}}\",\"extPoints\":[]},{\"code\":\"shelfProductListBuilder\",\"dependencies\":[\"productMergeQueryFetcher\",\"shopActivityFetcher\"],\"strategy\":\"\",\"extPoints\":[]},{\"code\":\"mainTitleBuilder\",\"dependencies\":[\"productMergeQueryFetcher\",\"shopActivityFetcher\"],\"strategy\":\"\",\"extPoints\":[{\"code\":\"MainTitleContentVP\",\"strategy\":\"{\\\\\\\"dpTitle\\\\\\\":\\\\\\\"\\\\\\\\u5356\\\\\\\\u573a\\\\\\\\u6d3b\\\\\\\\u52a8\\\\\\\",\\\\\\\"mtTitle\\\\\\\":\\\\\\\"\\\\\\\\u5356\\\\\\\\u573a\\\\\\\\u6d3b\\\\\\\\u52a8\\\\\\\",\\\\\\\"dpTitleIcon\\\\\\\":\\\\\\\"https://p0.meituan.net/dprainbow/0021fb6ae48dd8124d66c7e3aa8ee5d91382.png\\\\\\\",\\\\\\\"mtTitleIcon\\\\\\\":\\\\\\\"https://p0.meituan.net/dprainbow/f60a400ebac539bbc2181b83b10d20241730.png\\\\\\\",\\\\\\\"titleTagIcon\\\\\\\":\\\\\\\"https://p0.meituan.net/dprainbow/eb0dd38ce2ef531801c7282e7995bfe71262.png\\\\\\\"}\",\"route\":{\"type\":\"static\",\"rule\":\"ConfigMainTitleOption\"}}]},{\"code\":\"productShelfOceanBuilder\",\"dependencies\":[],\"strategy\":\"{ \\\\\\\"dpOcean\\\\\\\": { \\\\\\\"wholeShelf\\\\\\\": { \\\\\\\"bidClick\\\\\\\": \\\\\\\"\\\\\\\", \\\\\\\"bidView\\\\\\\": \\\\\\\"b_house_bgnu7hpy_mv\\\\\\\", \\\\\\\"category\\\\\\\": \\\\\\\"house\\\\\\\" }, \\\\\\\"filterBar\\\\\\\": { \\\\\\\"bidClick\\\\\\\": \\\\\\\"b_house_yh2g4duv_mc\\\\\\\", \\\\\\\"bidView\\\\\\\": \\\\\\\"b_house_yh2g4duv_mv\\\\\\\", \\\\\\\"category\\\\\\\": \\\\\\\"house\\\\\\\" }, \\\\\\\"more\\\\\\\": { \\\\\\\"bidClick\\\\\\\": \\\\\\\"b_house_x3scxsf3_mc\\\\\\\", \\\\\\\"bidView\\\\\\\": \\\\\\\"b_house_x3scxsf3_mv\\\\\\\", \\\\\\\"category\\\\\\\": \\\\\\\"house\\\\\\\" }, \\\\\\\"productItem\\\\\\\": { \\\\\\\"bidClick\\\\\\\": \\\\\\\"b_house_has7o0jh_mc\\\\\\\", \\\\\\\"bidView\\\\\\\": \\\\\\\"b_house_has7o0jh_mv\\\\\\\", \\\\\\\"category\\\\\\\": \\\\\\\"house\\\\\\\" } }, \\\\\\\"mtOcean\\\\\\\": { \\\\\\\"wholeShelf\\\\\\\": { \\\\\\\"bidClick\\\\\\\": \\\\\\\"\\\\\\\", \\\\\\\"bidView\\\\\\\": \\\\\\\"b_house_bgnu7hpy_mv\\\\\\\", \\\\\\\"category\\\\\\\": \\\\\\\"house\\\\\\\" }, \\\\\\\"filterBar\\\\\\\": { \\\\\\\"bidClick\\\\\\\": \\\\\\\"b_house_yh2g4duv_mc\\\\\\\", \\\\\\\"bidView\\\\\\\": \\\\\\\"b_house_yh2g4duv_mv\\\\\\\", \\\\\\\"category\\\\\\\": \\\\\\\"house\\\\\\\" }, \\\\\\\"more\\\\\\\": { \\\\\\\"bidClick\\\\\\\": \\\\\\\"b_house_x3scxsf3_mc\\\\\\\", \\\\\\\"bidView\\\\\\\": \\\\\\\"b_house_x3scxsf3_mv\\\\\\\", \\\\\\\"category\\\\\\\": \\\\\\\"house\\\\\\\" }, \\\\\\\"productItem\\\\\\\": { \\\\\\\"bidClick\\\\\\\": \\\\\\\"b_house_has7o0jh_mc\\\\\\\", \\\\\\\"bidView\\\\\\\": \\\\\\\"b_house_has7o0jh_mv\\\\\\\", \\\\\\\"category\\\\\\\": \\\\\\\"house\\\\\\\" } } }\",\"extPoints\":[{\"code\":\"ProductShelfOceanVP\",\"strategy\":\"{\\\\\\\"categoryId\\\\\\\":true,\\\\\\\"poiId\\\\\\\":true,\\\\\\\"fields\\\\\\\":[\\\\\\\"more\\\\\\\",\\\\\\\"filterBar\\\\\\\",\\\\\\\"wholeShelf\\\\\\\"]}\",\"route\":{\"type\":\"static\",\"rule\":\"ProductShelfOceanOpt\"}},{\"code\":\"PopLabOceanVP\",\"strategy\":\"{\\\\\\\"fields\\\\\\\":[\\\\\\\"promoPopView\\\\\\\",\\\\\\\"promoPopViewButton\\\\\\\",\\\\\\\"promoPopViewClose\\\\\\\"]}\",\"route\":{\"type\":\"static\",\"rule\":\"DefaultPopLabOceanOpt\"}}]},{\"code\":\"shelfResponseBuilder\",\"dependencies\":[\"shelfProductListBuilder\",\"mainTitleBuilder\",\"productShelfOceanBuilder\"],\"strategy\":\"{\\\\\\\"showType\\\\\\\" : 11}\",\"extPoints\":[]}]}";

        Object parse = JSON.parse(json);

        ResourceCfg resource = JsonCodec.decode(json, ResourceCfg.class);
        System.out.println(1);
    }

    public static class Config{

    }
}
