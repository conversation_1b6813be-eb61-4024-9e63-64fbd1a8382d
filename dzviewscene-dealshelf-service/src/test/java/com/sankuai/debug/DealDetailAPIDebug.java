package com.sankuai.debug;

import com.dianping.beauty.zone.common.adapter.ItemSearchAdapter;
import com.dianping.beauty.zone.common.parse.ItemDefaultParser;
import com.dianping.beauty.zone.common.parse.SingleParserFactory;
import com.dianping.beauty.zone.common.parse.reader.AnnotationReaderFactory;
import com.dianping.squirrel.client.impl.redis.RedisStoreClientImpl;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivityCtxBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.DefaultActivityEngine;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivity;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;
import java.util.*;

@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {
        "com.sankuai.dzviewscene",
        "com.dianping.beauty.zone.common.parse.reader",
        "com.sankuai.athena.viewscene.framework"})
public class DealDetailAPIDebug {


    static {
        TestBeanFactory.registerBean("activityEngine", DefaultActivityEngine.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
        TestBeanFactory.registerBean("ShopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.registerBean("itemSearchAdapter", ItemSearchAdapter.class);
        TestBeanFactory.registerBean("itemDefaultParser", ItemDefaultParser.class);
        TestBeanFactory.registerBean("singleParserFactory", SingleParserFactory.class);
        TestBeanFactory.registerBean("annotationReaderFactory", AnnotationReaderFactory.class);
        TestBeanFactory.registerBean("redisStoreClient", RedisStoreClientImpl.class);
    }

    @Resource
    private ActivityEngine activityEngine;

    @Environment(value = AthenaEnv.Product)
    //@Test
    public void test_() {
        // prod 771858873 test 412458345
        for (int i = 0; i < 100; i++) {
            ActivityRequest activityRequest = buildNewDetailActivityRequest("", 771858873, 100);
            activityRequest.addParam("spaceKey", "deal_detail_standard_service");
            activityRequest.setActivityCode(ProductDetailActivity.ACTIVITY_PRODUCT_DETAIL_CODE);
            com.sankuai.dzviewscene.shelf.framework.ActivityResponse<Object> response  = activityEngine.execute(activityRequest);
            DealModuleDetailVO build = (DealModuleDetailVO) response.getResult().join();
            System.out.println(1);
        }
    }

    @Environment(value = AthenaEnv.Product)
    //@Test
    public void test_1() {
        // prod 771858873 test 412458345
        for (int i = 0; i < 100; i++) {
            ActivityRequest activityRequest = buildNewDetailActivityRequest("", 771858873, 100);
            activityRequest.setActivityCode(ProductDetailActivity.ACTIVITY_PRODUCT_DETAIL_CODE);
            com.sankuai.dzviewscene.shelf.framework.ActivityResponse<Object> response  = activityEngine.execute(activityRequest);
            DealModuleDetailVO build = (DealModuleDetailVO) response.getResult().join();
            System.out.println(1);
        }
    }

    private ActivityRequest buildNewDetailActivityRequest(String sceneCode, int productId, int platform) {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ProductDetailActivity.ACTIVITY_PRODUCT_DETAIL_CODE);
        paddingParams(activityRequest.getParams(), sceneCode, productId, platform);
        activityRequest.addParam("spaceKey", DealDetailActivityCtxBuilder.DEAL_DETAIL_SPACE_KEY);
        return activityRequest;
    }

    private void paddingParams(Map<String, Object> params, String sceneCode, int productId, int platform) {
        params.put(ProductDetailActivityConstants.Params.platform, platform);
        params.put(ProductDetailActivityConstants.Params.userAgent, 100);
        params.put(ProductDetailActivityConstants.Params.productId, productId);
        params.put(ProductDetailActivityConstants.Params.traceMark, "1");
        params.put(QueryFetcher.Params.groupName2ProductIds, new HashMap<String, List<Integer>>() {{
            put("product", Arrays.asList(412458345));
        }});
        params.put(ProductDetailActivityConstants.Params.sceneCode, sceneCode);

        //////////////////////平台相关参数//////////////////////
        if (PlatformUtil.isMT(platform)) {
            params.put(ProductDetailActivityConstants.Params.mtCityId, 10);
        } else {
            params.put(ProductDetailActivityConstants.Params.dpCityId, 1);
        }
    }
}
