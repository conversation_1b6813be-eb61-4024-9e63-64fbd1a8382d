package com.sankuai.qpro.ai.professor;

import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf.CPVModel;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.CpvServiceProxy;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Slf4j
@SpringBootTest
public class CpvServiceProxyTests {

    @Autowired
    private CpvServiceProxy cpvServiceProxy;

    @Test
    public void getCpvInfoTest() {
        Map<String, CPVModel> result = cpvServiceProxy.getCpvInfo(84101019l);
        log.info("result:{}", SerializeUtils.toJsonStr(result));
    }

    /**
     * 测试获取 CPV 父子属性信息
     */
    @Test
    public void getCpvInfoTest2() {
        // 父子属性的categoryId，那个苹果品牌和机型的属性
        long productCategoryId = 84294l;
        Map<String, CPVModel> result = cpvServiceProxy.getCpvInfo(productCategoryId);
        log.info("result:{}", SerializeUtils.toJsonStr(result));
    }
}
