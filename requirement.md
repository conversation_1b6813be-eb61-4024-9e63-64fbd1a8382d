结构化提取如下：

# 2.2 双眼部分 - C端需求

## 2.2.1 质保信息新增单位属性

- 线上逻辑：当前质保属性均为数值，且固定以年为单位，需新增时间单位属性。
- 当前质保属性：
    - 镜片质保（key: lens_assure，数值，非必填）
    - 镜框质保（key: frame_warranty，数值，非必填）
    - 产品质保（key: product_warranty，数值，非必填）
- 展示规则：
    - 展示“镜片[lens_assure][LensQualityAssuranceUnit]质保，镜框[frame_warranty][FrameQualityUnit]质保”
        - 例：展示【镜片X天质保，镜框Y天质保】
        - 当[LensQualityAssuranceUnit]或[FrameQualityUnit]字段为空时，默认展示为“年”
        - 当[lens_assure]或[frame_warranty]字段为空时，售后质保信息不展示
    - 展示“[product_warranty][ProductQualityAssuranceUnit]年质保”
        - 例：展示【Z天质保】
        - 当[ProductQualityAssuranceUnit]字段为空时，默认展示为“年”
        - 当[product_warranty]字段为空时，售后质保信息不展示
- 应用类目：
    - 镜片/镜框质保：眼镜-近视配镜、儿童配镜、老花眼镜、仅镜框；眼科-儿童普通眼镜、医学配镜、离焦镜
    - 产品质保：眼镜-仅镜片、太阳眼镜、仅镜框、隐形眼镜；眼科-OK镜、离焦软镜、RGP镜、其他接触镜

---

## 2.2.2 取镜时间类型新增

- 当前逻辑：
    - 属性：取镜时间（acquire_time，文本，必填），指定天数（specify_days，数值，必填）
    - 展示：
        - acquire_time=“立等可取”时，展示[acquire_time]
        - acquire_time=“指定天数后可取”时，展示“[specify_days]天后可取”
- 新增逻辑：
    - acquire_time=“指定天数内可取”时，展示“[specify_days2]天内可取”
    - acquire_time=“指定天数范围可取”时，展示“[SpecifyDaysRangeMinValue] - [SpecifyDaysRangeMaxValue]天可取”
- 应用类目：
    - 眼镜-近视配镜、儿童配镜、仅镜片、老花眼镜、太阳眼镜、仅镜框、隐形眼镜
    - 眼科-儿童普通眼镜、医学配镜、离焦镜、OK镜、离焦软镜、RGP镜、其他接触镜

---

## 2.2.3 标题拼接逻辑更新

- 当前采用模式3（固定前拼接），需调整为模式2（推荐前拼接）：
    - 编辑团单选择属性后，自动前拼接，商家可点击“不使用推荐名称”，进入自定义输入状态
    - 不展示“推荐名称生成规则”入口
    - 固定拼接时，拼接分隔符“|”前后加半角空格“ ”

---

## 2.2.4 眼镜行业配镜类目补充度数/折射率说明入口及浮层

- 场景：
    - 配镜相关类目中补充度数/折射率关系说明入口及浮层
    - 类目为近视配镜、儿童配镜、老花眼镜时，有区分镜片、镜框表单，入口展示在“镜片”标题行最右侧
    - 类目为仅镜片时，入口展示在团单类目标题行最右侧
- 埋点信息：
    - 科普入口mv：b_gc_8ovsv7sd_mv
    - 科普入口mc：b_gc_8ovsv7sd_mc
    - 配镜科普浮层mv：b_gc_ps2x0ddk_mv
- 应用类目：眼镜-近视配镜、儿童配镜、仅镜片、老花眼镜

---

## 2.2.5 眼镜行业配镜类目-镜片技术字段支持多选

- 当前逻辑：单选
- 新逻辑：
    - 当lens_technology2为单选时，展示[lens_technology2]
    - 当lens_technology2为多选（数组）时，以“、”区隔展示，如“双非球面、多点近视离焦眼轴控制技术”
- 应用类目：眼镜-近视配镜、儿童配镜、仅镜片、老花眼镜、太阳眼镜

---

## 2.2.6 BUG FIX

### 2.2.6.1 眼科-健康整体评估-商家说明
- 健康检查模块商家说明未填写时，C端不再展示默认文案。

---

## 2.2.7 眼镜-验光操作人员调整为可多选

- 当前：单选，展示为[optometrist+（optometrist_experience）]
- 新逻辑：
    - 若optometrist2为单选，展示[optometrist2+（从业optometrist_experience）]；若optometrist_experience为空，只展示[optometrist2]
    - 若optometrist2为多选（数组），以“/”区隔展示+“可选”，不展示optometrist_experience
        - 如：中级验光员/高级验光员可选
        - 团单关联在线门店数大于1且多选时，文案最后方补充提示：“该团购多门店可用，实际验光操作人员可联系商家确认”
- 应用类目：眼镜-验光、儿童验光、近视配镜、儿童配镜、仅镜片、老花眼镜

---

## 2.2.8 眼科-验光操作人员调整为可多选

- 当前：单选，根据InspectOptometryProcedure字段内容分别展示PhysicianSelection/NurseSelection/OptometristSelection
- 新逻辑：
    - 展示OperatorsSelection数组内容
        - 多选时以“/”区隔+“可选”
        - 团单关联在线门店数大于1且多选时补充提示：“该团购多门店可用，实际验光操作人员可联系商家确认”
- 应用类目：眼科-儿童医学验光、角膜接触镜配镜检查、成人医学验光、儿童普通眼镜、医学配镜、离焦镜、OK镜、离焦软镜、RGP镜、其他接触镜、视觉训练

---

## 2.2.9 眼科-指定类目下的检查项目移除必选逻辑

- 指定类目下的部分项目取消置灰（保留默认选中），如：
    - 儿童医学验光、成人医学验光等的视力检查/电脑验光/综合验光项目
    - 儿童普通眼镜等的服务流程部分项目
- 应用类目详见原文表述

---

## 2.2.10 眼科-检查时长调整为范围值

- 新逻辑：
    - 检查时长展示范围，如10-20分钟（两个字段有值且max>min），否则展示单一值
    - 检查项目总时间按平均值计入总时长；未能整除的四舍五入
- 应用类目：
    - 视力检查：儿童医学验光、成人医学验光、近视术前检查
    - 眼科检查：儿童医学验光、成人医学验光、近视术前检查、干眼检查
    - 健康评估：近视术前检查

---

## 2.2.11 眼科-离焦镜类目翻单

- 团单标题含[离焦]或指定产品词的团单自动翻单到新类目，并按规则映射属性KEY。
- 明细规则详见原文表格。

---

## 2.2.12 眼科-错误字段翻单

- OK镜、离焦软镜、RGP镜3个类目的【产品质保】属性KEY由Product_warranty调整为product_warranty，并清洗线上数据。

---

# 2.3 口腔部分 - C端需求

## 2.3.1 补牙、儿童补牙科普信息补充

- 在补牙和儿童补牙类目下，“材料型号MaterialModel”字段属性值后增加关于3M材料型号的科普信息入口（小箭头点开）。
- 判定条件：Technique=树脂补牙 且 MaterialBrand=3M 时出现。
- 不同MaterialModel对应不同图片和链接。
- 埋点：重点展示信息-明细解释入口：b_gc_o2xm8cqh_mc

---

## 2.3.2 口腔-服务时长调整为范围逻辑

- 服务时长（duration）展示方式调整：
    - duration及durationRangeMax都有填写且max＞min，则展示[min]-[max]分钟
    - 若只有一个有值，则只展示有值的那一个
    - 检查项目总时间按平均值计入总时长（单位一致才计算），否则不计算总时长
- 无duration的服务流程不计入总时长
- 指定服务流程不需要展示服务时长填写框（如术后护理指导等）

---

## 2.3.3 口腔-套餐包含新增属性（仅部分类目）

- 字段PackageIncludes（数组，非必填，可多选），未选择则C端不展示。
- 属性值枚举：
    - “牙周治疗”：含洗牙1次、含局部麻醉
    - “窝沟封闭”：含涂氟1次
- 应用位置：重点展示信息模块

---

## 2.3.5 口腔-服务时长数据清洗

- 指定服务流程（如术后护理指导等）不需要服务时长duration数据，对应线上数据需清空。

---

以上内容已全面覆盖2.2和2.3部分所有C端需求。