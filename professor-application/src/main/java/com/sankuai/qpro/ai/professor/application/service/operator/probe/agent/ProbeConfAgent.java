package com.sankuai.qpro.ai.professor.application.service.operator.probe.agent;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.langmodel.api.annotation.MdpLangModelFunction;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.SystemMessage;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel;
import com.sankuai.qpro.ai.professor.api.enums.ConfigUnitEnum;
import com.sankuai.qpro.ai.professor.api.enums.SegmentEnum;
import com.sankuai.qpro.ai.professor.application.constants.ToolTypeConstant;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.extract.ToolParamExractModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.generate.NaturalLanguageGenerateModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.exception.model.timeout.TimeoutModelException;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf.*;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.agent.conf.param.CheckPVValidParam;
import com.sankuai.qpro.ai.professor.application.model.operator.probe.chat.AgentOutputModel;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeAgentPrompt;
import com.sankuai.qpro.ai.professor.application.prompt.ProbeOutputPrompt;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationParamService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.chat.ConversationStoreService;
import com.sankuai.qpro.ai.professor.application.service.operator.probe.proxy.CpvServiceProxy;
import com.sankuai.qpro.ai.professor.application.utils.LLMExceptionUtil;
import com.sankuai.qpro.ai.professor.application.utils.RetryExecuteUtils;
import com.sankuai.qpro.ai.professor.application.utils.SerializeUtils;
import com.sankuai.qpro.ai.professor.entity.ConversationParamEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Slf4j
@Component
public class ProbeConfAgent {

    @Autowired
    private ConversationStoreService conversationStoreService;

    @Autowired
    private ProbeAgentPrompt agentPrompt;

    @Autowired
    private ProbeOutputPrompt probeOutputPrompt;

    @Autowired
    private CpvServiceProxy cpvServiceProxy;

    @Autowired
    @Qualifier("gpt4oModel")
    private OpenAIChatModel gpt4oModel;

    @Autowired
    private ConversationParamService conversationParamService;

    @MdpLangModelFunction(description = "斗斛实验之后的属性配置问题，用户描述中包括基于线上逻辑修改和删除线上逻辑重新配置，以及具体配置的详情包括配置点位、点位下具体的属性及其展示逻辑，都应该由我来处理", type = ToolTypeConstant.PROBE, returnDirect = true)
    public AgentOutputModel generateConfigTips(ProbeConfParam param) {
        log.info("[ProbeConfAgent] generateConfigTips param = {}", SerializeUtils.toJsonStr(param));

        // 参数校验
        if (param.getConversationId() == null) {
            throw new ToolParamExractModelException(SegmentEnum.CONF.getCode() + "阶段，generateConfigTips conversionId 提取为空");
        }
        ConversationParamEntity paramEntity = conversationParamService.getConversationParamEntity(param.getConversationId(), SegmentEnum.CONF);
        AgentOutputModel validateAgentOutputModel = AgentOutputModel.from(paramEntity.getStage(), SegmentEnum.CONF, probeOutputPrompt);
        if (validateAgentOutputModel != null) {
            return validateAgentOutputModel;
        }

        param = conversationParamService.getProbeConfParam(param);
        log.info("[ProbeConfAgent] generateConfigTips 配置提示问题处理开始， param = {}", SerializeUtils.toJsonStr(param));

        String msg = generateTaskListOfConfigTips(param);
        return AgentOutputModel.from(msg, SegmentEnum.CONF);
    }

    /**
     * 生成配置引导任务列表
     */
    public String generateTaskListOfConfigTips(ProbeConfParam param) {
        String configRule = getConfigRule(param.getConfigUnit());

        List<Message> historyMsgs = getHistoryMsgs(param.getConversationId());
        UserMessage lastUserMessage = (UserMessage) historyMsgs.get(historyMsgs.size() - 1);

        // 检查PV是否有效，如果无效则返回错误信息
        PVCheckResult pvCheckResult = checkPVValid(CheckPVValidParam.of(param.getProductCategoryId(), lastUserMessage.getContent(), param.getConversationId()));
        if (pvCheckResult != null && !pvCheckResult.isValid()) {
            return pvCheckResult.getMsg();
        }

        // 生成配置引导任务列表的提示信息
        String generateTaskListPrompt = String.format(agentPrompt.PROBE_CONF_PROMPT, configRule);
        List<Message> msgList = Lists.newArrayList(SystemMessage.from(generateTaskListPrompt));
        msgList.addAll(conversationStoreService.queryHistoryMessagesForConfigSegment(param.getConversationId()));
        msgList.add(UserMessage.from("点位和属性检查结果是合法的，可以继续下一个问题"));
        AssistantMessage assistantMessage = RetryExecuteUtils.retryModelTimeoutExecute(() -> gpt4oModel.sendMessages(msgList), 3);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeConfAgent] generateTaskListOfConfigTips 生成配置引导任务列表异常, param={}, error={}", SerializeUtils.toJsonStr(param), modelException);
            if (modelException instanceof SocketTimeoutException) {
                throw new TimeoutModelException("属性配置引导模型生成超时，请重试");
            }
            throw new NaturalLanguageGenerateModelException("属性配置引导模型生成异常");
        }

        return assistantMessage.getContent();
    }

    public PVCheckResult checkPVValid(CheckPVValidParam param) {
        log.info("[ProbeConfAgent] checkPVValid param={}", SerializeUtils.toJsonStr(param));
        String example = "\n\n>eg.[特色艾灸] 点位，属性艾灸手法serviceTechnique，value1=盒灸，展示1=用盒灸；value2=仪器灸，展示2=用仪器灸；value3=铺灸，展示3=铺灸；value4=手工悬灸，展示4=手工灸照";

        // 1. 类目不能为空
        if (param.getProductCategoryId() == null) {
            return PVCheckResult.of(false, "检查不通过，团购分类ID不能为空");
        }

        // 2. 结构化提取失败，提示用户按照结构化的方式来描述
        List<Message> msgList = Lists.newArrayList(SystemMessage.from(agentPrompt.PROBE_CONF_STRUCTURE_PROMPT), UserMessage.from(param.getConfStr()));
        AssistantMessage assistantMessage = RetryExecuteUtils.retryModelTimeoutExecute(() -> gpt4oModel.sendMessages(msgList), 3);
        if (!LLMExceptionUtil.validateMsg(assistantMessage)) {
            Exception modelException = LLMExceptionUtil.extractModelException(assistantMessage);
            log.error("[ProbeConfAgent] checkPVValid 提取结构化信息失败, param={}, error={}", SerializeUtils.toJsonStr(param), modelException);
            return PVCheckResult.of(false, String.format("检查不通过，根据你的描述无法提取结构化的信息，输入示例：\n\n%s", example));
        }
        PPVDList ppvdList = SerializeUtils.toObj(assistantMessage.getContent(), PPVDList.class);
        if (ppvdList == null || CollectionUtils.isEmpty(ppvdList.getPositionList())) {
            log.error("[ProbeConfAgent] checkPVValid 提取结构化信息失败，用户消息={}, 提取的结构化信息={}, 会话id={}", param.getConfStr(), SerializeUtils.toJsonStr(assistantMessage.getContent()), param.getConversationId());
            return PVCheckResult.of(false, String.format("检查不通过，根据你的描述无法提取结构化的信息，输入示例：\n\n%s", example));
        }
        log.info("[ProbeConfAgent] checkPVValid 用户消息={}，提取的结构化信息={}，会话id={}", param.getConfStr(), SerializeUtils.toJsonStr(ppvdList), param.getConversationId());

        // 3. 类目是否有效
        Map<String, CPVModel> cpvModelMap = Maps.newHashMap();
        int times = 0;
        while (times < 3) {
            try {
                cpvModelMap = cpvServiceProxy.getCpvInfo(param.getProductCategoryId());
                break;
            } catch (Exception e) {
                times++;
                log.error("[ProbeConfAgent] checkPVValid 查询CPV-PRC接口失败，团购分类ID={}, 会话id={}, 准备第{}次重试",
                        param.getProductCategoryId(), param.getConversationId(), times, e);
            }
        }

        if (MapUtils.isEmpty(cpvModelMap)) {
            log.warn("[ProbeConfAgent] checkPVValid 查询CPV-PRC接口返回的CPV-MAP信息为空，团购分类ID={}, 会话id={}", param.getProductCategoryId(), param.getConversationId());
            return PVCheckResult.of(false, String.format("检查不通过，团购分类ID=%s无CPV信息，请重试", param.getProductCategoryId()));
        }

        // 4. 检查用户输入的点位、属性、属性值是否合法
        for (Position position : ppvdList.getPositionList()) {
            //  检查点位信息是否为空
            if (StringUtils.isBlank(position.getPosition())) {
                // 检查属性是否为空
                if (CollectionUtils.isEmpty(position.getPvdList())) {
                    return PVCheckResult.of(false, "当前未识别到点位信息，你可以这样对我说：" +
                            "\n\n>eg.我想基于线上逻辑进行修改，把排烟设备点位修改成预约点位，其他点位保持不变，请不要说第几个点位哦，因为我可能无法正确识别" +
                            "\n\n>eg.我想覆盖线上逻辑，重新配置服务部位范围、艾灸手法两个点位");
                }
                return PVCheckResult.of(false,"当前未识别到点位信息，你可以这样对我说：" +
                        "\n\n>eg.[服务部位范围]点位，属性服务部位范围bodyRegion，value1=全身，展示1=含全身；value2=局部部位，展示2=局部" +
                        "\n\n>eg.[功效]点位，属性功效massage_efficacy，当属性值为以下一个或多个时：祛湿、健脾、护肝、养气血。请按照以下优先顺序展示：祛湿 > 健脾 > 护肝 > 养气血，也就是说，优先展示的症状是列表中最前面的那个。当属性值是养肤排毒、身体放松、温经通络、暖宫调经、眼部放松、头部放松、助睡眠时不展示");
            }

            // 点位不为空，检查属性是否为空
            if (CollectionUtils.isEmpty(position.getPvdList())) {
                return PVCheckResult.of(false, String.format("请告诉我[%s]点位需要使用的属性KEY，你可以这样对我说：%s"
                        , position.getPosition(), generateDynamicExampleButNoKey(position.getPosition())));
            }

            // 点位不为空，检查属性、属性值是否为空
            for (PVD pvd : position.getPvdList()) {
                if (StringUtils.isBlank(pvd.getProperty())) {
                    return PVCheckResult.of(false, String.format("请告诉我[%s]点位需要使用的属性KEY，你可以这样对我说：%s"
                            , position.getPosition(), generateDynamicExampleButNoKey(position.getPosition())));
                }

                // 检查类目属性是否存在
                CPVModel cpvModel = cpvModelMap.get(pvd.getProperty());
                if (cpvModel == null) {
                    String propertyList = cpvModelMap.values().stream().map(p -> p.getPropertyName() + p.getProperty()).collect(Collectors.joining("、"));
                    return PVCheckResult.of(false, String.format("检查不通过，团购分类ID=%s时，属性%s不存在，可用的属性如下：%s",
                            param.getProductCategoryId(), pvd.getProperty(), propertyList));
                }
                log.info("[ProbeConfAgent]checkPVValid 查询CPV-PRC接口返回的cpvModel={}，属性={}", SerializeUtils.toJsonStr(cpvModel), pvd.getProperty());

                // 检查属性值是否存在
                if (CollectionUtils.isEmpty(cpvModel.getValue())) {
                    continue;
                }

                Set<String> userConfValueSet = pvd.getVdList().stream().filter(vd -> StringUtils.isNotEmpty(vd.getValue())).map(VD::getValue).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(cpvModel.getValue()) && CollectionUtils.isEmpty(userConfValueSet)) {
                    return PVCheckResult.of(false, String.format("[%s]点位属性%s[%s]，它有%s个枚举值，分别是%s" +
                                    "，请告诉我你想如何展示，你可以这样对我说：%s"
                            , position.getPosition()
                            , pvd.getPropertyName()
                            , pvd.getProperty()
                            , cpvModel.getValue().size()
                            , cpvModel.getValue().stream().collect(Collectors.joining("、"))
                            , generateExample(cpvModel, pvd)));
                }

                // 检查属性值是否有效
                String diffErrorMsg = diffErrorMsg(param.getProductCategoryId(), cpvModel, pvd);
                if (StringUtils.isNotBlank(diffErrorMsg)) {
                    return PVCheckResult.of(false, diffErrorMsg);
                }
            }
        }

        return PVCheckResult.of(true, "检查通过");
    }

    private String diffErrorMsg(Long productCategory, CPVModel cpvModel, PVD pvd) {
        Set<String> userConfValueSet = pvd.getVdList().stream().filter(vd -> StringUtils.isNotEmpty(vd.getValue())).map(VD::getValue).collect(Collectors.toSet());
        if (cpvModel.getValue().size() == userConfValueSet.size() && cpvModel.getValue().containsAll(userConfValueSet)) {
            return "";
        }

        return String.format("检查不通过，团购分类ID=%s时，属性%s的value值%s与输入的value值%s不一致，请按照CPV属性枚举值逐一说明展示规则，输入示例：%s",
                productCategory,
                pvd.getProperty(),
                cpvModel.getValue(),
                userConfValueSet,
                generateExample(cpvModel, pvd));
    }

    private String generateDynamicExampleButNoKey(String position) {
        StringBuilder sb = new StringBuilder()
                .append("\n\n>eg.[")
                .append(position)
                .append("]点位，取属性[")
                .append(position)
                .append("][请输入属性KEY]，但我不知道属性值是什么，请帮我查询属性的枚举值")
                .append("\n\n>eg.[")
                .append(position)
                .append("]点位，取属性[")
                .append(position)
                .append("][请输入属性KEY]，当属性值为以下一个或多个时：xxx、xxx、xxx。请按照以下优先顺序展示：xxx > xxx > xxx，也就是说，优先展示列表中最前面的那个。当属性值是 xxx、xxx、xxx 时不展示");
        return sb.toString();
    }

    private String generateExample(CPVModel cpvModel, PVD pvd) {
        List<String> values = Lists.newArrayList(cpvModel.getValue());

        //  属性值小于等于4个，不展示
        if (values.size() <= 4) {
            return generateExampleWhenValueEquals(cpvModel, pvd);
        }
        int midIndex = (values.size() + 1) / 2;
        List<String> showValueList = values.subList(0, midIndex);
        List<String> notShowValueList = values.subList(midIndex, values.size());

        StringBuilder specialExample = new StringBuilder("\n\n>eg.[")
                .append(cpvModel.getPropertyName())
                .append("] 点位，属性")
                .append(pvd.getPropertyName())
                .append(pvd.getProperty())
                .append("，当属性值为以下一个或多个时：")
                .append(showValueList.stream().collect(Collectors.joining("、")))
                .append("。请按照以下优先顺序展示：")
                .append(showValueList.stream().collect(Collectors.joining(" > ")))
                .append("，也就是说，优先展示列表中最前面的那个。");

        if (CollectionUtils.isNotEmpty(notShowValueList)) {
            specialExample.append("当属性值是 ")
                    .append(notShowValueList.stream().collect(Collectors.joining("、")))
                    .append(" 中的任意一个时，均不展示");
        }

        return specialExample.toString();
    }


    private String generateExampleWhenValueEquals(CPVModel cpvModel, PVD pvd) {
        StringBuilder specialExample = new StringBuilder("\n\n>eg.[")
                .append(cpvModel.getPropertyName())
                .append("] 点位，属性")
                .append(pvd.getPropertyName())
                .append(pvd.getProperty())
                .append("，");
        int index = 1;
        for (String value : cpvModel.getValue()) {
            specialExample.append(String.format("\n\n>value%s=%s，展示%s=%s；", index, value, index, value));
            index++;
        }

        return specialExample.toString();
    }

    /**
     * TODO 后面存 ai_operator_config_unit
     * 获取配置单元的配置规则
     *
     * @param configUnit
     * @return
     */
    private String getConfigRule(String configUnit) {
        if (ConfigUnitEnum.SHELF_SUBTITLE.getCode().equals(configUnit)) {
            return "货架副标题最多由三个点位构成，每个点位可配置一个或多个属性的展示规则。";
        }

        if (ConfigUnitEnum.TITLE_PREFIX.getCode().equals(configUnit)) {
            return "货架标题前缀可配置一个或多个属性的展示规则。";
        }

        return null;
    }

    private List<Message> getHistoryMsgs(Long conversationId) {
        return conversationStoreService.queryHistoryMessagesWithoutSystem(conversationId);
    }
}
