diff --git a/professor-application/pom.xml b/professor-application/pom.xml
index ec6eb3a..b1e612b 100644
--- a/professor-application/pom.xml
+++ b/professor-application/pom.xml
@@ -15,6 +15,15 @@
     <name>professor-application</name>
 
     <dependencies>
+        <!-- Project module -->
+        <dependency>
+            <groupId>com.sankuai.qpro.ai</groupId>
+            <artifactId>professor-api</artifactId>
+        </dependency>
+        <dependency>
+            <groupId>com.sankuai.qpro.ai</groupId>
+            <artifactId>professor-domain</artifactId>
+        </dependency>
         <dependency>
             <groupId>com.meituan.nibscp.framework</groupId>
             <artifactId>scp-cpv-api</artifactId>
@@ -27,17 +36,12 @@
                     <artifactId>validation-api</artifactId>
                     <groupId>javax.validation</groupId>
                 </exclusion>
+                <exclusion>
+                    <artifactId>mdp-boot-starter-bean-copy</artifactId>
+                    <groupId>com.meituan.mdp.boot</groupId>
+                </exclusion>
             </exclusions>
         </dependency>
-        <!-- Project module -->
-        <dependency>
-            <groupId>com.sankuai.qpro.ai</groupId>
-            <artifactId>professor-api</artifactId>
-        </dependency>
-        <dependency>
-            <groupId>com.sankuai.qpro.ai</groupId>
-            <artifactId>professor-domain</artifactId>
-        </dependency>
         <dependency>
             <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
             <artifactId>open-sdk</artifactId>
@@ -80,10 +84,6 @@
             <groupId>com.meituan.mdp.boot</groupId>
             <artifactId>mdp-boot-starter-config</artifactId>
         </dependency>
-        <dependency>
-            <groupId>org.projectlombok</groupId>
-            <artifactId>lombok</artifactId>
-        </dependency>
         <dependency>
             <groupId>com.google.code.gson</groupId>
             <artifactId>gson</artifactId>
@@ -99,14 +99,37 @@
         <dependency>
             <groupId>com.sankuai.spt</groupId>
             <artifactId>spt-ark-api</artifactId>
+            <exclusions>
+                <exclusion>
+                    <groupId>*</groupId>
+                    <artifactId>*</artifactId>
+                </exclusion>
+            </exclusions>
         </dependency>
         <dependency>
             <groupId>com.sankuai.spt</groupId>
             <artifactId>spt-ark-common</artifactId>
+            <exclusions>
+                <exclusion>
+                    <groupId>*</groupId>
+                    <artifactId>*</artifactId>
+                </exclusion>
+            </exclusions>
         </dependency>
         <dependency>
             <groupId>com.sankuai.spt</groupId>
             <artifactId>spt-gray-api</artifactId>
+            <exclusions>
+                <exclusion>
+                    <groupId>*</groupId>
+                    <artifactId>*</artifactId>
+                </exclusion>
+            </exclusions>
+        </dependency>
+        <!-- nibscp enhance api -->
+        <dependency>
+            <groupId>com.meituan.nibscp.flow</groupId>
+            <artifactId>scp-general-enhance-api</artifactId>
         </dependency>
         <dependency>
             <groupId>com.meituan.mdp.boot</groupId>
@@ -146,7 +169,6 @@
             <groupId>edu.stanford.nlp</groupId>
             <artifactId>stanford-corenlp</artifactId>
         </dependency>
-
         <!-- 单元测试 -->
         <!-- mockito-core -->
         <dependency>
@@ -154,4 +176,5 @@
             <artifactId>mockito-core</artifactId>
         </dependency>
     </dependencies>
+
 </project>
