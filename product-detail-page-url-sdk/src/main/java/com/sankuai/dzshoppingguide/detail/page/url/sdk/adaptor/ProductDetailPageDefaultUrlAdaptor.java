package com.sankuai.dzshoppingguide.detail.page.url.sdk.adaptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.detail.page.url.api.enums.ProductDetailPageUrlParamEnum;
import com.sankuai.dzshoppingguide.detail.page.url.api.request.ProductDetailPageUrlQueryRequest;
import com.sankuai.dzshoppingguide.detail.page.url.sdk.exception.ProductDetailPageUrlFatalException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 15:45
 */
@Slf4j
public class ProductDetailPageDefaultUrlAdaptor implements InitializingBean {

    private final static String DEFAULT_URL_LION_KEY = "com.sankuai.dzshoppingguide.detail.gateway.product.detail.page.default.url";

    private Map<String, String> defaultUrlMap = new HashMap<>();

    /**
     * 兜底链接
     */
    public String getDefaultUrl(final ProductDetailPageUrlQueryRequest request) {
        try {
            ProductTypeEnum productTypeEnum = ProductTypeEnum.fromCode(request.getProductType());
            ClientTypeEnum clientTypeEnum = ClientTypeEnum.fromCode(request.getClientType());
            String url = defaultUrlMap.get(buildKey(productTypeEnum, clientTypeEnum));
            if (StringUtils.isNotBlank(url)) {
                return formatUrl(url, request);
            }
            url = defaultUrlMap.get(buildDefaultKey(productTypeEnum));
            if (StringUtils.isBlank(url)) {
                throw new ProductDetailPageUrlFatalException("该商品类型没有配置最兜底链接:" + productTypeEnum.name());
            }
            return formatUrl(url, request);
        } catch (Throwable throwable) {
            log.error("ProductDetailPageUrlQueryAdaptor.getDefaultUrl,request:{}", JSON.toJSONString(request), throwable);
            throw throwable;
        }
    }

    private String buildKey(ProductTypeEnum productTypeEnum,
                            ClientTypeEnum clientTypeEnum) {
        return String.format("%s-%s", productTypeEnum.name(), clientTypeEnum.name());
    }

    private String buildDefaultKey(ProductTypeEnum productTypeEnum) {
        return String.format("%s-defaultClientType", productTypeEnum.name());
    }

    private void refreshDefaultUrlMap(String json) {
        Map<String, String> defaultUrlMap = JSON.parseObject(json, new TypeReference<Map<String, String>>() {
        });
        if (MapUtils.isEmpty(defaultUrlMap)) {
            throw new ProductDetailPageUrlFatalException("商品详情页兜底配置为空!!!");
        }
        this.defaultUrlMap = defaultUrlMap;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        refreshDefaultUrlMap(Lion.getStringValue(DEFAULT_URL_LION_KEY));
        Lion.addConfigListener(DEFAULT_URL_LION_KEY, configEvent -> refreshDefaultUrlMap(configEvent.getValue()));
    }

    private String formatUrl(String url, final ProductDetailPageUrlQueryRequest request) {
        url = url.replaceAll(ProductDetailPageUrlParamEnum.shopId.getPlaceholder(), String.valueOf(request.getPoiId()));
        url = url.replaceAll(ProductDetailPageUrlParamEnum.productId.getPlaceholder(), String.valueOf(request.getProductId()));
        url = url.replaceAll(ProductDetailPageUrlParamEnum.productType.getPlaceholder(), String.valueOf(request.getProductType()));
        url = url.replaceAll(ProductDetailPageUrlParamEnum.pageSource.getPlaceholder(), Optional.ofNullable(request.getPageSource()).orElse(""));
        url = url.replaceAll(ProductDetailPageUrlParamEnum.skuId.getPlaceholder(), String.valueOf(request.getSkuId()));
        return url;
    }

}
