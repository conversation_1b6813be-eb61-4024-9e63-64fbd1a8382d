# 产品详情页功能需求规范（基于实际实现澄清版）

## 1. 眼镜行业功能需求

### 1.1 质保信息单位属性功能

**功能描述**：为眼镜产品质保信息增加时间单位属性，支持灵活的质保期展示。

**属性字段**：
- 镜片质保数值：`lens_assure`（数值型，非必填）
- 镜框质保数值：`frame_warranty`（数值型，非必填）  
- 产品质保数值：`product_warranty`（数值型，非必填）
- 镜片质保单位：`LensQualityAssuranceUnit`（文本型，非必填）
- 镜框质保单位：`FrameQualityUnit`（文本型，非必填）
- 产品质保单位：`ProductQualityAssuranceUnit`（文本型，非必填）

**展示逻辑**：
1. **镜片/镜框质保组合展示**：
   - 当`lens_assure`和`frame_warranty`均有值时，展示格式："镜片{lens_assure}{LensQualityAssuranceUnit}质保，镜框{frame_warranty}{FrameQualityUnit}质保"
   - 单位字段为空时，默认使用"年"作为单位
   - 任一数值字段为空时，整个质保信息不展示

2. **产品质保独立展示**：
   - 当`product_warranty`有值时，展示格式："{product_warranty}{ProductQualityAssuranceUnit}质保"
   - 单位字段为空时，默认使用"年"作为单位
   - 数值字段为空时，质保信息不展示

**应用范围**：
- 镜片/镜框质保：眼镜-近视配镜、儿童配镜、老花眼镜、仅镜框；眼科-儿童普通眼镜、医学配镜、离焦镜
- 产品质保：眼镜-仅镜片、太阳眼镜、仅镜框、隐形眼镜；眼科-OK镜、离焦软镜、RGP镜、其他接触镜

### 1.2 取镜时间类型扩展功能

**功能描述**：扩展取镜时间的展示类型，支持更多样化的时间表达方式。

**属性字段**：
- 取镜时间类型：`acquire_time`（文本型，必填）
- 指定天数（原有）：`specify_days`（数值型，必填）
- 指定天数内：`specify_days2`（数值型，非必填）
- 范围最小值：`SpecifyDaysRangeMinValue`（数值型，非必填）
- 范围最大值：`SpecifyDaysRangeMaxValue`（数值型，非必填）

**展示逻辑**：
1. `acquire_time="立等可取"`：直接展示"立等可取"
2. `acquire_time="指定天数后可取"`：展示"{specify_days}天后可取"
3. `acquire_time="指定天数内可取"`：展示"{specify_days2}天内可取"
4. `acquire_time="指定天数范围可取"`：展示"{SpecifyDaysRangeMinValue} - {SpecifyDaysRangeMaxValue}天可取"

**应用范围**：眼镜-近视配镜、儿童配镜、仅镜片、老花眼镜、太阳眼镜、仅镜框、隐形眼镜；眼科-儿童普通眼镜、医学配镜、离焦镜、OK镜、离焦软镜、RGP镜、其他接触镜

### 1.3 验光操作人员多选功能

#### 1.3.1 眼镜类目验光操作人员

**功能描述**：支持验光操作人员的多选配置，并根据门店数量提供相应提示。

**属性字段**：
- 验光操作人员：`optometrist2`（数组型，支持单选/多选）
- 从业经验：`optometrist_experience`（文本型，非必填）

**展示逻辑**：
1. **单选模式**：
   - 有经验信息：展示"{optometrist2}（从业{optometrist_experience}）"
   - 无经验信息：仅展示"{optometrist2}"

2. **多选模式**：
   - 展示格式："{验光员1}/{验光员2}/...可选"
   - 不展示从业经验信息
   - 多门店且多选时，添加气泡提示："该团购多门店可用，实际验光操作人员可联系商家确认"

**应用范围**：眼镜-验光、儿童验光、近视配镜、儿童配镜、仅镜片、老花眼镜

#### 1.3.2 眼科类目验光操作人员

**功能描述**：统一使用OperatorsSelection字段，支持多选并提供门店提示。

**属性字段**：
- 操作人员选择：`OperatorsSelection`（数组型，支持单选/多选）

**展示逻辑**：
1. **单选模式**：直接展示操作人员名称
2. **多选模式**：
   - 展示格式："{操作人员1}/{操作人员2}/...可选"
   - 多门店且多选时，添加气泡提示："该团购多门店可用，实际验光操作人员可联系商家确认"

**应用范围**：眼科-儿童医学验光、角膜接触镜配镜检查、成人医学验光、儿童普通眼镜、医学配镜、离焦镜、OK镜、离焦软镜、RGP镜、其他接触镜、视觉训练

### 1.4 镜片技术字段多选功能

**功能描述**：支持镜片技术的多选配置，提供更丰富的技术描述。

**属性字段**：
- 镜片技术：`lens_technology2`（数组型，支持单选/多选）

**展示逻辑**：
1. **单选模式**：直接展示技术名称
2. **多选模式**：使用"、"分隔符连接，如"双非球面、多点近视离焦眼轴控制技术"

**应用范围**：眼镜-近视配镜、儿童配镜、仅镜片、老花眼镜、太阳眼镜

### 1.5 检查时长范围值功能

**功能描述**：支持检查项目时长的范围展示，提供更准确的时间预期。

**属性字段**：
- 视力检查时长：`VisualExamDuration2`（数值型）
- 视力检查时长最大值：`VisualExamDurationRangeMax`（数值型）
- 眼科检查时长：`Ophthalmicexaminationduration`（数值型）
- 眼科检查时长最大值：`OphthalmicExaminationDurationRangeMax`（数值型）
- 健康评估时长：`HealthAssessmentDuration`（数值型）
- 健康评估时长最大值：`HealthAssessmentDurationRangeMax`（数值型）

**展示逻辑**：
1. **范围展示**：当最小值和最大值都有且不相等时，展示"{min}-{max}分钟"
2. **单值展示**：当只有一个值或两值相等时，展示"{value}分钟"
3. **总时长计算**：按平均值计入总时长，四舍五入处理

**应用范围**：
- 视力检查：儿童医学验光、成人医学验光、近视术前检查
- 眼科检查：儿童医学验光、成人医学验光、近视术前检查、干眼检查
- 健康评估：近视术前检查

## 2. 口腔行业功能需求

### 2.1 补牙材料科普信息功能

**功能描述**：为3M树脂补牙材料提供科普信息入口和浮层展示。

**触发条件**：
- 补牙技术：`Technique="树脂补牙"`
- 材料品牌：`MaterialBrand="3M"`

**展示逻辑**：
1. 在材料型号字段后显示科普信息入口（小箭头图标）
2. 根据`MaterialModel`值匹配对应的图片和链接内容
3. 通过Lion配置`MATERIAL_COMPARISON_PIC`管理图片资源

**埋点信息**：
- 重点展示信息-明细解释入口：`b_gc_o2xm8cqh_mc`

**应用范围**：补牙、儿童补牙类目

### 2.2 服务时长范围功能

**功能描述**：支持口腔服务时长的范围展示，提供更准确的服务时间预期。

**属性字段**：
- 服务时长：`duration`（数值型）
- 服务时长最大值：`durationRangeMax`（数值型）
- 服务时长单位：`ServiceDurationUnits2`（文本型，默认"分钟"）

**展示逻辑**：
1. **范围展示**：当duration和durationRangeMax都有值且max>min时，展示"{min}-{max}{单位}"
2. **单值展示**：只有一个值时，展示"{value}{单位}"
3. **总时长计算**：
   - 只有单位一致的服务流程才计入总时长
   - 按平均值计算：(duration + durationRangeMax) / 2，四舍五入
   - 无duration的服务流程不计入总时长

**特殊处理**：指定服务流程（如术后护理指导）不展示服务时长填写框

### 2.3 套餐包含属性功能

**功能描述**：为特定口腔类目增加套餐包含内容的展示。

**属性字段**：
- 套餐包含：`PackageIncludes`（数组型，非必填，可多选）

**属性值枚举**：
- "牙周治疗"：含洗牙1次、含局部麻醉
- "窝沟封闭"：含涂氟1次

**展示逻辑**：
1. 未选择时不展示
2. 展示在重点展示信息模块
3. 支持多选，按配置的描述文案展示

## 3. 技术实现要点

### 3.1 多选字段处理模式
- 使用`List<String>`类型接收数组属性值
- 通过`CollectionUtils.isEmpty()`判断是否有值
- 单选时直接取第一个元素，多选时使用指定分隔符连接

### 3.2 时长范围处理模式
- 支持最小值和最大值两个字段
- 按平均值计算总时长，使用`Math.round()`四舍五入
- 单位一致性校验，不一致时不计算总时长

### 3.3 门店数量相关提示
- 通过`context.getAvailableShopCount()`获取可用门店数
- 多门店且多选时显示气泡提示
- 使用JSON格式构建气泡弹窗数据

### 3.4 配置化管理
- 通过Lion配置管理业务规则和资源
- 支持材料对比图片的配置化管理
- 单值数组属性通过配置列表管理
