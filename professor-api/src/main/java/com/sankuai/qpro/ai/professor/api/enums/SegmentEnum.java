package com.sankuai.qpro.ai.professor.api.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 会话环节枚举
 * <AUTHOR>
 * @date 2025/1/2
 */
public enum SegmentEnum {

    CATEGORY("category", "分类提示环节", "处理分类问题", 1),
    EXP("exp", "实验提示环节", "处理斗斛实验问题", 2),
    CONF("conf", "配置提示环节", "属性配置问题", 3),
    GENERATE("generate", "代码生成环节", "最终代码生成", 4),
    ERROR("error", "异常环节", "异常", 5);

    private final String code;
    private final String description;
    private final String tool;

    private final int order;

    private static final Map<String, SegmentEnum> CODE_MAP = Maps.newHashMap();

    static {
        for (SegmentEnum segmentEnum : SegmentEnum.values()) {
            CODE_MAP.put(segmentEnum.getCode(), segmentEnum);
        }
    }

    public static SegmentEnum getByCode(String code) {
        return CODE_MAP.get(code);
    }

    SegmentEnum(String code, String description, String tool, int order) {
        this.code = code;
        this.description = description;
        this.tool = tool;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getTool() {
        return tool;
    }
    public int getOrder() {
        return order;
    }
}
