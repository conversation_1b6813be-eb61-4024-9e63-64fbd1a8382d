# 产品详情页通用模块需求技术方案设计

## 1. 需求分析

### 1.1 业务需求解读

基于requirement.md分析，本次需求主要涉及三个业务领域的优化：

#### 1.1.1 行业通用部分
- **开店宝后台翻单引导标签**：需要在后台展示"待升级"和"待更新"标签
- **核心功能**：标签识别、hover浮层、引导文案展示

#### 1.1.2 双眼部分（眼镜/眼科行业）
- **质保信息优化**：支持"年"与"天"等单位展示
- **取镜时间类型**：支持多种时间类型（立等可取、指定天数等）
- **标题拼接逻辑**：调整为推荐前拼接模式
- **度数/折射率说明**：新增说明入口及浮层
- **镜片技术字段**：支持多选
- **验光操作人员**：调整为多选
- **数据结构优化**：翻单映射规则优化

#### 1.1.3 口腔部分
- **补牙科普信息**：3M材料型号科普信息展示
- **服务时长**：支持范围值展示
- **套餐包含**：新增属性字段
- **数据清洗**：特定场景下的服务时长清洗

### 1.2 技术要求识别

- **前端展示优化**：多种UI组件和交互逻辑
- **数据模型扩展**：新增字段和枚举类型
- **业务逻辑增强**：条件判断和数据处理
- **配置化支持**：Lion配置管理
- **兼容性保证**：向后兼容现有数据

### 1.3 与现有架构契合度分析

**高度契合点：**
- 现有模块化架构完全支持新功能扩展
- 结构化团详模块(`ModuleDetailStructuredDetailVO`)可直接承载新字段
- 现有Builder模式可快速扩展新的构建逻辑
- Lion配置服务可支持新的配置需求

**需要扩展的部分：**
- 新增行业特定的VO类和枚举
- 扩展现有Builder的业务逻辑
- 新增配置服务支持新的业务规则

## 2. 方案设计

### 2.1 整体架构设计

基于现有DDD架构，采用以下设计原则：
- **最小侵入**：充分利用现有模块和组件
- **可扩展性**：支持未来更多行业特定需求
- **配置化**：业务规则通过Lion配置管理
- **向后兼容**：保证现有功能不受影响

### 2.2 模块扩展设计

#### 2.2.1 API模块扩展

**新增VO类：**

```java
// 眼镜行业特定VO
@Data
@TypeDoc(description = "眼镜行业质保信息")
@MobileDo(id = 0x9001)
public class EyeglassWarrantyVO implements Serializable {
    @FieldDoc(description = "镜片质保天数")
    private Integer lensWarrantyDays;
    
    @FieldDoc(description = "镜框质保天数") 
    private Integer frameWarrantyDays;
    
    @FieldDoc(description = "质保单位")
    private WarrantyUnitEnum warrantyUnit;
}

// 取镜时间VO
@Data
@TypeDoc(description = "取镜时间信息")
@MobileDo(id = 0x9002)
public class PickupTimeVO implements Serializable {
    @FieldDoc(description = "取镜时间类型")
    private PickupTimeTypeEnum timeType;
    
    @FieldDoc(description = "最小天数")
    private Integer minDays;
    
    @FieldDoc(description = "最大天数")
    private Integer maxDays;
}

// 口腔科普信息VO
@Data
@TypeDoc(description = "3M材料科普信息")
@MobileDo(id = 0x9003)
public class MaterialScienceVO implements Serializable {
    @FieldDoc(description = "材料型号")
    private String materialModel;
    
    @FieldDoc(description = "科普图片URL")
    private String scienceImageUrl;
    
    @FieldDoc(description = "科普说明")
    private String scienceDescription;
}
```

**新增枚举类：**

```java
// 质保单位枚举
@Getter
public enum WarrantyUnitEnum {
    DAYS(1, "天"),
    YEARS(2, "年");
    
    private final int code;
    private final String desc;
}

// 取镜时间类型枚举
@Getter
public enum PickupTimeTypeEnum {
    IMMEDIATE(1, "立等可取"),
    AFTER_DAYS(2, "指定天数后可取"),
    WITHIN_DAYS(3, "指定天数内可取"),
    RANGE_DAYS(4, "指定天数范围可取");
    
    private final int code;
    private final String desc;
}
```

#### 2.2.2 应用服务层扩展

**新增Builder类：**

```java
@Builder(
    moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
    startFetcher = CommonModuleStarter.class,
    dependentFetchers = {
        ProductBaseInfoFetcher.class,
        ProductAttrFetcher.class,
        IndustryConfigFetcher.class
    }
)
public class IndustrySpecificStructuredDetailBuilder extends BaseBuilder<ModuleDetailStructuredDetailVO> {
    
    @Override
    public ModuleDetailStructuredDetailVO doBuild() {
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        
        // 根据行业类型选择不同的构建策略
        if (isEyeglassIndustry(baseInfo)) {
            return buildEyeglassDetails(baseInfo, productAttr);
        } else if (isDentalIndustry(baseInfo)) {
            return buildDentalDetails(baseInfo, productAttr);
        }
        
        return buildDefaultDetails(baseInfo, productAttr);
    }
}
```

**新增Fetcher类：**

```java
@ComponentFetcher(
    aggregateFetcher = QueryCenterAggregateFetcher.class
)
public class IndustryConfigFetcher extends ComponentFetcherContext<
    QueryByDealGroupIdRequestBuilder,
    QueryCenterAggregateReturnValue,
    IndustryConfig> {
    
    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder.dealGroupAttr(DealGroupAttrBuilder.builder().all());
    }
    
    @Override
    public IndustryConfig extractResult(QueryCenterAggregateReturnValue aggregateResult) {
        // 提取行业特定配置信息
        return IndustryConfigParser.parse(aggregateResult);
    }
}
```

#### 2.2.3 领域层扩展

**新增领域服务：**

```java
@Component
public class IndustrySpecificConfigService {
    
    private static final String EYEGLASS_WARRANTY_CONFIG = "eyeglass.warranty.config";
    private static final String DENTAL_MATERIAL_CONFIG = "dental.material.config";
    
    public EyeglassWarrantyConfig getEyeglassWarrantyConfig() {
        return Lion.getBean(Environment.getAppName(), EYEGLASS_WARRANTY_CONFIG, 
                           EyeglassWarrantyConfig.class);
    }
    
    public DentalMaterialConfig getDentalMaterialConfig() {
        return Lion.getBean(Environment.getAppName(), DENTAL_MATERIAL_CONFIG,
                           DentalMaterialConfig.class);
    }
}
```

### 2.3 数据流设计

```
请求 → ProductDetailPageCommonModuleSpiImpl 
     → ModuleArrangeFramework
     → IndustrySpecificStructuredDetailBuilder
     → [IndustryConfigFetcher, ProductAttrFetcher]
     → IndustrySpecificConfigService
     → Lion配置中心
     → 构建行业特定VO
     → 返回响应
```

## 3. 架构适配

### 3.1 现有服务集成

**利用现有组件：**
- `CompositeAtomServiceImpl` - 集成外部服务调用
- `QueryCenterAclService` - 商品信息查询
- `MapperCacheWrapper` - ID映射和缓存
- `ProductAttrConfigService` - 属性配置管理

**扩展现有模块：**
- 在`ModuleKeyConstants`中新增行业特定模块键
- 扩展`ProductStructuredDetailModuleFactory`支持行业选择
- 增强`LionConfigUtils`支持新的配置项

### 3.2 调用链路设计

1. **配置查询链路**：
   ```
   Builder → IndustrySpecificConfigService → Lion配置中心
   ```

2. **数据获取链路**：
   ```
   Builder → ProductAttrFetcher → QueryCenterAclService → 商品查询中心
   ```

3. **缓存链路**：
   ```
   Builder → MapperCacheWrapper → Redis缓存
   ```

## 4. 实施计划

### 4.1 第一阶段：基础架构扩展（1周）

**任务列表：**
- [ ] 新增行业特定VO类和枚举（2天）
- [ ] 扩展ModuleKeyConstants常量定义（0.5天）
- [ ] 新增IndustrySpecificConfigService（1天）
- [ ] 创建IndustryConfigFetcher（1天）
- [ ] 单元测试编写（1.5天）

**依赖关系：**
- VO类设计 → 枚举定义 → 配置服务 → Fetcher实现

### 4.2 第二阶段：业务逻辑实现（2周）

**任务列表：**
- [ ] IndustrySpecificStructuredDetailBuilder实现（3天）
- [ ] 眼镜行业特定逻辑实现（3天）
- [ ] 口腔行业特定逻辑实现（3天）
- [ ] Lion配置项配置（1天）
- [ ] 集成测试（4天）

**风险点：**
- 业务逻辑复杂度较高，需要充分理解需求
- 多选字段的前端展示逻辑需要与前端团队协调

### 4.3 第三阶段：测试和部署（1周）

**任务列表：**
- [ ] 完整功能测试（2天）
- [ ] 性能测试（1天）
- [ ] 灰度发布（2天）
- [ ] 全量发布（2天）

## 5. 技术细节

### 5.1 关键接口设计

```java
public interface IndustrySpecificProcessor {
    /**
     * 处理行业特定的结构化详情
     */
    List<DealDetailStructuredDetailVO> processIndustryDetails(
        ProductBaseInfo baseInfo, 
        ProductAttr productAttr,
        IndustryConfig config
    );
    
    /**
     * 判断是否支持该行业
     */
    boolean supports(int categoryId);
}
```

### 5.2 配置模型设计

```java
@Data
public class EyeglassWarrantyConfig {
    private Map<String, WarrantyRule> warrantyRules;
    private List<PickupTimeRule> pickupTimeRules;
    private Map<String, String> lensDescriptions;
}

@Data
public class DentalMaterialConfig {
    private Map<String, MaterialInfo> materialInfoMap;
    private List<ServiceDurationRule> durationRules;
    private Map<String, String> packageIncludes;
}
```

### 5.3 集成测试策略

**测试覆盖范围：**
- 各行业特定逻辑的正确性
- 配置变更的实时生效
- 向后兼容性验证
- 性能影响评估

**测试环境：**
- 单元测试：本地开发环境
- 集成测试：test环境
- 性能测试：staging环境
- 用户验收测试：prod环境灰度

### 5.4 部署考虑

**配置管理：**
- 所有业务规则通过Lion配置管理
- 支持动态配置更新，无需重启服务
- 配置版本控制和回滚机制

**监控告警：**
- 新增业务指标监控
- 异常情况告警机制
- 性能指标跟踪

**兼容性保证：**
- 新字段设置默认值
- 渐进式功能发布
- 降级方案准备

## 6. 风险评估与应对

### 6.1 技术风险

**风险点：**
- 多选字段的数据结构变更可能影响现有功能
- 新增配置项过多可能影响系统性能

**应对措施：**
- 充分的向后兼容性测试
- 配置缓存优化
- 分阶段发布策略

### 6.2 业务风险

**风险点：**
- 行业特定逻辑复杂，容易出现理解偏差
- 前后端协调成本较高

**应对措施：**
- 详细的需求评审和确认
- 完善的文档和示例
- 密切的跨团队协作

通过以上技术方案，可以在现有架构基础上高效地实现所有需求功能，同时保证系统的稳定性和可扩展性。
