## 业务范围

本需求聚焦于美团及点评APP中，按摩足疗、美发、教育培训三大类目的“团购详情页”，具体针对“团购次卡”商品，接入“先用后付”能力。目标是实现团购次卡在满足商品和用户条件下，详情页及相关交易链路的“先用后付”标签、提示、交互展示，并支持实验平台灰度。

---

## 需求内容

- 在团购详情页（仅团购次卡，限按摩足疗、美发、教育培训类目）增加“先用后付”标签及相关文案、交互。

- 展示需同时满足：

    - 商品维度：该团购次卡支持先用后付（pay_method=4）。

    - 用户维度：当前用户可使用先用后付（通过BNPLExposureRequest接口判断）。

- DealGroupPBO对象新增creditPay布尔属性，用于标识当前是否可展示“先用后付”。

- 接入实验平台douhu，实验号分别为EXP2025032100002（线下）、EXP2025032100003（线上），实验标识MtCreditPayExp。

- 需根据Lion配置灵活控制功能开关、类目白名单及用户查询入参。

---

## 功能点详细描述

1. **团购详情页展示逻辑**

    - 仅在团购次卡商品（按摩足疗、美发、教育培训）详情页，且商品支持先用后付、用户可用时，展示“先用后付”标签。

    - 若关联单次团购存在次卡且支持先用后付，也需在规格小卡片上打标。

2. **实验组/对照组**

    - 实验组c：团购详情页展示“先用后付”标签和提示条。

    - 对照组：不展示相关内容。

3. **DealGroupPBO对象扩展**

    - 新增creditPay布尔属性，前端据此判断是否展示“先用后付”相关UI。

4. **配置化能力**

    - Lion配置全局开关控制功能启停。

    - Lion配置控制支持的二级分类白名单，仅在指定类目开放。

    - Lion配置动态提供用户查询接口入参（如bizId、planId、signIphPayMerchantNo等信息）。

---

## 项目范围

- **代码仓库**：[tuangou/mapi-dztgdetail-web](

- **appkey**：com.sankuai.dzu.tpbase.dztgdetailweb

- **主入口接口**：DzDealBaseAction#execute（团购详情主接口）

- **实验平台**：douhu，实验号EXP2025032100002/EXP2025032100003，实验标识MtCreditPayExp

- **适用端**：美团APP、点评APP

---

## 依赖信息详细说明

### 依赖接口1：商品先用后付属性获取

- **目标**：判断商品是否支持先用后付

- **接口**：com.sankuai.general.product.query.center.client.service.DealGroupQueryService#queryByDealGroupIds

- **调用方式**：

    - 入参需带上dealGroupIds及attrsByKey(AttrSubjectEnum.DEAL_GROUP, "pay_method")

    - 出参DealGroupDTO.attrs["pay_method"]为4时表示支持

- **说明**：无接口改动，仅需正确取值和解析

### 依赖接口2：到综次卡查询接口（用户维度“先用后付”能力）

- **作用**：判断当前用户是否具备BNPL（次卡先用后付）能力

- **提供方**：到综次卡

- **接口文档**：

    - [到综次卡传参沟通](到综次卡传参沟通

    - [到综次卡BNPL外部接口文档](4.1 聚合曝光建议

- **调用方式**：

    - maven依赖引入bnpl-client

    - spring bean配置ThriftClientProxy，远程服务为com.sankuai.fincreditpay.bnpl.client.access.thrift.IBNPLAccessThriftService

    - 方法：exposure

    - 入参：BNPLExposureRequest（含exposureScenario、userId、productSignInfo、order、deviceInfo等）

        - exposureScenario.bizIdFieldMeaning=PLAN_ID

        - subBizScenario按行业分别传BeauCard/EduCard/SpaCard

        - deviceInfo.app区分端类型（group=美团APP，dianping-nova=点评APP）

        - 详细字段见接口文档

    - 出参：BNPLExposureResponse

        - status: SUCCESS/FAIL/UNKNOWN

        - data.exposure: EXPOSED/UNEXPOSED

        - data.exposureProductList: 若exposureProduct=delaypay且exposure=EXPOSED，则用户可展示“先用后付”

- **实现要点**：

    - 调用exposure方法，解析返回的exposureProductList，判断是否可展示“先用后付”

    - 行业区分、端类型区分需准确传递

    - 严格按照文档构造入参

- **说明**：需根据Lion配置动态传递参数

### 依赖配置项

1. **功能总开关**

    - Lion key: com.sankuai.dzu.tpbase.dztgdetailweb.product.credit.pay.switch（boolean）

2. **类目白名单**

    - Lion key: com.sankuai.dzu.tpbase.dztgdetailweb.product.credit.pay.category（List<Integer>）

3. **用户查询入参配置**

    - Lion key: deal.shelf.user.credit.pay.config（Map<String, String>），如{"bizId":"10296","planId":"10296","signIphPayMerchantNo":"123"}

---

## 需求总结

本需求旨在实现团购次卡在按摩足疗、美发、教育培训三大类目下的“先用后付”能力。开发需在团购详情主接口DzDealBaseAction#execute中，基于商品属性和用户能力双重校验，通过调用DealGroupQueryService和IBNPLAccessThriftService两个外部接口，并结合Lion配置项，实现对“先用后付”标签与文案的动态展示。涉及页面包括团购详情页、提单页、订单详情页等多处链路。所有功能需接入实验平台做灰度/AB实验。开发需确保所有依赖接口和配置项正确调用和解析，并严格按照文档中的业务场景和交互细节实现前端展示与交互。