package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
public class CouponPromoItemVO implements Serializable {

    /**
     * 商品id
     */
    @MobileDo.MobileField
    private long productId;

    /**
     * 券button文案
     */
    @MobileDo.MobileField
    private String couponButtonText;

    /**
     * 神会员点位
     */
    @MobileDo.MobileField
    private String position;

    /**
     * 券类型，1-神会员券，2-免费神券
     */
    @MobileDo.MobileField
    private int couponType;

    /**
     * 券标签
     */
    @MobileDo.MobileField
    private String couponTag;

    /**
     * 页面来源
     */
    @MobileDo.MobileField
    private int pageSource;

    /**
     * 膨胀参数透传
     */
    @MobileDo.MobileField
    private String bizToken;

    /**
     * 膨胀前门槛（分）
     */
    @MobileDo.MobileField
    private int requiredAmount;

    /**
     * 膨胀前金额（分）
     */
    @MobileDo.MobileField
    private int reduceAmount;

    /**
     * 资产类型
     */
    @MobileDo.MobileField
    private int assetType;

    /**
     * 券描述
     */
    @MobileDo.MobileField
    private String couponDesc;

    /**
     * 券logo图标
     */
    @MobileDo.MobileField
    private String logoIcon;

    /**
     * 是否已膨胀，1：已膨胀，2：未膨胀
     */
    @MobileDo.MobileField
    private int inflatedStatus;

    /**
     * 是否可膨胀，1：可膨胀，2：不可膨胀
     */
    @MobileDo.MobileField
    private int canInflate;

    /**
     * 券id
     */
    @MobileDo.MobileField
    private String couponCode;

    /**
     * 券批次id
     */
    @MobileDo.MobileField
    private String applyId;

    @MobileDo.MobileField
    private String couponChannel;

    /**
     * 线下码id
     */
    @MobileDo.MobileField
    private String offlineCode;

}